<template>
  <div class="page" style="user-select:none;">

    <div class="box">

      <!-- 表内容 -->
      <div class="from">
        <div class="listButton">
          查看历史记录
        </div>
        <div class="typeTitle">
          <span></span>
          请填写需要特别关注对象姓名
        </div>
        <div class="text">
          <a-input placeholder="请输入姓名" style="width: 200px;" />
        </div>

        <div class="typeTitle">
          <span></span>
          学习情况
        </div>
        <div class="text">
          <a-textarea v-model="value" :auto-size="{ minRows: 3, maxRows: 5 }" placeholder="成果、不足、学习氛围如何、影响学习的因素" />
        </div>
        <div class="typeTitle">
          <span></span>
          生活情况
        </div>
        <div class="text">
          <a-textarea v-model="value" :auto-size="{ minRows: 3, maxRows: 5 }"
            placeholder="是否有规律、生活习惯、生活状态积极或消极、如何更好;" />
        </div>
        <div class="typeTitle">
          <span></span>
          人际关系
        </div>
        <div class="text">
          <a-textarea v-model="value" :auto-size="{ minRows: 3, maxRows: 5 }"
            placeholder="同学中是否出现人际冲突、能否自我调节、是否需要他人帮助;" />
        </div>
        <div class="typeTitle">
          <span></span>
          情感交流
        </div>
        <div class="text">
          <a-textarea v-model="value" :auto-size="{ minRows: 3, maxRows: 5 }"
            placeholder="同学中是否出现情感问题、能否自我调节、是否需要他人帮助;" />
        </div>
        <div class="typeTitle">
          <span></span>
          娱乐导向
        </div>
        <div class="text">
          <a-textarea v-model="value" :auto-size="{ minRows: 3, maxRows: 5 }" placeholder="班级同学主要娱乐活动及项目;" />
        </div>
        <div class="typeTitle">
          <span></span>
          综合评价
          <a-radio-group name="radioGroup" :default-value="1" style="margin-left: 20px;">
            <a-radio :value="1">
              优
            </a-radio>
            <a-radio :value="2">
              良
            </a-radio>
            <a-radio :value="3">
              中
            </a-radio>
            <a-radio :value="4">
              差
            </a-radio>
          </a-radio-group>
        </div>
        <div class="text">
          <a-textarea v-model="value" :auto-size="{ minRows: 3, maxRows: 5 }" placeholder="班级心理健康总体情况;" />
        </div>
        <div class="typeTitle">
          <span></span>
          班级本周开展哪些心理健康教育活动
        </div>
        <div class="text">
          <a-textarea v-model="value" :auto-size="{ minRows: 3, maxRows: 5 }" placeholder="班级本周开展哪些心理健康教育活动;" />
        </div>
        <div class="typeTitle">
          <span></span>
          你的工作希望获得哪些协助
        </div>
        <div class="text">
          <a-textarea v-model="value" :auto-size="{ minRows: 3, maxRows: 5 }" placeholder="你的工作希望获得哪些协助;" />
        </div>
        <div class="agree">
          备注: 各班辅导员或心理委员须如实填写各项内容，并注意做好保密工作。
        </div>
        <div class="button">
          提交
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'from',
  components: {},
  data() {
    const options = [
      { label: '学习困难', value: '学习困难' },
      { label: '人际关系', value: '人际关系' },
      { label: '适应', value: '适应' },
    ];
    return {
      value2: '',
      options,
      type: '',
      timeIndex: '0',
      timelist: ['09：00-10:00', '10：00-11:00'],
      list: [{
        name: '今天',
        time: '06-05'
      }, {
        name: '明天',
        time: '06-05'
      }, {
        name: '后天',
        time: '06-05'
      }, {
        name: '周二',
        time: '06-05'
      }, {
        name: '周三',
        time: '06-05'
      }, {
        name: '周四',
        time: '06-05'
      }, {
        name: '周五',
        time: '06-05'
      }],
      timeList: [],
      zhouIndex: '0'
    }
  },
  filters: {

  },
  created() {

  },
  methods: {
    // 获取时间组
    getTime() {

    },
    // 获取周几
    getWeek(dateStr) {
      let weeks = {
        "0": '星期日',
        "1": '星期一',
        "2": '星期二',
        "3": '星期三',
        "4": '星期四',
        "5": '星期五',
        "6": '星期六',
      }
      let date = new Date(dateStr);
      let weekIndex = date.getDay();
      date = weeks[weekIndex];
      return date
    },

    // 获取当前时间，day为number，getDay(-1):昨天的日期;getDay(0):今天的日期;getDay(1):明天的日期;【以此类推】
    getDay(day) {
      var today = new Date();
      var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
      today.setTime(targetday_milliseconds); //注意，这行是关键代码

      var tYear = today.getFullYear();
      var tMonth = today.getMonth();
      var tDate = today.getDate();
      tMonth = this.doHandleMonth(tMonth + 1);
      tDate = this.doHandleMonth(tDate);
      return tYear + "-" + tMonth + "-" + tDate;
    },
    doHandleMonth(month) {
      var m = month;
      if (month.toString().length == 1) {
        m = "0" + month;
      }
      return m;
    },
    getChoose(e) {
      this.type = e.target.value
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  padding: 10px 30px;
}

.from {
  border-radius: 16px;
  background: #fff;
  padding: 34px 36px;
  margin-top: 20px;
  position: relative;

  .title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
  }

  .mess {
    font-size: 16px;
    color: #666666;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .typeTitle {
    display: flex;
    align-items: center;
    height: 28px;
    font-size: 16px;
    color: #333;
    margin-top: 20px;

    span {
      display: inline-block;
      width: 3px;
      height: 14px;
      background: #3EA6FF;
      margin: 0 10px;
    }
  }

  .yuyue {
    margin-top: 20px;
    margin-bottom: 20px;
  }

}

.chilTitle {
  text-align: center;
  margin-right: 440px;
  margin-top: 20px;
  margin-bottom: 10px;
}

.zhou {
  display: flex;
  align-items: center;
  justify-content: center;

  >div {
    text-align: center;
    width: 50px;
    height: 50px;
    background: #EAEAEA;
    border-radius: 5px;
    margin-right: 20px;
    font-size: 14px;
  }

  .isChoose {
    background: #E2F2FF;
    color: #3EA6FF;

  }

  .isActive {
    border: 1px dashed #3EA6FF;
  }
}

.time {
  display: flex;
  align-items: center;
  justify-content: center;

  >div {
    text-align: center;
    width: 211px;
    height: 38px;
    background: #EAEAEA;
    border-radius: 5px;
    margin-right: 35px;
    font-size: 14px;
    line-height: 38px;
  }

  .isActive {
    border: 1px dashed #3EA6FF;
    background: #E2F2FF;
    color: #3EA6FF;
  }
}

.text {
  padding: 20px 0;
  margin: 0 20px;
  padding-bottom: 0;
}

.radioTitle {
  margin: 20px 0;
  margin-left: 20px;
  font-size: 14px;
  color: #666666;
}

.agree {
  margin-left: 20px;
  color: #939393;
  font-size: 12px;
  margin-top: 20px;

  span {
    color: #0486FE;
  }
}

.button {
  width: 197px;
  height: 38px;
  background: #35A2FF;
  border-radius: 19px;
  text-align: center;
  line-height: 38px;
  color: #FFFFFF;
  font-size: 14px;
  margin: 30px auto;
}

.listButton {
  width: 164px;
  height: 38px;
  background: linear-gradient(127deg, #3FC4FF 0%, #35A2FF 100%);
  border-radius: 19px;
  text-align: center;
  line-height: 38px;
  color: #fff;
  position: absolute;
  cursor: pointer;
  right: 60px;
}
</style>
