<template>
  <div class="page" style="user-select:none;">
    <div class="box">
      <div class="boxLeft">
        <div class="leftTop">
          <div class="tab">
            <div class="tabLeft">
              擅长领域：
            </div>
            <div class="tabRight">
              <div :class="lingyuIndex == index ? 'active' : ''" v-for="(i, index) in lingyuList" :key="index"
                @click="getChange(index, 'lingyuIndex')">
                {{ i }}
              </div>
            </div>
          </div>
          <div class="tab">
            <div class="tabLeft">
              咨询方式：
            </div>
            <div class="tabRight">
              <div :class="zixunIndex == index ? 'active' : ''" v-for="(i, index) in zixunList" :key="index"
                @click="getChange(index, 'zixunIndex')">
                {{ i }}
              </div>
            </div>
          </div>
        </div>
        <div class="leftBottom">
          <div class="titile">
            <div class="leftTitle">
              <span></span>
              心理咨询师
            </div>
            <a-input-search placeholder="搜索咨询师名字" style="width: 200px" @search="onSearch" />
          </div>
          <div class="list">
            <div class="listInfo">
              <img src="@/assets/img/img.png" alt="">
              <div class="infoCenter">
                <div class="name">
                  孙月霞
                </div>
                <div class="tag">
                  <div>
                    国家二级咨询师
                  </div>
                  <div>
                    国家二级咨询师
                  </div>
                </div>
                <div class="introduce">
                  擅长：青少年心理问题，婚姻家庭、情感问题、人际沟通、压力管理和自我成长的咨询与辅导。
                </div>
              </div>
              <div class="infoButton">
                <div>
                  私信
                </div>
                <div @click="getRouter">
                  咨询
                </div>
              </div>
            </div>
            <div class="listInfo">
              <img src="@/assets/img/img.png" alt="">
              <div class="infoCenter">
                <div class="name">
                  孙月霞
                </div>
                <div class="tag">
                  <div>
                    国家二级咨询师
                  </div>
                  <div>
                    国家二级咨询师
                  </div>
                </div>
                <div class="introduce">
                  擅长：青少年心理问题，婚姻家庭、情感问题、人际沟通、压力管理和自我成长的咨询与辅导。
                </div>
              </div>
              <div class="infoButton">
                <div>
                  私信
                </div>
                <div @click="getRouter">
                  咨询
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="boxRight">
        <div class="top">
          <div class="titile">
            <div class="leftTitle">
              <span></span>
              预约历史
            </div>
            <div>
              更多 >
            </div>
          </div>
          <div class="history">
            <div class="historyInfo">
              <div class="name">
                <div class="left">
                  咨询师：孙月霞
                </div>
                <div class="right queren">
                  <span></span>
                  待确认
                </div>
              </div>
              <div class="name">
                <div class="left">
                  咨询方式：语音咨询
                </div>
              </div>
              <div class="name">
                <div class="left">
                  预约日期：2023/10/24
                </div>
              </div>
              <div class="name">
                <div class="left">
                  预约时间：12:00-13:00
                </div>
              </div>
              <div class="button">
                取消预约
              </div>
            </div>
            <div class="historyInfo">
              <div class="name">
                <div class="left">
                  咨询师：孙月霞
                </div>
                <div class="right wancheng">
                  <span></span>
                  待确认
                </div>
              </div>
              <div class="name">
                <div class="left">
                  咨询方式：语音咨询
                </div>
              </div>
              <div class="name">
                <div class="left">
                  预约日期：2023/10/24
                </div>
              </div>
              <div class="name">
                <div class="left">
                  预约时间：12:00-13:00
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="titile">
            <div class="leftTitle">
              <span></span>
              咨询指南
            </div>
          </div>
          <div class="bottomInfo">
            <div v-for="(i, index) in list" :key="index">
              <img :src="i.url" alt="">
              {{ i.mess }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EducationHome',
  components: {
  },
  data() {
    return {
      lingyuIndex: '0',
      lingyuList: ['全部', '亲子教育', '人际关系', '个人成长', '学业职场', '情绪压力', '婚姻恋爱', '家庭困扰', '心理健康'],
      zixunIndex: '0',
      zixunList: ['全部', '视频咨询', '语音咨询', '面对面咨询'],
      list: [{
        url: require('@/assets/image/zixun.png'),
        mess: '我需要心理咨询吗？'
      }, {
        url: require('@/assets/image/zixun1.png'),
        mess: '如何选择咨询师？'
      }, {
        url: require('@/assets/image/zixun2.png'),
        mess: '第一次咨询会发生什么？'
      }]
    }
  },
  filters: {

  },
  created() {

  },
  methods: {
    getChange(index, type) {
      this[type] = index
    },
    // 路由跳转
    getRouter() {
      this.$router.push({ path: '/appointment/detail' });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  display: flex;
  align-items: flex-start;

  .top {
    padding: 21px 24px;
    background: #FFFFFF;
    border-radius: 16px;
  }

  .bottom {
    margin-top: 20px;
    padding: 21px 24px;
    background: #FFFFFF;
    border-radius: 16px;

    .bottomInfo {
      border-top: 1px dashed #D5D5D5;
      margin-top: 20px;

      div {
        display: flex;
        align-items: center;
        border-radius: 2px;
        font-size: 14px;
        color: #333333;
        height: 48px;
        margin-top: 20px;

        img {
          margin-left: 50px;
          margin-right: 10px;
        }
      }

      div:nth-child(1) {
        background: rgba(125, 171, 250, 0.09);
      }

      div:nth-child(2) {
        background: rgba(250, 139, 125, 0.09);
      }

      div:nth-child(3) {
        background: rgba(255, 191, 95, 0.09);
      }
    }
  }

  .list {
    margin-top: 28px;

    .listInfo {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      img {
        width: 82px;
        height: 108px;
      }

      .infoButton {
        display: flex;
        align-items: center;

        div {
          width: 62px;
          height: 28px;
          border-radius: 14px;
          border: 1px solid #148EFE;
          text-align: center;
          line-height: 26px;
          font-size: 14px;
          color: #188FFE;
          margin-left: 20px;
          cursor: pointer;
        }
      }

      .infoCenter {
        flex: 1;
        margin-left: 20px;

        .name {
          font-size: 16px;
          color: #333333;
          margin-bottom: 15px;
        }

        .tag {
          display: flex;
          align-items: center;
          margin-bottom: 15px;

          div {
            padding: 6px 10px;
            background: rgba(176, 176, 176, 0.14);
            border-radius: 2px;
            font-size: 14px;
            color: #666666;
            margin-right: 20px;
          }
        }

        .introduce {
          font-size: 14px;
          color: #666666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

        }
      }
    }
  }

  .tab {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .tabLeft {
      font-size: 16px;
      color: #333333;
    }
  }

  .leftTop {
    border-bottom: 1px solid #EBEBEB
  }

  .leftBottom {
    margin-top: 20px;


  }

  .tabRight {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: 10px;

    div {
      margin-right: 30px;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
    }

    .active {
      color: #0486FE;
    }
  }

  .boxLeft {
    flex: 1;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 30px 26px;
  }

  .boxRight {
    width: 320px;
    border-radius: 16px;
    margin-left: 20px;
  }
}

.titile {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .leftTitle {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333333;

    span {
      display: inline-block;
      width: 4px;
      height: 20px;
      background: #0486FE;
      margin-right: 13px;
    }
  }
}

.historyInfo {
  border-top: 1px dashed #D5D5D5;
  margin-top: 20px;
  padding-top: 20px;

  .button {
    margin: 20px auto;
    width: 100px;
    height: 28px;
    border-radius: 14px;
    border: 1px solid #D5D5D5;
    text-align: center;
    line-height: 26px;
    font-size: 14px;
    color: #999999;
    cursor: pointer;
  }

  .name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
  }

  .right {
    font-size: 14px;
    display: flex;
    align-items: center;
  }

  .queren {
    color: #35A51B;

    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #35A51B;
      border-radius: 50%;
      margin-right: 5px;
    }
  }

  .wancheng {
    color: #999999;

    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #999999;
      border-radius: 50%;
      margin-right: 5px;
    }
  }
}
</style>
