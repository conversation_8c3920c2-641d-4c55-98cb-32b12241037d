<template>
  <div class="page" style="user-select:none;">
    <div class="box" >
          <div class="zixun">
            <img class="bg" src="@/assets/image/zixunbg.png" alt="">
            <div class="top">
              <img class="logo" src="@/assets/img/img.png" alt="">
              <div class="left">
                <div class="leftTop">
                  <div class="name">
                    孙月霞
                  </div>
                  <div class="sixin">
                    <a-icon type="mail" />
                    私信
                  </div>
                </div>
                <div class="zhicheng">
                  国家二级咨询师
                </div>
                <div class="leftBottom">
                  <div>
                    <div>
                      执业年限
                    </div>
                    <div>
                      <span>3</span>年
                    </div>
                  </div>
                  <div>
                    <div>
                      服务人次
                    </div>
                    <div>
                      <span>3</span>人
                    </div>
                  </div>
                  <div>
                    <div>
                      所在地
                    </div>
                    <div>
                      <span>山东</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="left">
                <div>
                  <div class="title">
                    <img src="@/assets/image/zizhi.png" alt="">
                    个人资质
                  </div>
                  <div class="info">
                    国家二级咨询师
                  </div>
                </div>
                <div>
                  <div class="title">
                    <img src="@/assets/image/zixunduixiang.png" alt="">
                    咨询对象
                  </div>
                  <div class="info">
                    无
                  </div>
                </div>
                <div>
                  <div class="title">
                    <img src="@/assets/image/shanchang.png" alt="">
                    擅长领域
                  </div>
                  <div class="info">
                    青少年学习、婚姻家庭、亲子关系、职场压力
                  </div>
                </div>
              </div>
              <div class="line"></div>
              <div class="left">
                <div>
                  <div class="title">
                    <img src="@/assets/image/shouxun.png" alt="">
                    受训及工作经历
                  </div>
                  <div class="info">
                    无
                  </div>
                </div>
                <div>
                  <div class="title">
                    <img src="@/assets/image/yuyue.png" alt="">
                    咨询须知
                  </div>
                  <div class="info">
                    1.回应时长：我将在收到订单后24小时内回复是否接受咨询，并通过私信或电
                    话与来访者协商咨询时间、地点。
                    2.变更预约：若因特殊原因需变更/取消已协商好的咨询预约，请务必提前4小
                    时联络我，否则咨询将如期开始。
                  </div>
                </div>
              </div>
            </div>
            <div class="button" @click="getRouter">
              预约
            </div>
          </div>
      </div>
    </div>
</template>

<script>
export default {
  name: 'detail',
  components: {
  },
  data() {
    return {
      type: '2', // 1是预约详情页面  2是心理咨询预约登记表
    }
  },
  filters: {

  },
  created() {

  },
  methods: {
    // 路由跳转
    getRouter() {
      this.$router.push({ path: '/appointment/from' });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  padding: 10px 30px;
}
.button {
  margin: 0 auto;
  width: 197px;
  height: 38px;
  background: #35A2FF;
  border-radius: 19px;
  text-align: center;
  line-height: 38px;
  color: #fff;
  font-size: 14px;
  margin-top: 80px;
  cursor: pointer;
}
.zixun {
  background: #FFFFFF;
  border-radius: 16px;
  margin-top: 10px;
  position: relative;
  padding-bottom: 60px;

  .bg {
    width: 100%;
    position: absolute;

  }

  .top {
    display: flex;
    align-items: flex-start;
    z-index: 100;
    position: relative;
    top: 75px;

    .logo {
      width: 132px;
      height: 174px;
      margin-left: 48px;
    }

    .leftTop,
    .leftBottom {
      display: flex;
      align-items: center;
    }

    .left {
      margin-left: 17px;

      .zhicheng {
        color: #FFFFFF;
        font-size: 16px;
        margin-bottom: 30px;
        margin-top: 5px;
      }

      .leftBottom {
        >div {
          margin-right: 50px;

          div:nth-child(1) {
            font-size: 14px;
            color: #333333;
          }

          div:nth-child(2) {
            color: #666666;
            font-size: 14px;

            span {
              font-weight: bold;
              font-size: 28px;
              color: #0486FE;
              margin-right: 5px;
            }
          }
        }
      }
    }

    .leftTop {
      .name {
        font-size: 26px;
        color: #FFFFFF;
        font-weight: bold;
      }

      .sixin {
        color: #FFFFFF;
        width: 78px;
        height: 29px;
        border-radius: 17px;
        border: 1px solid #FFFFFF;
        text-align: center;
        line-height: 27px;
        margin-left: 22px;
        cursor: pointer;
      }
    }
  }

  .bottom {
    margin-top: 100px;
    margin-left: 48px;
    display: flex;
    align-items: center;

    

    .left {
      width: 48%;
    }

    .line {
      width: 1px;
      height: 230px;
      border: 1px solid rgba(151, 151, 151, 0.15);
      margin-right: 30px;
    }

    .title {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333;
      margin-bottom: 20px;

      img {
        width: 36px;
        margin-right: 15px;
      }
    }

    .info {
      font-size: 14px;
      margin-bottom: 20px;
      color: #999;
    }
  }
}
</style>
