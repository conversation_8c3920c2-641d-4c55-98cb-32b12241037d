<template>
  <div class="page" style="user-select:none;">
    <div class="box">
      <div class="boxLeft">
        <div class="top">
          <div class="left">
            <img src="@/assets/img/img.png" alt="">
            晴天
          </div>
          <div class="time">
            2024-02-16 14:09:00
          </div>
        </div>
        <div class="mess">
          <div class="title">
            睡不着怎么办
          </div>
          <div class="content">
            <img style="width: 36px;height: 36px;border-radius:50%" src="@/assets/img/img.png" alt="">
            睡不着怎么办
          </div>
        </div>
        <div class="pinglun">
          <div>
            <img src="@/assets/image/pinglun.png" alt="">
            评论
          </div>
          <div class="zan">
            <img src="@/assets/image/zan.png" alt="">
            点赞
          </div>
          <div class="zan">
            <img src="@/assets/image/delete.png" alt="">
            删除
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EducationHome',
  components: {
  },
  data() {
    return {
      list: [{
        num: '111',
        name: '提问数'
      }, {
        num: '111',
        name: '评论数'
      }, {
        num: '111',
        name: '点赞数'
      }],
      infoList: [{
        title: '睡不着怎么办',
        content: '最近学习压力太大了，一直睡不着，想问下',
        time: '2024-02-16 14:09:00'
      }, {
        title: '睡不着怎么办',
        content: '最近学习压力太大了，一直睡不着，想问下',
        time: '2024-02-16 14:09:00'
      }, {
        title: '睡不着怎么办',
        content: '最近学习压力太大了，一直睡不着，想问下',
        time: '2024-02-16 14:09:00'
      }]
    }
  },
  filters: {

  },
  created() {

  },
  methods: {
    // 路由跳转
    getRouter() {
      this.$router.push({ path: '/appointment/detail' });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  .bottombox {
    margin-top: 20px;
    padding: 21px 24px;
    background: #FFFFFF;
    border-radius: 16px;

    .bottomInfo {
      margin-top: 20px;

      .info {
        border-bottom: 1px dashed #D5D5D5;
        padding-bottom: 14px;
        margin-bottom: 20px;

        .title {
          color: #333333;
          font-size: 16px;
          font-weight: bold;
        }

        .content {
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          margin: 11px 0;
        }

        .time {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.5);
          line-height: 20px;
        }
      }

      .info:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }
    }
  }



  .leftTop {
    border-bottom: 1px solid #EBEBEB
  }

  .leftBottom {
    margin-top: 20px;


  }

  .tabRight {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: 10px;

    div {
      margin-right: 30px;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
    }

    .active {
      color: #0486FE;
    }
  }

  .boxLeft {
    background: #FFFFFF;
    border-radius: 16px;
    padding: 30px 26px;
    
    .pinglunList {
      margin-top: 20px;

      .list {
        border: 1px solid #DBDBDB;
        margin-top: 20px;

        .child {
          margin-left: 50px;
        }

        .pinglunInfo {
          .bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;

            margin-left: 70px;
            margin-right: 28px;
            margin-bottom: 20px;
          }

          .pinglun {
            margin-top: 0;

            >div {
              img {
                width: 24px;
                height: 24px;
              }
            }

            .zan {
              margin-left: 20px;
            }
          }

          .actiLogo {
            display: flex;
            align-items: center;
            font-size: 16px;
            color: #000000;
            margin-left: 22px;
            margin-top: 20px;

            img {
              width: 38px;
              height: 38px;
              border-radius: 50%;
              margin-right: 10px;
            }
          }

          .content {
            font-size: 14px;
            color: #666666;
            margin: 15px 0;
            margin-left: 70px;
          }
        }

        .title {
          border-bottom: 1px solid #DBDBDB;
          font-size: 14px;
          color: #000000;
          padding-left: 22px;
          margin-top: 10px;
          padding-bottom: 10px;
        }
      }

      .send {
        display: flex;
        align-items: center;

        img {
          width: 44px;
          height: 44px;
          border-radius: 50%;
          margin-right: 20px;
        }
      }
    }

    .mess {
      .title {
        font-size: 16px;
        color: #333333;
        font-weight: 600;
        line-height: 22px;
      }

      .content {
        font-size: 14px;
        color: #666666;
        margin: 10px 0;
      }

      .pic {
        img {
          width: 420px;
          height: 202px;
        }
      }
    }

    .pinglun {
      display: flex;
      align-items: center;
      margin-top: 20px;

      >div {
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.5);
        font-size: 14px;

        img {
          width: 24px;
          height: 24px;
          margin-right: 10px;
        }
      }

      .zan {
        margin-left: 20px;
      }
    }

    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .time {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.5);
      }

      .left {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #000000;

        img {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          margin-right: 16px;
        }
      }
    }
  }

  .boxRight {
    width: 320px;
    border-radius: 16px;
    margin-left: 20px;
  }
}

.titile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px dashed #D5D5D5;
  padding-bottom: 20px;

  .leftTitle {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333333;

    span {
      display: inline-block;
      width: 4px;
      height: 20px;
      background: #0486FE;
      margin-right: 13px;
    }
  }
}

.tongji {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 10px;

  div {
    display: flex;
    align-items: center;
    flex-direction: column;

    .num {
      font-size: 28px;
      color: #35A2FF;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .name {
      color: #333333;
      font-size: 16px;
    }
  }
}

.sanjiao {
  display: inline-block;
  width: 0; 
  height: 0;
  border-left: 8px solid #8F8F8F;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  margin-left: 15px;
  margin-right: 15px;
}
</style>
