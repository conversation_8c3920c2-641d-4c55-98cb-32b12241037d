<template>
  <div class="page" style="user-select:none;">
    <div class="box">
      <div class="tab">
        <div :class="index == '0' ? 'active' : ''" @click="getChange('0')">
          提问
        </div>
        <div :class="index == '1' ? 'active' : ''" @click="getChange('1')">
          回答
        </div>
      </div>
      <questions v-if="index == '0'"></questions>
      <reply v-if="index == '1'"></reply>
    </div>
  </div>
</template>

<script>
import questions from'./components/index.vue'
import reply from'./components/reply.vue'
export default {
  name: 'list',
  components: {
    questions,
    reply
  },
  data() {
    return {
      index: '0'
    }
  },
  filters: {

  },
  created() {

  },
  methods: {
    getChange(index) {
      this.index = index
    },
    // 路由跳转
    getRouter() {
      this.$router.push({ path: '/appointment/detail' });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
 
  padding: 21px 24px;
  background: #FFFFFF;
  .tab{
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #666666;
    div{
      cursor: pointer;
    }
    >div:first-child{
      margin-right: 40px;
    }
    .active {
      color: #35A2FF;
      position: relative;
    }
  
    .active::after {
      content: "";
      position: absolute;
      width: 18px;
      height: 3px;
      background: #35A2FF;
      left: 0px;
      right: 0px;
      bottom: 0px;
      top: 30px;
      margin: auto;
      border-radius: 1px;
    }
  }
}
</style>
