<template>
    <div class="home">
        <div class="top">
            <div class="left">
                心理健康数据分析平台
            </div>
            <div class="right">
                <div @click="goHome">
                    <img src="@/assets/echarts/home.png" alt="">
                    首页
                </div>
            </div>
        </div>
        <a-row :gutter="20" style="margin: 20px;">
            <a-col class="gutter-row" :span="6">
                <div class="fenxi">
                    <div class="title">测评分析</div>
                    <div class="flex">
                        <div class="fenxiNum" v-for="(i, index) in fenxiList" :key="index">
                            <div class="num">
                                {{ i.num }}
                            </div>
                            <div class="name">
                                {{ i.name }}
                            </div>
                            <div class="type">
                                {{ i.type }}
                            </div>
                            <img :src="i.img" alt="">
                        </div>
                    </div>
                </div>
                <div class="yuyue">
                    <div class="title">咨询预约</div>
                    <div class="shuliang">
                        <div class="zixunnum numflex">
                            <div class="names">
                                咨询记录总数量
                            </div>
                            <div class="bottom">
                                328172 <span>次</span>
                            </div>
                        </div>
                        <div class="yuyuenum numflex">
                            <div class="names">
                                咨询预约总数量
                            </div>
                            <div class="bottom">
                                328172 <span>次</span>
                            </div>
                        </div>
                    </div>
                    <div id="charts" class="report-sleep-chart" ref="sleepChart"></div>
                </div>
            </a-col>
            <a-col class="gutter-row" :span="12">
                <div class="zhishu">
                    <div class="zhishutop">
                        <div v-for="(i, index) in topList" :key="index"
                            :style="{ backgroundImage: 'url(' + i.img + ')' }">
                            <div>{{ i.num }}</div>
                            <div>{{ i.name }}</div>
                        </div>
                    </div>
                </div>
                <div class="jiankang">
                    <div class="jiankangleft">
                        <div class="shuliang">
                            <img src="../../assets/echarts/baogao.png" alt="">
                            <div>
                                <div class="names">
                                    测评统计数量
                                </div>
                                <div class="nums">
                                    328172
                                </div>
                            </div>
                        </div>
                        <div class="pinggu">
                            <img src="../../assets/echarts/pinggu.png" alt="">
                            <div>
                                <div class="names">
                                    H5心理自助测评数量
                                </div>
                                <div class="nums">
                                    328172
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="jiankangcenter">

                    </div>
                    <div class="jiankangright">
                        <div class="tuanti">
                            <div>
                                <div class="names">
                                    下发测评任务数量
                                </div>
                                <div class="nums">
                                    328172
                                </div>
                            </div>
                            <img src="../../assets/echarts/tuanti.png" alt="">

                        </div>
                        <!-- <div class="shaicha">
                            <div>
                                <div class="names">
                                    个体筛查
                                </div>
                                <div class="nums">
                                    328172
                                </div>
                            </div>
                            <img src="../../assets/echarts/shaicha.png" alt="">

                        </div> -->
                    </div>
                </div>
                <div class="zongrenshu">
                    <div class="titlerenshu">
                        测评总人数
                    </div>
                    <div class="nrenflex">
                        <div class="nren" v-for="i in total.split('')" :key="i">
                            {{ i }}
                        </div>
                    </div>
                </div>
                <div class="ceping">
                    <div class="title">
                        测评使用频率
                    </div>
                    <div id="charts1" class="shiyong" ref="sleepCharts"></div>
                </div>
            </a-col>
            <a-col class="gutter-row" :span="6">
                <div class="weiji">
                    <div class="title">危机干预中心</div>
                    <div class="numInfo">
                        <div v-for="(i, index) in weijiList" :key="index">
                            <img :src="i.img" alt="">
                            <div>
                                <div class="weijiNum">
                                    {{ i.num }}
                                </div>
                                <div class="weijiName">
                                    {{ i.name }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nameTitle">
                        <span></span> 性别占比
                    </div>
                    <div id="chartsWeiji" class="weijiCharts" ref="sleepChartsWeiji"></div>
                    <div class="nameTitle">
                        <span></span> 年龄占比
                    </div>
                    <div id="chartsAge" class="AgejiCharts" ref="sleepChartsAge"></div>
                </div>
                <div class="changyong">
                    <div class="title">常用量表</div>
                    <div class="changyongInfo">
                        <div v-for="(i, index) in liangbiaoList" :key="index">
                            <div class="changyongInfoName">
                                <span>{{ index + 1 }}</span> {{ i.name }}
                            </div>
                            <div class="changyongInfoNum">
                                {{ i.num }} 次
                            </div>
                        </div>
                    </div>
                </div>
            </a-col>
        </a-row>
    </div>
</template>

<script>
import * as echarts from 'echarts/core'
import { TitleComponent, TooltipComponent, GridComponent, ToolboxComponent, LegendComponent } from 'echarts/components'
import { BarChart, LineChart, ScatterChart, PieChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
// 注册必须的组件
echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    ToolboxComponent,
    LegendComponent,
    BarChart,
    PieChart,
    LineChart,
    ScatterChart,
    CanvasRenderer,
])
export default {
    name: 'Home',
    components: {
    },
    computed: {
        sleepChartOpt() {
            let obj = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {},
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01]
                },
                yAxis: {
                    type: 'category',
                    data: ['学习压力', '人际关系', '适应', '人格', '自我认知', '恋爱问题', '强迫', '抑郁', '情绪困扰', '个人发展', '睡眠', '焦虑', '经济问题', '其他']
                },
                series: [
                    {
                        type: 'bar',
                        data: [
                            { value: 100, itemStyle: { color: '#23FFFC' } },
                            { value: 200, itemStyle: { color: '#23FFFC' } },
                            { value: 100, itemStyle: { color: '#23FFFC' } },
                            { value: 120, itemStyle: { color: '#23FFFC' } },
                            { value: 200, itemStyle: { color: '#23FFFC' } },
                            { value: 180, itemStyle: { color: '#23FFFC' } },
                            { value: 100, itemStyle: { color: '#23FFFC' } },
                            { value: 160, itemStyle: { color: '#23FFFC' } },
                            { value: 320, itemStyle: { color: '#23FFFC' } },
                            { value: 100, itemStyle: { color: '#23FFFC' } },
                            { value: 260, itemStyle: { color: '#23FFFC' } },
                            { value: 300, itemStyle: { color: '#23FFFC' } },
                            { value: 100, itemStyle: { color: '#23FFFC' } },
                            { value: 220, itemStyle: { color: '#23FFFC' } },
                            { value: 300, itemStyle: { color: '#23FFFC' } },
                        ],
                    }
                ]
            }
            return obj
        },
        sleepChartInfo() {
            let option = {
                xAxis: {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        data: [120, 200, 150, 80, 70, 110, 130, 120, 200, 150, 80, 70, 110, 130, 80, 90],
                        type: 'bar',
                        barWidth: 20, //柱图宽度
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 1, color: "#2244AC" }, //柱图渐变色
                                { offset: 0, color: "#00B7FF" }, //柱图渐变色
                            ]),
                        }
                    }
                ]
            };

            return option
        },
        sleepChartRight() {
            let option = {
                title: {
                    text: '24',
                    subtext: '总人数（人）',
                    left: 'center',
                    top: '38%',
                    textStyle: {
                        fontSize: 14,
                        color: '#FFA951',
                        align: 'center'
                    },
                    subtextStyle: {
                        fontFamily: "微软雅黑",
                        fontSize: 11,
                        color: '#fff',
                    }
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: 'Access From',
                        type: 'pie',
                        radius: ['60%', '80%'],

                        data: [
                            { value: 12, name: '男', itemStyle: { color: '#3BAC7C' } },
                            { value: 12, name: '女', itemStyle: { color: '#014AF6' } },
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            return option
        },
        sleepChartAge() {
            let option = {
                title: {
                    text: '24',
                    subtext: '总人数（人）',
                    left: 'center',
                    top: '38%',
                    textStyle: {
                        fontSize: 14,
                        color: '#FFA951',
                        align: 'center'
                    },
                    subtextStyle: {
                        fontFamily: "微软雅黑",
                        fontSize: 11,
                        color: '#fff',
                    }
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: 'Access From',
                        type: 'pie',
                        radius: ['60%', '80%'],

                        data: [
                            { value: 10, name: '10-15', itemStyle: { color: '#3BAC7C' } },
                            { value: 2, name: '1-5', itemStyle: { color: '#014AF6' } },
                            { value: 12, name: '6-10', itemStyle: { color: '#F69401' } },
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            return option
        },
    },
    data() {
        return {
            total: '0172086',
            liangbiaoList: [{
                name: '疲劳量表',
                num: '120'
            }, {
                name: '睡眠量表',
                num: '114'
            }, {
                name: '焦虑量表',
                num: '101'
            }, {
                name: '其他量表',
                num: '87'
            }, {
                name: '人际关系量表',
                num: '43'
            }, {
                name: '适应量表',
                num: '22'
            }, {
                name: '人格量表',
                num: '10'
            },],
            weijiList: [{
                name: '预警人数',
                num: '12',
                img: require('../../assets/echarts/yujing.png')
            }, {
                name: '干预人数',
                num: '10',
                img: require('../../assets/echarts/ganyu.png')
            }, {
                name: '解除人数',
                num: '0',
                img: require('../../assets/echarts/jeichu.png')
            }],
            topList: [{
                name: '学生总数量',
                num: '12345',
                img: require('../../assets/echarts/xuesheng.png')
            }, {
                name: '班主任数量',
                num: '12345',
                img: require('../../assets/echarts/jiaozhigong.png')
            }, {
                name: '咨询师数量',
                num: '12345',
                img: require('../../assets/echarts/zixun.png')
            }, {
                name: '学生数量',
                num: '12345',
                img: require('../../assets/echarts/peibi.png')
            }
            // , {
            //     name: '心理档案数量',
            //     num: '12345',
            //     img: require('../../assets/echarts/dangan.png')
            // }
            ],
            fenxiList: [{
                name: '正常(人）',
                num: '12345',
                type: '90%',
                img: require('../../assets/echarts/zc.png')
            }, {
                name: '轻度（人）',
                num: '1',
                type: '30%',
                img: require('../../assets/echarts/zz.png')
            }, {
                name: '中度（人）',
                num: '4',
                type: '1%',
                img: require('../../assets/echarts/yj.png')
            }, {
                name: '重度（人）',
                num: '4',
                type: '1%',
                img: require('../../assets/echarts/wj.png')
            }],
            sleepCharts: '',
            sleepChartsInfo: '',
            sleepChartsInfoWeiji: '',
            sleepChartsInfoAge: ''
        }
    },
    created() {
        this.$nextTick(() => {
            let sleepDom = this.$refs.sleepChart
            this.sleepCharts = echarts.init(sleepDom)
            this.sleepCharts.setOption(this.sleepChartOpt)

            let sleepDomInfo = this.$refs.sleepCharts
            this.sleepChartsInfo = echarts.init(sleepDomInfo)
            this.sleepChartsInfo.setOption(this.sleepChartInfo)

            let sleepDomInfoWeiji = this.$refs.sleepChartsWeiji
            this.sleepChartsInfoWeiji = echarts.init(sleepDomInfoWeiji)
            this.sleepChartsInfoWeiji.setOption(this.sleepChartRight)

            let sleepDomInfoAge = this.$refs.sleepChartsAge
            this.sleepChartsInfoAge = echarts.init(sleepDomInfoAge)
            this.sleepChartsInfoAge.setOption(this.sleepChartAge)
        })
    },
    methods: {
        goHome() {
            this.$router.push({ path:'/dashboard/analysis' });
        }
    }
}
</script>

<style scoped lang="scss">
.home {
    width: 100%;
    height: 100%;
    background: url(../../assets/echarts/bg.png) no-repeat;
    background-size: 100% 100%;
    overflow-y: scroll;
}

.fenxi {
    width: 100%;
    height: 407px;
    background: url(../../assets/echarts/fenxibg.png) no-repeat;
    background-size: 100% 100%;

    .title {
        font-size: 16px;
        color: #DFEEF3;
        height: 40px;
        line-height: 36px;
        padding-left: 45px;
    }
}

.top {
    width: 100%;
    height: 56px;
    background: url(../../assets/echarts/top.png) no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left {
        font-weight: 600;
        font-size: 24px;
        color: #F3FCFF;
        line-height: 56px;
        margin-left: 60px;
    }

    .right {
        margin-right: 60px;

        div {
            display: flex;
            align-items: center;
            font-size: 16px;
            color: #23FFFC;

            img {
                width: 16px;
                right: 16px;
                margin-right: 10px;
            }
        }
    }
}

.ceping {
    width: 100%;
    height: 330px;
    margin-top: 20px;
    background: url(../../assets/echarts/cepingbg.png) no-repeat;
    background-size: 100% 100%;

    .title {
        font-size: 16px;
        color: #DFEEF3;
        height: 40px;
        line-height: 36px;
        padding-left: 45px;
    }
}

.weiji {
    width: 100%;
    height: 629px;
    background: url(../../assets/echarts/weijibg.png) no-repeat;
    background-size: 100% 100%;

    .title {
        font-size: 16px;
        color: #DFEEF3;
        height: 40px;
        line-height: 36px;
        padding-left: 45px;
    }
}

.changyong {
    width: 100%;
    height: 335px;
    margin-top: 20px;
    background: url(../../assets/echarts/changyong.png) no-repeat;
    background-size: 100% 100%;

    .title {
        font-size: 16px;
        color: #DFEEF3;
        height: 40px;
        line-height: 36px;
        padding-left: 45px;
    }
}

.yuyue {
    width: 100%;
    height: 559px;
    margin-top: 20px;
    background: url(../../assets/echarts/yuyuebg.png) no-repeat;
    background-size: 100% 100%;

    .title {
        font-size: 16px;
        color: #DFEEF3;
        height: 40px;
        line-height: 36px;
        padding-left: 45px;
    }

    .shuliang {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding-top: 20px;

        .zixunnum {
            width: 168px;
            height: 84px;
            background: url(../../assets/echarts/zixunnum.png) no-repeat;
            background-size: 100% 100%;

            .bottom {
                color: #23FFFC;
            }
        }

        .yuyuenum {
            width: 168px;
            height: 84px;
            background: url(../../assets/echarts/yuyuenum.png) no-repeat;
            background-size: 100% 100%;

            .bottom {
                color: #23B9FF;
            }
        }
    }
}

.numflex {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    justify-content: center;
    padding-left: 20px;

    .names {
        color: #FFFFFF;
        font-size: 14px;
    }

    .bottom {
        font-size: 20px;

        span {
            font-weight: 600;
            font-size: 14px;
            color: #FFFFFF;
            margin-left: 12px;
        }
    }
}

.flex {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;

    .fenxiNum {
        width: 35%;
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-top: 20px;
        position: relative;
    }

    .fenxiNum:first-child {
        color: #0AB2D0;
    }

    .fenxiNum:nth-child(2) {
        color: #FFE238;
    }

    .fenxiNum:nth-child(3) {
        color: #FCB10D;
    }

    .fenxiNum:nth-child(4) {
        color: #FF312C;
    }

    .num {
        font-size: 24px;
        font-weight: 600;
    }

    .name {
        font-size: 14px;
        margin: 10px 0;
    }

    .type {
        position: relative;
        top: 15px;
        font-size: 15px;
        height: 0;
    }
}

.report-sleep-chart {
    width: 100%;
    height: 400px;
}

.zhishu {
    margin-top: 10px;
}

.zhishutop {
    display: flex;
    align-items: center;
    justify-content: space-between;

    >div {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        justify-content: center;
        width: 160px;
        height: 78px;
        background-size: 100% 100%;
        margin-right: 15px;
        padding-left: 60px;

        >div:first-child {
            font-size: 21px;
            color: #FFFFFF;
        }

        >div:last-child {
            font-size: 14px;
            color: #B4C1BF;
        }
    }
}

.titlerenshu {
    text-align: center;
    font-weight: 600;
    font-size: 25px;
    color: #00FFF4;
    margin: 20px 0;
}

.nrenflex {
    display: flex;
    align-items: center;
    justify-content: center;

    .nren {
        width: 67px;
        height: 64px;
        background: url(../../assets/echarts/renshubg.png) no-repeat;
        background-size: 100% 100%;
        text-align: center;
        line-height: 60px;
        font-weight: 600;
        font-size: 50px;
        color: #00FFF4;
        margin-right: 10px;
    }
}

.shiyong {
    width: 100%;
    height: 300px;
}

.jiankangleft {
    >div {
        display: flex;
        align-items: center;
        margin-bottom: 40px;

        .names {
            font-size: 14px;
            color: #FFFFFF;
        }
    }

    .shuliang {
        width: 184px;
        height: 82px;
        background: url(../../assets/echarts/left.png) no-repeat;
        background-size: 100% 100%;

        .nums {
            font-size: 20px;
            color: #23FFFC;
        }
    }

    .pinggu {
        width: 184px;
        height: 82px;
        background: url(../../assets/echarts/left1.png) no-repeat;
        background-size: 100% 100%;

        .nums {
            font-size: 20px;
            color: #23B9FF;
        }
    }
}

.jiankang {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 35px;
}

.jiankangcenter {
    width: 344px;
    height: 350px;
    background: url(../../assets/echarts/centerbg.png) no-repeat;
    background-size: 100% 100%;
}

.jiankangright {
    >div {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-bottom: 40px;

        .names {
            font-size: 14px;
            color: #FFFFFF;
        }
    }

    .tuanti {
        width: 184px;
        height: 82px;
        background: url(../../assets/echarts/right.png) no-repeat;
        background-size: 100% 100%;

        .nums {
            font-size: 20px;
            color: #23FFFC;
        }
    }

    .shaicha {
        width: 184px;
        height: 82px;
        background: url(../../assets/echarts/right1.png) no-repeat;
        background-size: 100% 100%;

        .nums {
            font-size: 20px;
            color: #23B9FF;
        }
    }
}

.ceping {
    margin-top: 40px;
}

.numInfo {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin: 20px 20px;

    >div:nth-child(2) {
        justify-content: flex-end;
    }

    >div {
        width: 45%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;

        img {
            width: 60px;
            height: 60px;
            margin-right: 20px;
        }

        .weijiNum {
            font-weight: 600;
            font-size: 24px;
            color: #30FDFF;
        }

        .weijiName {
            font-size: 14px;
            color: #BAC3C4;
        }
    }
}

.weijiCharts,
.AgejiCharts {
    width: 100%;
    height: 150px;
}

.nameTitle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 84px;
    height: 28px;
    background: rgba(35, 255, 252, 0.14);
    border-radius: 4px;
    margin: 10px auto;
    text-align: center;
    line-height: 28px;
    color: #fff;
    font-size: 14px;

    span {
        display: inline-block;
        width: 4px;
        height: 4px;
        background: #FFFFFF;
        border-radius: 50%;
        margin-right: 5px;
    }
}

.changyongInfo {
    >div {
        margin: 10px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #fff;
        font-size: 14px;

        span {
            margin-right: 10px;
            font-size: 14px;
            display: inline-block;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 19px;
            border-radius: 50%;

        }
    }

    >div:nth-child(1) {
        color: #23FFFC;

        span {
            background: #149896;

        }

        .changyongInfoNum {
            color: #03CBCB;
        }
    }
    >div:nth-child(2) {
        color: #23B9FF;

        span {
            background: #1782B7

        }

        .changyongInfoNum {
            color: #24B6FF
        }
    }
    >div:nth-child(3) {
        color: #FED357;

        span {
            background: #CA8812

        }

        .changyongInfoNum {
            color: #FED357
        }
    }
}
</style>