<template>
  <div>
    <a-form style="max-width: 500px; margin: 40px auto 0;" :form="form">
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="团体报告名称">
        <a-input placeholder="请输入团体报告抬头" v-decorator="['name', { rules: [{required: true, message: '请先输入团体报告名称'}] }]" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="测评对象">
        <a-input placeholder="请输入测评对象" v-decorator="['testObject', { rules: [{required: true, message: '请先输入测评对象'}] }]" />
      </a-form-item>
      <a-form-item label="量表" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-tree-select
          showSearch
          multiple
          style="width:100%"
          :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
          :treeData="measureTree"
          v-model="selectedMeasure"
          treeNodeFilterProp="title"
          placeholder="请选择量表">
        </a-tree-select>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="编码前缀">
        <a-input placeholder="请输入编码前缀，以便于模糊查询相关报告" v-decorator="['prefixEncode', {}]" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="测评时间">
        <a-range-picker
          format="YYYY-MM-DD"
          v-model="timeRange"
          :placeholder="['开始时间', '结束时间']"
          @change="onDateChange"
        />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="测试者编号">
        <a-input placeholder="请输入测试者编号，以便于模糊查询相关报告" v-decorator="['userNumber', {}]" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="年龄范围">
        <a-input-number v-decorator="[ 'ageRangeBefor', {}]" />
        -
        <a-input-number v-decorator="[ 'ageRangeAfter', {}]" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="性别">
        <j-dict-select-tag v-decorator="['sex', {}]" :triggerChange="true" placeholder="请输入用户性别"
                           dictCode="sex"/>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="部门名称">
        <a-input placeholder="请输入部门名称，以便于模糊查询相关报告" v-decorator="['departmentName', {}]" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="程度">
        <j-multi-select-tag
          v-decorator="['degrees', {}]"
          dictCode="result_degree"
          placeholder="请选择程度">
        </j-multi-select-tag>
      </a-form-item>
      <a-form-item :wrapperCol="{span: 20, offset: 9}">
        <a-button type="primary" @click="nextStep">下一步</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import JMultiSelectTag from '@/components/dict/JMultiSelectTag'
import pick from 'lodash.pick'
import moment from 'moment'
import { getAction } from '@api/manage'
export default {
  name: 'Step1',
  components: {JMultiSelectTag},
  data() {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      form: this.$form.createForm(this),
      model: {},
      timeRange:[],
      measureTree: [],
      selectedMeasure: [],
      url: {
        treeList: '/psychology/psCategory/queryTreeSelectList'
      }
    }
  },
  created() {
    this.loadTreeData()
    this.form.resetFields()
    this.$nextTick(() => {
      let groupInfo = JSON.parse(sessionStorage.getItem('groupInfo'))
      this.form.setFieldsValue(pick(groupInfo, 'name', 'measureIds', 'prefixEncode', 'userNumber', 'ageRangeBefor', 'ageRangeAfter',
        'sex', 'departmentName', 'degree'))
      if (groupInfo && groupInfo.startTime && groupInfo.endTime) {
        this.timeRange = [moment(groupInfo.startTime),moment(groupInfo.endTime)]
        this.model.startTime = groupInfo.startTime
        this.model.endTime = groupInfo.endTime
      }
    })
  },
  methods: {
    onDateChange: function(value, dateString) {
      this.model.startTime = dateString[0]
      this.model.endTime = dateString[1]
    },
    nextStep () {
      const { form: { validateFields } } = this
      // 先校验，通过表单校验后，才进入下一步
      validateFields((err, values) => {
        if (!err) {
          let groupInfo = values;
          groupInfo.startTime = this.model.startTime;
          groupInfo.endTime = this.model.endTime;
          if (this.selectedMeasure.length == 0) {
            this.$message.error('请先选择至少一个量表!')
            return
          }
          let selectedMeasure = this.selectedMeasure.map(select => select.split(',')[0])
          groupInfo.measureIds = selectedMeasure.length > 0 ? selectedMeasure.join(',') : ''
          sessionStorage.setItem('groupInfo',JSON.stringify(groupInfo))
          this.$emit('nextStep')
        }
      })
    },
    loadTreeData() {
      //查找筛查数据
      var params = {}
      params.isScreening = '1'
      getAction(this.url.treeList, params).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            this.measureTree.push(temp)
          }
          this.loading = false
        }
      })
    }
  }
}
</script>

<style scoped>

</style>