<template>
  <a-modal
    :title="title"
    :width="1500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :footer="null"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-card :bordered="false">
      <a-steps class="steps" :current="currentTab">
        <a-step title="填写生成信息" />
        <a-step title="确认筛查数据" />
        <a-step title="完成" />
      </a-steps>
      <div class="content">
        <step1 v-if="currentTab === 0" @nextStep="nextStep" />
        <step2 v-if="currentTab === 1" @nextStep="nextStep" @prevStep="prevStep" />
        <step3 v-if="currentTab === 2" @prevStep="prevStep" @finish="finish" />
      </div>
    </a-card>
  </a-modal>
</template>

<script>
import Step1 from '@views/group/modules/GroupReportModalStep1'
import Step2 from '@views/group/modules/GroupReportModalStep2'
import Step3 from '@views/group/modules/GroupReportModalStep3'

export default {
  name: 'DxGroupReportRecordModal',
  components: {
    Step1,
    Step2,
    Step3
  },
  data() {
    return {
      title: '操作',
      visible: false,
      currentTab: 0,
      confirmLoading: false
    }
  },
  created() {
  },
  methods: {
    add() {
      this.visible = true
    },
    close() {
      sessionStorage.removeItem('groupInfo')
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    nextStep() {
      if (this.currentTab < 2) {
        this.currentTab += 1
      }
    },
    prevStep() {
      if (this.currentTab > 0) {
        this.currentTab -= 1
      }
    },
    finish() {
      this.currentTab = 0
      this.handleCancel();
    }
  }
}
</script>

<style lang="less" scoped>

</style>