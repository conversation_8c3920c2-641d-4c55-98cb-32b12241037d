<template>
  <a-drawer
    :title="title"
    :maskClosable="true"
    :width="drawerWidth"
    placement="right"
    :closable="true"
    destroyOnClose
    @close="handleCancel"
    :visible="visible">
    <a-spin :spinning="loading">
      <div v-if="!isEdit">
        <div class="no-print" style="text-align: right;margin-right: 35px;">
          <a-button v-print="'#reportCommonContent'" ghost type="primary" style="margin-right: 10px;">打印</a-button>
          <a-button ghost type="primary" @click="viewEdit">编辑</a-button>
        </div>
        <div id="reportCommonContent">
          <p v-html="datas"></p>
        </div>
      </div>
      <div v-if="isEdit">
        <a-spin :spinning="loading">
          <a-row>
            <j-editor :j-height="700" v-model="editContent"/>
          </a-row>
          <a-row style="margin-top: 15px;">
            <a-col :span="18"></a-col>
            <a-col :span="6">
              <a-button ghost type="primary" @click="saveResport" style="margin-right: 10px;">确定</a-button>
              <a-button ghost type="primary" @click="cancelResport">取消</a-button>
            </a-col>
          </a-row>
        </a-spin>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script>
  import { getAction } from '@/api/manage'
  import JEditor from '@/components/jeecg/JEditor'

  export default {
    name: 'ReportCommonForm',
    components: {
      JEditor
    },
    data() {
      return {
        title: '操作',
        loading: false,
        drawerWidth: 700,
        visible: false,
        datas: '',
        isEdit: false,
        editContent: '',
        url: {
          generatePrintHtml: '/psEvaluation/generatePrintHtml'
        }
      }
    },
    methods: {
      handleCancel() {
        this.close()
      },
      edit(record) {
        let that = this
        that.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
        that.visible = true
        that.generatePrintHtml(record.id)
      },
      generatePrintHtml(id) {
        this.loading = true
        getAction(this.url.generatePrintHtml, { id: id }).then((res) => {
          if (res.success) {
            this.datas = res.result
            this.loading = false
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.isEdit = false
      },
      // 根据屏幕变化,设置抽屉尺寸
      resetScreenSize() {
        let screenWidth = document.body.clientWidth
        if (screenWidth < 500) {
          this.drawerWidth = screenWidth
        } else {
          this.drawerWidth = 900
        }
      },
      viewEdit() {
        let that_ = this
        this.isEdit = true
        this.editContent = this.datas
        this.loading = true
        setTimeout(function() {
          that_.loading = false
        }, 250)
      },
      saveResport() {
        this.isEdit = false
        this.datas = this.editContent
      },
      cancelResport() {
        this.isEdit = false
      }
    }
  }
</script>

<style scoped>

</style>