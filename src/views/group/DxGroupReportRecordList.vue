<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="报告名称">
              <j-input placeholder="请输入报告名称" v-model="queryParam.name"></j-input>
            </a-form-item>
          </a-col>
<!--          <a-col :md="6" :sm="8">-->
<!--            <a-form-item label="预警人数">-->
<!--              <a-input placeholder="请输入预警人数" v-model="queryParam.totalUserCount"></a-input>-->
<!--            </a-form-item>-->
<!--          </a-col>-->
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <!-- <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="sync">生成团体报告</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div> -->

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange">
        <span slot="finishUserRatio"  slot-scope="text, record">
          {{ text+'' !== '0' ? text + '%' : '0' }}
        </span>
        <span slot="action" slot-scope="text, record">
          <template v-if="record.finishUserRatio >= 100">
            <a @click="handlePrintForm(record)">报告预览</a>
            <a-divider type="vertical" />
            <a @click="handleExportDoc(record)">报告下载</a>
          </template>
          <!-- <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm> -->
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->
    <!-- 表单区域 -->
    <dxGroupReportRecord-modal ref="modalForm" @ok="modalFormOk"></dxGroupReportRecord-modal>
    <!-- 打印表单页 -->
    <group-report-form ref="GroupReportForm"></group-report-form>
  </a-card>
</template>

<script>
import DxGroupReportRecordModal from './modules/DxGroupReportRecordModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import GroupReportForm from './modules/GroupReportForm'
import {deleteAction, downFile, getAction} from '@/api/manage'

export default {
  name: 'DxGroupReportRecordList',
  mixins: [JeecgListMixin],
  components: {
    DxGroupReportRecordModal,
    GroupReportForm
  },
  data() {
    return {
      description: '团体报告记录管理页面',
      // 表头 任务名称、任务说明、开始时间、结束时间、参与总人数、实际完成人数、 完成人数占比、完成情况、有效人数、完成率、总体预警情况、各级预警人数占比、预警人数、性别分析、水平分析、心理健康指导建议
      columns: [
        {
          title: '任务名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '任务说明',
          align: 'center',
          dataIndex: 'details'
        },
        {
          title: '开始时间',
          align: 'center',
          dataIndex: 'beginTime'
        },
        {
          title: '结束时间',
          align: 'center',
          dataIndex: 'finishTime'
        },
        {
          title: '参与总人数',
          align: 'center',
          dataIndex: 'totalUserCount'
        },
        {
          title: '实际完成人数',
          align: 'center',
          dataIndex: 'finishUserCount',
        },
        {
          title: '完成人数占比',
          align: 'center',
          dataIndex: 'finishUserRatio',
          scopedSlots:{customRender: 'finishUserRatio'}
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      downLoading: false,
      url: {
        list: '/psEvaluation/list',
        delete: '/group/dxGroupReportRecord/delete',
        deleteBatch: '/group/dxGroupReportRecord/deleteBatch',
        exportDocUrl: '/psEvaluation/exportReportPDF',
      }
    }
  },
  methods: {
    handlePrintForm(record) {
      this.$refs.GroupReportForm.edit(record)
      this.$refs.GroupReportForm.title = '内容预览'
    },
    handleExportDoc(record) {
      this.downLoading = true
      var fileName = record.name + '.pdf'
      downFile(this.url.exportDocUrl, {id: record.id}).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          this.downLoading = false
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName)
          this.downLoading = false
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
          this.downLoading = false
        }
      })
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>