<template>
  <div class="page" style="user-select:none;">
    <div class="box">
      <div class="zixun">
        <img class="bg" src="@/assets/image/zixunbg.png" alt="">
        <div class="top">
          <img class="logo" :src="imgUrl + info.avatar" alt="">
          <div class="left">
            <div class="leftTop">
              <div class="name">
                {{ info.name }}
              </div>
              <div class="sixin">
                <a-icon type="mail" />
                私信
              </div>
            </div>
            <div class="zhicheng">
              {{ info.description }}
            </div>
            <div class="leftBottom">
              <div>
                <div>
                  执业年限
                </div>
                <div>
                  <span>{{ info.work_start_date }}</span>年
                </div>
              </div>
              <div>
                <div>
                  服务人次
                </div>
                <div>
                  <span>{{ info.serviceCount }}</span>人
                </div>
              </div>
              <div>
                <div>
                  所在地
                </div>
                <div>
                  <span>{{info.address}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="left">
            <div>
              <div class="title">
                <img src="@/assets/image/zizhi.png" alt="">
                个人资质
              </div>
              <div class="info">
                {{ info.description }}
              </div>
            </div>
            <div>
              <div class="title">
                <img src="@/assets/image/zixunduixiang.png" alt="">
                咨询对象
              </div>
              <div class="info">
                {{ info.consultObject }}
              </div>
            </div>
            <div>
              <div class="title">
                <img src="@/assets/image/shanchang.png" alt="">
                擅长领域
              </div>
              <div class="info">
                {{ info.skillSpace }}
              </div>
            </div>
          </div>
          <div class="line"></div>
          <div class="left">
            <div>
              <div class="title">
                <img src="@/assets/image/shouxun.png" alt="">
                受训及工作经历
              </div>
              <div class="info">
                <p v-for="i in info.careers"> {{i.content}}</p>
              </div>
            </div>
            <div>
              <div class="title">
                <img src="@/assets/image/yuyue.png" alt="">
                咨询须知
              </div>
              <div class="info">
                {{ info.notice }}
              </div>
            </div>
          </div>
        </div>
        <div class="button" @click="getRouter">
          预约
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listDoctorInfo } from '@/api/api'
export default {
  name: 'detail',
  components: {
  },
  data () {
    return {
      imgUrl: 'https://eduyun.zhisongkeji.com/zhisong-jiaoyu/',
      type: '2', // 1是预约详情页面  2是心理咨询预约登记表
      id: '',
      info: {}
    }
  },
  filters: {

  },
  created () {
    this.id = this.$route.query.id
    this.getlistDoctorInfo()
  },
  methods: {
    // 路由跳转
    getRouter () {
      this.$router.push({
        path: '/appointment/from',
        query: {
          id: this.id
        }
      });
    },

    // 查询老师
    getlistDoctorInfo () {
      listDoctorInfo({
        id: this.id
      }).then((res) => {
        if (res.success) {
          this.info = res.result
        } else {
        }
      });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  padding: 10px 30px;
}

.button {
  margin: 0 auto;
  width: 197px;
  height: 38px;
  background: #35a2ff;
  border-radius: 19px;
  text-align: center;
  line-height: 38px;
  color: #fff;
  font-size: 14px;
  margin-top: 80px;
  cursor: pointer;
}

.zixun {
  background: #ffffff;
  border-radius: 16px;
  margin-top: 10px;
  position: relative;
  padding-bottom: 60px;

  .bg {
    width: 100%;
    position: absolute;
  }

  .top {
    display: flex;
    align-items: flex-start;
    z-index: 100;
    position: relative;
    top: 75px;

    .logo {
      width: 132px;
      height: 174px;
      margin-left: 48px;
    }

    .leftTop,
    .leftBottom {
      display: flex;
      align-items: center;
    }

    .left {
      margin-left: 17px;

      .zhicheng {
        color: #ffffff;
        font-size: 16px;
        margin-bottom: 30px;
        margin-top: 5px;
      }

      .leftBottom {
        > div {
          margin-right: 50px;

          div:nth-child(1) {
            font-size: 14px;
            color: #333333;
          }

          div:nth-child(2) {
            color: #666666;
            font-size: 14px;

            span {
              font-weight: bold;
              font-size: 28px;
              color: #0486fe;
              margin-right: 5px;
            }
          }
        }
      }
    }

    .leftTop {
      .name {
        font-size: 26px;
        color: #ffffff;
        font-weight: bold;
      }

      .sixin {
        color: #ffffff;
        width: 78px;
        height: 29px;
        border-radius: 17px;
        border: 1px solid #ffffff;
        text-align: center;
        line-height: 27px;
        margin-left: 22px;
        cursor: pointer;
      }
    }
  }

  .bottom {
    margin-top: 100px;
    margin-left: 48px;
    display: flex;
    align-items: center;

    .left {
      width: 48%;
    }

    .line {
      width: 1px;
      height: 230px;
      border: 1px solid rgba(151, 151, 151, 0.15);
      margin-right: 30px;
    }

    .title {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333;
      margin-bottom: 20px;

      img {
        width: 36px;
        margin-right: 15px;
      }
    }

    .info {
      font-size: 14px;
      margin-bottom: 20px;
      color: #999;
    }
  }
}
</style>
