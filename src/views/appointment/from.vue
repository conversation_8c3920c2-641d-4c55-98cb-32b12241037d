<template>
  <div class="page" style="user-select:none;">
    <div class="box">
      <!-- 表内容 -->
      <div class="from">
        <div class="title">
          心理咨询预约登记表
        </div>
        <div class="mess">
          为使咨询更有效果及节约时间，希望你能在咨询前详细提供如下资料，我们承诺严格保密。希望你能尽量填写真实、有效、准确的信息，尽量描述清楚所 要咨询的问题发生的时间、经过、以及您对此问题的看法、处理方式等。
        </div>
        <!-- <div class="typeTitle">
          <span></span>
          预约方式
        </div>
        <div class="yuyue">
          <div>
            <img src="@/assets/image/yuyin.png" alt="">
            <div class="typeName">
              语音
            </div>
            <div class="radio">
              <a-radio :checked="type == 'yuyin'" @change="getChoose" value="yuyin"></a-radio>
            </div>
            <div class="money">
              78元
            </div>
          </div>
          <div>
            <img src="@/assets/image/yuyin.png" alt="">
            <div class="typeName">
              视频
            </div>
            <div class="radio">
              <a-radio :checked="type == 'shipin'" @change="getChoose" value="shipin"></a-radio>
            </div>
            <div class="money">
              78元
            </div>
          </div>
          <div>
            <img src="@/assets/image/mian.png" alt="">
            <div class="typeName">
              面对面
            </div>
            <div class="radio">
              <a-radio :checked="type == 'mian'" @change="getChoose" value="mian"></a-radio>
            </div>
            <div class="money">
              78元
            </div>
          </div>
        </div> -->
        <div class="typeTitle">
          <span></span>
          预约时间
        </div>
        <div class="chooseTime">
          <div class="chilTitle">
            选择日期
          </div>
          <div class="zhou">
            <div v-for="(i, index) in list" :key="index" :class="index == zhouIndex ? 'isActive isChoose' : ''">
              {{ i.name }} </br>
              {{ i.time }}
            </div>
          </div>
          <div class="chilTitle">
            选择时间
          </div>
          <div class="time">
            <div v-for="(i, index) in timelist" :key="index" :class="index == timeIndex ? 'isActive isChoose' : ''">
              {{ i }}
            </div>
          </div>
        </div>
        <div class="typeTitle">
          <span></span>
          家庭情况
        </div>
        <div class="text">
          <a-textarea v-model="params.familySituation" :auto-size="{ minRows: 3, maxRows: 5 }"
            placeholder="你认为家庭关系的那些方面和成长经历影响到了现在困扰的你？" />
        </div>
        <div class="typeTitle">
          <span></span>
          来询问题
        </div>
        <div class="radioList">
          <div class="radioTitle">
            你困惑或难以摆脱的问题是什么？
          </div>
          <a-radio-group v-model="params.question" style="margin-left: 20px;" >
            <a-radio :value="i.value" v-for="i in dictOptions" :key="i.value">
              {{ i.text }}
            </a-radio>
          </a-radio-group>

        </div>
        <div class="typeTitle">
          <span></span>
          咨询目的
        </div>
        <div class="text">
          <a-textarea v-model="params.consultationPurpose" :auto-size="{ minRows: 3, maxRows: 5 }" placeholder="你期待从咨询中得到什么样的帮助或实现怎么样的转变？" />
        </div>
        <div class="typeTitle">
          <span></span>
          咨询历史
        </div>
        <div class="text">
          <a-textarea v-model="params.consultationHistory" :auto-size="{ minRows: 3, maxRows: 5 }" placeholder="以前有没有做过咨询和心理测试，结果如何？" />
        </div>
        <div class="typeTitle">
          <span></span>
          近期有无重大事情发生
        </div>
        <div class="text">
          <a-textarea v-model="params.majorEvents" :auto-size="{ minRows: 3, maxRows: 5 }" placeholder="若有请标明，时间、主要事件、造成的影响" />
        </div>
        <div class="agree">
          <a-checkbox></a-checkbox>
          同意 <span>《心理咨询预约协议》</span> 与 <span>《知情通知书》</span>
        </div>
        <div class="button">
          提交
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ajaxGetDictItems, getDictItemsFromCache, paAppointment } from '@/api/api'
import {doctorSchedule } from '@/api/api' 
export default {
  name: 'from',
  components: {},
  data() {
    const options = [
      { label: '学习困难', value: '学习困难' },
      { label: '人际关系', value: '人际关系' },
      { label: '适应', value: '适应' },
    ];
    return {
      params:{},
      value2: '',
      options,
      type: '',
      timeIndex: '0',
      timelist: ['09：00-10:00', '10：00-11:00'],
      list: [{
        name: '今天',
        time: '06-05'
      }, {
        name: '明天',
        time: '06-05'
      }, {
        name: '后天',
        time: '06-05'
      }, {
        name: '周二',
        time: '06-05'
      }, {
        name: '周三',
        time: '06-05'
      }, {
        name: '周四',
        time: '06-05'
      }, {
        name: '周五',
        time: '06-05'
      }],
      timeList: [],
      zhouIndex: '0',
      id: '',
      dictCode: 'ask_question',
      dictOptions: []
    }
  },
  filters: {

  },
  created() {
    this.id = this.$route.query.id
    this.getTime()
    this.initDictData()
  },
  methods: {
    initDictData() {
  
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(this.dictCode, null).then((res) => {
        if (res.success) {
          //                console.log(res.result);
          this.dictOptions = res.result;
        }
      })
    },
    // 获取时间组
    getTime() {
      doctorSchedule({
        id: this.id
      }).then((res) => {
        if (res.success) {
          this.info = res.result
        } else {
        }
      });
    },
    // 获取周几
    getWeek(dateStr) {
      let weeks = {
        "0": '星期日',
        "1": '星期一',
        "2": '星期二',
        "3": '星期三',
        "4": '星期四',
        "5": '星期五',
        "6": '星期六',
      }
      let date = new Date(dateStr);
      let weekIndex = date.getDay();
      date = weeks[weekIndex];
      return date
    },

    // 获取当前时间，day为number，getDay(-1):昨天的日期;getDay(0):今天的日期;getDay(1):明天的日期;【以此类推】
    getDay(day) {
      var today = new Date();
      var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
      today.setTime(targetday_milliseconds); //注意，这行是关键代码

      var tYear = today.getFullYear();
      var tMonth = today.getMonth();
      var tDate = today.getDate();
      tMonth = this.doHandleMonth(tMonth + 1);
      tDate = this.doHandleMonth(tDate);
      return tYear + "-" + tMonth + "-" + tDate;
    },
    doHandleMonth(month) {
      var m = month;
      if (month.toString().length == 1) {
        m = "0" + month;
      }
      return m;
    },
    getChoose(e) {
      this.type = e.target.value
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  padding: 10px 30px;
}

.from {
  border-radius: 16px;
  background: #fff;
  padding: 34px 36px;
  margin-top: 20px;

  .title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
  }

  .mess {
    font-size: 16px;
    color: #666666;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .typeTitle {
    display: flex;
    align-items: center;
    height: 28px;
    background: rgba(62, 166, 255, 0.1);
    font-size: 16px;
    color: #333;
    margin-top: 20px;

    span {
      display: inline-block;
      width: 3px;
      height: 14px;
      background: #3EA6FF;
      margin: 0 10px;
    }
  }

  .yuyue {
    margin-top: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    >div {
      text-align: center;

      .typeName {
        font-size: 14px;
        color: #333333;
        margin: 5px 0;
      }

      .money {
        font-size: 12px;
        color: #999999;
        margin-top: 5px;
      }
    }

    >div:nth-child(2) {
      margin: 0 120px;
    }
  }

}

.chilTitle {
  text-align: center;
  margin-right: 440px;
  margin-top: 20px;
  margin-bottom: 10px;
}

.zhou {
  display: flex;
  align-items: center;
  justify-content: center;

  >div {
    text-align: center;
    width: 50px;
    height: 50px;
    background: #EAEAEA;
    border-radius: 5px;
    margin-right: 20px;
    font-size: 14px;
  }

  .isChoose {
    background: #E2F2FF;
    color: #3EA6FF;

  }

  .isActive {
    border: 1px dashed #3EA6FF;
  }
}

.time {
  display: flex;
  align-items: center;
  justify-content: center;

  >div {
    text-align: center;
    width: 211px;
    height: 38px;
    background: #EAEAEA;
    border-radius: 5px;
    margin-right: 35px;
    font-size: 14px;
    line-height: 38px;
  }

  .isActive {
    border: 1px dashed #3EA6FF;
    background: #E2F2FF;
    color: #3EA6FF;
  }
}

.text {
  padding: 20px 0;
  margin: 0 20px;
}

.radioTitle {
  margin: 20px 0;
  margin-left: 20px;
  font-size: 14px;
  color: #666666;
}

.agree {
  margin-left: 20px;
  color: #333333;
  font-size: 14px;

  span {
    color: #0486FE;
  }
}

.button {
  width: 197px;
  height: 38px;
  background: #35A2FF;
  border-radius: 19px;
  text-align: center;
  line-height: 38px;
  color: #FFFFFF;
  font-size: 14px;
  margin: 30px auto;
}
</style>
