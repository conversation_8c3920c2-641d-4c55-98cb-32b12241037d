<template>
  <j-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name" label="普查名称">
          <a-input placeholder="请输入普查名称" v-model="model.name" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type" label="普查类型">
          <j-dict-select-tag v-model="model.type" placeholder="请选择普查类型" dictCode="evaluation_type" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="beginTime" label="普查开始时间">
          <a-date-picker showTime valueFormat='YYYY-MM-DD HH:mm:ss' v-model="model.beginTime" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="finishTime" label="普查结束时间">
          <a-date-picker showTime valueFormat='YYYY-MM-DD HH:mm:ss' v-model="model.finishTime" />
        </a-form-model-item>
        <a-form-model-item label="班级" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
          <j-select-depart v-model="departList" @back="backDepartInfo" :backDepart="true" :isRadio="false"></j-select-depart>
        </a-form-model-item>
        <a-form-model-item label="学生" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
          <j-select-patient v-model="stucentList" @back="backPatientInfo" :backInfo="true" :isRadio="false"></j-select-patient>
        </a-form-model-item>
        <a-form-model-item label="量表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="psEvaluationMeasureList">
          <a-select
            mode="multiple"
            show-search
            placeholder="请选择量表"
            option-filter-prop="children"
            style="width: 100%"
            :filter-option="filterOption"
            v-model="psEvaluationMeasureList"
            @change="handleChange"
          >
            <a-select-option v-for="measure in measures" :value="measure.id" :key="measure.id" :channel="measure">
              {{ measure.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <!-- <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="userType" label="用户类型">
          <j-dict-select-tag v-model="queryParam.userType" placeholder="请选择普查用户类型" dictCode="evaluation_userType" />
        </a-form-model-item> -->
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="details" label="详情">
          <a-input placeholder="请输入详情" v-model="model.details" />
        </a-form-model-item>
		
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import moment from "moment"
import JSelectDepart from '../../../components/jeecgbiz/JSelectDepart.vue'

  export default {
  components: { JSelectDepart },
    name: "PsEvaluationModal",
    data () {
      return {
        title:"操作",
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        measures: [],
        psEvaluationMeasureList: [],
        stucentList: "",
        departList: "",
        validatorRules:{
        },
        url: {
          add: "/psEvaluation/add",
          edit: "/psEvaluation/edit",
          listAll: '/psychology/psMeasure/listAllWithTenants'
        },
      }
    },
    created () {
    },
    methods: {
      add () {
        //初始化默认值
        this.edit({});
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
        this.loadPsTemplate()
      },
      close () {
        this.$emit('close');
        this.visible = false;
        this.$refs.form.clearValidate();
      },
      handleOk () {
        const that = this;
        // 触发表单验证
         this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            console.log(this.model)
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }else{
             return false;
          }
        })
      },
      /**
       * 初始化量表下拉选
       */
       loadPsTemplate() {
        httpAction(this.url.listAll, {}, 'get').then((res) => {
          if (res.success) {
            this.measures = res.result
          }
        })
      },
      handleCancel () {
        this.close()
      },
      filterOption(input, option) {
        console.log(option.componentOptions)
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
      handleChange(value, option) {
        this.model.psEvaluationMeasureList = []
        if (value && value.length > 0) {
          value.forEach(element => {
            this.model.psEvaluationMeasureList.push({
              measureId: element
            })
          });
        }
        console.log(this.model.psEvaluationMeasureList);
      },
      backPatientInfo(value) {
        this.model.psEvaluationUserList = []
        if (value && value.length > 0) {
          value.forEach(element => {
            this.model.psEvaluationUserList.push({
              userId: element.value,
              type: 1
            })
          });
        }
        console.log(this.model.psEvaluationUserList);
      },
      backDepartInfo(value) {
        this.model.psEvaluationClassList = []
        if (value && value.length > 0) {
          value.forEach(element => {
            this.model.psEvaluationClassList.push({
              classId: element.value,
              className: element.text
            })
          });
        }
        console.log(this.model.psEvaluationClassList);
      }
    }
  }
</script>

<style lang="less" scoped>

</style>