<template>
  <j-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">
      
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title" label="宣教名称">
          <a-input placeholder="请输入宣教名称" v-model="model.title" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subTitle" label="宣教分类">
          <a-select placeholder="请选择宣教分类" v-model="model.materialCategoryId"> 
            <a-select-option v-for="item in typeList" :key="item.value" :value="item.value">{{item.text}}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type" label="宣教类型">
          <a-radio-group v-model="model.type">
            <a-radio :value="1">文章</a-radio>
            <a-radio :value="2">视频</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item v-if="model.type == 1" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content" label="文章">
          <j-editor :j-height="600" v-model="model.content" />
        </a-form-model-item>
        <a-form-model-item v-if="model.type == 2" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="videoPath" label="视频地址">
          <a-input placeholder="请输入视频地址" v-model="model.videoPath" />
          <j-upload-2 v-model="model.videoPath" :uploadParams="uploadParams" :multiple="false" :number="1" :returnUrl="true"></j-upload-2>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import {initDictOptions, filterDictText,filterDictTextByCache} from '@/components/dict/JDictSelectUtil'

  import moment from "moment"

  export default {
    name: "MentalHealthProfessionalNewsModal",
    data () {
      return {
        title:"操作",
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        validatorRules:{
        },
        url: {
          add: "/me/mentalHealthProfessionalNews/add",
          edit: "/me/mentalHealthProfessionalNews/edit",
        },
        typeList: [],
        uploadParams: {
          'category': 'eduyun'
        },
      }
    },
    created () {
    },
    methods: {
      add () {
        //初始化默认值
        this.edit({});
      },
      edit (record) {
        initDictOptions('psychological_info_type').then((res) => {
          if (res.success) {
            this.typeList = res.result;
          }
        });
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      close () {
        this.$emit('close');
        this.visible = false;
        this.$refs.form.clearValidate();
      },
      handleOk () {
        const that = this;
        // 触发表单验证
         this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }else{
             return false;
          }
        })
      },
      handleCancel () {
        this.close()
      },


    }
  }
</script>

<style lang="less" scoped>

</style>