<template>
  <j-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">
        <!-- 机构名称、机构负责人、机构地址、联系方式、成立日期、机构认证、沙盘资质、沙盘类型、服务范围、上传资质图片 -->
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="orgName" label="机构名称">
          <a-input placeholder="请输入机构名称" v-model="model.orgName" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="leaderName" label="机构负责人">
          <a-input placeholder="请输入机构负责人" v-model="model.leaderName" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address" label="机构地址">
          <a-input placeholder="请输入机构地址" v-model="model.address" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="contactInfo" label="联系方式">
          <a-input placeholder="请输入联系方式" v-model="model.contactInfo" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="establishDate" label="成立日期">
          <a-date-picker v-model="model.establishDate" style="width: 100%"/>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="certification" label="机构认证">
          <a-input placeholder="请输入机构认证" v-model="model.certification" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sandplayQualification" label="沙盘资质">
          <a-input placeholder="请输入沙盘资质" v-model="model.sandplayQualification" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sandplayType" label="沙盘类型">
          <a-select placeholder="请选择沙盘类型" v-model="model.materialCategoryId"> 
            <a-select-option value="实体沙盘">实体沙盘</a-select-option>
            <a-select-option value="虚拟沙盘">虚拟沙盘</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="serviceScope" label="服务范围">
          <a-input placeholder="请输入服务范围" v-model="model.serviceScope" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="qualificationImgPath" label="资质图片">
          <j-image-upload v-model="model.qualificationImgPath"></j-image-upload>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import moment from "moment"

  export default {
    name: "MentalHealthBaseOrgModal",
    data () {
      return {
        title:"操作",
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        validatorRules:{
          orgName:[{ required: true, message: '请输入机构名称!' }],
          contactInfo:[{ validator: this.validatePhone }],
        },
        url: {
          add: "/me/mentalHealthBaseOrg/add",
          edit: "/me/mentalHealthBaseOrg/edit",
        },
      }
    },
    created () {
    },
    methods: {
      add () {
        //初始化默认值
        this.edit({});
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      close () {
        this.$emit('close');
        this.visible = false;
        this.$refs.form.clearValidate();
      },
      handleOk () {
        const that = this;
        // 触发表单验证
         this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            if (this.model.establishDate && this.model.establishDate instanceof moment) this.model.establishDate = this.model.establishDate.format("YYYY-MM-DD");

            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }else{
             return false;
          }
        })
      },
      handleCancel () {
        this.close()
      },

      validatePhone (rule, value, callback) {
        if (!value) {
          callback()
        } else {
          if (new RegExp(/^1[3|4|5|7|8|9][0-9]\d{8}$/).test(value) || new RegExp(/^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/).test(value)) {
            callback()
          } else {
            callback("请输入正确格式的手机号或座机号!");
          }
        }
      },
    }
  }
</script>

<style lang="less" scoped>

</style>