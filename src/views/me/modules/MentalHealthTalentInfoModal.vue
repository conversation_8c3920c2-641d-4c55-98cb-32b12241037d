<template>
  <j-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">
        <!-- 姓名、性别、联系方式、头像、邮箱、咨询师等级、服务权限、就业年限、隶属机构（选择机构）、所在地区、咨询对象、受训经历、结案方式及定价、擅长标签、擅长领域、资质展示、个人寄语、预约须知 -->
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name" label="姓名">
          <a-input placeholder="请输入姓名" v-model="model.name" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="contact" label="联系方式">
          <a-input placeholder="请输入联系方式" v-model="model.contact" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="avatar" label="头像URL">
          <j-image-upload v-model="model.avatar"></j-image-upload>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="email" label="邮箱">
          <a-input placeholder="请输入邮箱" v-model="model.email" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="counselorLevel" label="咨询师等级">
          <a-input placeholder="请输入咨询师等级" v-model="model.counselorLevel" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="servicePermission" label="服务权限">
          <a-radio-group v-model="model.servicePermission">
            <a-radio :value="0">否</a-radio>
            <a-radio :value="1">是</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="workingYears" label="就业年限">
          <a-input-number v-model="model.workingYears" style="width: 100%"/>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="orgId" label="隶属机构">
          <a-select v-model="model.orgId" placeholder="请选择隶属机构"> 
            <a-select-option v-for="item in orgList" :key="item.id">{{item.orgName}}</a-select-option>
          </a-select>
          <!-- <j-select-depart v-model="model.orgId" :multi="false" :backDepart="true"></j-select-depart> -->
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="region" label="所在地区">
          <a-input placeholder="请输入所在地区" v-model="model.region" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="counselingTarget" label="咨询对象">
          <a-input placeholder="请输入咨询对象（学生/教师）" v-model="model.counselingTarget" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="trainingExperience" label="受训经历">
          <a-input placeholder="请输入受训经历" v-model="model.trainingExperience" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="caseClosingMethod" label="结案方式">
          <a-input placeholder="请输入结案方式" v-model="model.caseClosingMethod" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="pricing" label="咨询价格(元/小时)">
          <a-input-number v-model="model.pricing" style="width: 100%"/>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="specialtyTags" label="擅长标签">
          <a-input placeholder="请输入擅长标签(逗号分隔)" v-model="model.specialtyTags" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="specialtyFields" label="擅长领域">
          <a-input placeholder="请输入擅长领域(逗号分隔)" v-model="model.specialtyFields" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="qualificationImages" label="资质图片">
          <j-image-upload v-model="model.qualificationImages" :isMultiple="true"></j-image-upload>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="personalMessage" label="个人寄语">
          <a-input placeholder="请输入个人寄语" v-model="model.personalMessage" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="appointmentNotice" label="预约须知">
          <a-input placeholder="请输入预约须知" v-model="model.appointmentNotice" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import moment from "moment"

  export default {
    name: "MentalHealthTalentInfoModal",
    data () {
      return {
        title:"操作",
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        validatorRules:{
        name:[{ required: true, message: '请输入姓名!' }],
        },
        url: {
          add: "/me/mentalHealthTalentInfo/add",
          edit: "/me/mentalHealthTalentInfo/edit",
          list: "/me/mentalHealthBaseOrg/list"
        },
        orgList: []
      }
    },
    created () {
    },
    methods: {
      add () {
        //初始化默认值
        this.edit({});
      },
      edit (record) {
        this.getList()
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      close () {
        this.$emit('close');
        this.visible = false;
        this.$refs.form.clearValidate();
      },
      getList() {
        getAction(this.url.list, {pageNo: 1, pageSize: 10000}).then((res) => {
          if (res.success) {
            this.orgList = res.result.records
          }
        })
      },
      handleOk () {
        const that = this;
        // 触发表单验证
         this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }else{
             return false;
          }
        })
      },
      handleCancel () {
        this.close()
      }

    }
  }
</script>

<style lang="less" scoped>

</style>