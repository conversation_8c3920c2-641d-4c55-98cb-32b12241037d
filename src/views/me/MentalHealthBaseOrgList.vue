<template>
  <a-card :bordered="false">

    <!-- 查询区域 机构名称、机构负责人、联系方式 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">

          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="机构名称">
              <j-input placeholder="请输入机构名称" v-model="queryParam.orgName"></j-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="机构负责人">
              <j-input placeholder="请输入机构负责人" v-model="queryParam.leaderName"></j-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="联系方式">
              <j-input placeholder="请输入联系方式" v-model="queryParam.contactInfo"></j-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('心里人才库-机构管理')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <mentalHealthBaseOrg-modal ref="modalForm" @ok="modalFormOk"></mentalHealthBaseOrg-modal>
  </a-card>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import MentalHealthBaseOrgModal from './modules/MentalHealthBaseOrgModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: "MentalHealthBaseOrgList",
    mixins:[JeecgListMixin],
    components: {
      MentalHealthBaseOrgModal
    },
    data () {
      return {
        description: '机构管理',
        // 表头 机构名称、机构负责人、联系方式、成立日期、机构地址
        columns: [
		   {
            title: '机构名称',
            align:"center",
            dataIndex: 'orgName'
           },
		   {
            title: '机构负责人',
            align:"center",
            dataIndex: 'leaderName'
           },
		   {
            title: '联系方式',
            align:"center",
            dataIndex: 'contactInfo'
           },
		   {
            title: '成立日期',
            align:"center",
            dataIndex: 'establishDate'
           },
           {
            title: '机构地址',
            align:"center",
            dataIndex: 'address'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            scopedSlots: { customRender: 'action' },
          }
        ],
		url: {
          list: "/me/mentalHealthBaseOrg/list",
          delete: "/me/mentalHealthBaseOrg/delete",
          deleteBatch: "/me/mentalHealthBaseOrg/deleteBatch",
          exportXlsUrl: "me/mentalHealthBaseOrg/exportXls",
          importExcelUrl: "me/mentalHealthBaseOrg/importExcel",
       },
    }
  },
  computed: {
    importExcelUrl: function(){
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
    methods: {
     
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>