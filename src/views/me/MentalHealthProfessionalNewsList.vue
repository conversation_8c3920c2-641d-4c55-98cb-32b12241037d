<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="宣教名称">
              <a-input placeholder="请输入宣教名称" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="宣教分类">
              <a-select placeholder="请选择宣教分类" v-model="queryParam.materialCategoryId">
                <a-select-option v-for="item in typeList" :key="item.value" :value="item.value">{{
                  item.text
                }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="发布状态">
              <j-dict-select-tag  v-model="queryParam.status" placeholder="请选择发布状态" dictCode="send_status"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('心里人才库-心理资讯')">导出</a-button>
      <a-upload
        name="file"
        :showUploadList="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        @change="handleImportExcel"
      >
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="type" slot-scope="text, record">
          {{ record.type === 1 ? '文章' : '视频' }}
        </template>

        <span slot="status" slot-scope="text, record">
          <a-badge :status="text | statusTypeFilter" :text="record.status_dictText" />
        </span>

        <span slot="action" slot-scope="text, record">
          <template v-if="record.status == 1">
            <a-popconfirm title="确定取消发布吗?" @confirm="() => handleCancelReport(record)">
              <a>取消发布</a>
            </a-popconfirm>
          </template>
          <template v-else>
            <a-popconfirm title="确定发布吗?" @confirm="() => handleReport(record)">
              <a>发布</a>
            </a-popconfirm>
          </template>
          <a-divider v-if="record.status != 1" type="vertical" />
          <a v-if="record.status != 1" @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <template>
            <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
              <a>删除</a>
            </a-popconfirm>
          </template>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <mentalHealthProfessionalNews-modal ref="modalForm" @ok="modalFormOk"></mentalHealthProfessionalNews-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import MentalHealthProfessionalNewsModal from './modules/MentalHealthProfessionalNewsModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { initDictOptions, filterDictText, filterDictTextByCache } from '@/components/dict/JDictSelectUtil'
import { getAction, httpAction } from '@/api/manage'

const statusMap = {
  1: {
    status: 'success',
  },
  0: {
    status: 'error',
  }
}

export default {
  name: 'MentalHealthProfessionalNewsList',
  mixins: [JeecgListMixin],
  components: {
    MentalHealthProfessionalNewsModal,
  },
  data() {
    return {
      description: '心理资讯',
      // 表头 名称、资讯分类、资讯类型、创建时间
      columns: [
        {
          title: '宣教名称',
          align: 'center',
          dataIndex: 'title',
        },
        {
          title: '宣教分类',
          align: 'center',
          dataIndex: 'materialCategoryId',
          customRender: (text) => {
            if (!text) {
              return ''
            } else {
              return filterDictText(this.typeList, text)
            }
          },
        },
        {
          title: '宣教类型',
          align: 'center',
          dataIndex: 'type',
          scopedSlots: { customRender: 'type' },
        },
        {
          title: '发布状态',
          align: 'center',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' },
        }
      ],
      url: {
        list: '/me/mentalHealthProfessionalNews/list',
        delete: '/me/mentalHealthProfessionalNews/delete',
        report: '/me/mentalHealthProfessionalNews/report',
        deleteBatch: '/me/mentalHealthProfessionalNews/deleteBatch',
        exportXlsUrl: 'me/mentalHealthProfessionalNews/exportXls',
        importExcelUrl: 'me/mentalHealthProfessionalNews/importExcel',
      },
      typeList: [],
    }
  },
  filters: {
      statusFilter (type) {
        return statusMap[type].text
      },
      statusTypeFilter (type) {
        return statusMap[type].status
      }
    },
  created() {
    initDictOptions('psychological_info_type').then((res) => {
      if (res.success) {
        this.typeList = res.result
      }
    })
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    handleReport(record) {
      this.loading = true;
      let that = this
      let httpurl = this.url.report
      record.status = 1
      let method = 'put'
      httpAction(httpurl, record, method).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.modalFormOk()
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
        that.loading = false
      })
    },
    handleCancelReport(record) {
      this.loading = true;
      let that = this
      record.status = 2
      let httpurl = this.url.report
      let method = 'put'
      httpAction(httpurl, record, method).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.modalFormOk()
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
        that.loading = false
      })
    }
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>