<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="姓名">
              <a-input placeholder="请输入姓名" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="手机号">
              <a-input placeholder="请输入手机号" v-model="queryParam.telphone"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="班级名称">
              <a-input placeholder="请输入班级名称" v-model="queryParam.sysOrgId_dictText"></a-input>
            </a-form-item>
          </a-col>
          <!-- <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="年级名称">
              <a-input placeholder="请输入年级名称" v-model="queryParam.gradeName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="系/部名称">
              <a-input placeholder="请输入系/部名称" v-model="queryParam.deptName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学院名称">
              <a-input placeholder="请输入学院名称" v-model="queryParam.collegeName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24"> 
            <a-form-item label="学校名称">
              <a-input placeholder="请输入学校名称" v-model="queryParam.schoolName"></a-input>
            </a-form-item>
          </a-col> -->
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('心理档案')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-popover v-model="miniVisible" trigger="click" placement="bottom">
        <div slot="content">
          <vue-qr :size="200" :margin="10" :auto-color="true" :dot-scale="1" :text="qrMiniUrl" />
        </div>
        <a-button type="primary">老师二维码</a-button>
      </a-popover>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange">

        <span slot="banji" slot-scope="text, record">
          {{ record.sysOrgId_dictText }}{{ record.collegeCode_dictText }}{{ record.deptCode_dictText }}{{ record.gradeCode_dictText }}{{ record.classCode_dictText }}
        </span>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">档案详情</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>     
        </span>
      </a-table>
    </div>
    <!-- 表单区域 -->
    <paPatient-modal ref="modalForm" @ok="modalFormOk"></paPatient-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import PaPatientModal from './modules/PaPatientModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { mapGetters } from 'vuex'
import VueQr from 'vue-qr'
import { getAction } from '@api/manage'

export default {
  name: "PaPatientList",
  mixins: [JeecgListMixin],
  components: {
    PaPatientModal,
    VueQr
  },
  data () {
    return {
      description: '心理档案',
      // 表头 编号、姓名、手机号、性别、生日、年龄、班级、年级、系/部、学院、学校、民族、籍贯、创建时间
      // 新增/编辑字段：编号、姓名、手机号、性别、生日、年龄、班级、年级、系/部、学院、学校、民族、籍贯、监护人信息（姓名、性别、电话、年龄、与学生关系）
      // 详情可查看基本信息、心里情况（个人测试记录和报告，列表展示）、心路历程（量表测评得分对应状态、心理咨询记录、放松训练、减压中心）、监护人信息（姓名、性别、电话、年龄、与学生关系）
      // 查询条件：姓名、手机号、班级名称、年级名称、系/部名称，学院名称，学校名称
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: "center",
          customRender: function (t, r, index) {
            return parseInt(index) + 1;
          }
        },
        {
          title: '姓名',
          align: "center",
          dataIndex: 'name'
        },
        {
          title: '手机号',
          align: "center",
          dataIndex: 'telphone'
        },
        {
          title: '性别',
          align: "center",
          dataIndex: 'sex_dictText'
        },
        {
          title: '生日',
          align: "center",
          dataIndex: 'birthday'
        },
        {
          title: '年龄',
          align: "center",
          dataIndex: 'age'
        },
        {
          title: '班级',
          align: "center",
          // dataIndex: 'sysOrgId_dictText'
          scopedSlots: { customRender: 'banji' }
        },
        // {
        //   title: '年级',
        //   align: "center",
        //   dataIndex: 'gradeName'
        // },
        // {
        //   title: '系/部',
        //   align: "center",
        //   dataIndex: 'deptName'
        // },
        // {
        //   title: '学院',
        //   align: "center",
        //   dataIndex: 'collegeName'
        // },
        // {
        //   title: '学校',
        //   align: "center",
        //   dataIndex: 'schoolName'
        // },
        {
          title: '民族',
          align: "center",
          dataIndex: 'nationality_dictText'
        },
        {
          title: '籍贯',
          align: "center",
          dataIndex: 'nativePlace'
        },
        {
          title: '创建时间',
          align: "center",
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' },
        }
      ],
      url: {
        list: "/pa/paPatient/list",
        delete: "/pa/paPatient/delete",
        deleteBatch: "/pa/paPatient/deleteBatch",
        exportXlsUrl: "pa/paPatient/exportXls",
        importExcelUrl: "pa/paPatient/importExcel",
        generateQRCode: "/sys/user/generateQRCode"
      },
      miniVisible: false,
      qrMiniUrl: ''
    }
  },
  created() {
    var userInfo = JSON.parse(localStorage.getItem('pro__Login_Userinfo'))
    this.qrMiniUrl =
      'https://eduyun.zhisongkeji.com/student/pages/register/registerStudent/index?doctorId=' +
      this.userInfo().id +
      '&name=' +
      this.userInfo().realname
      // +
      // '&tenantId=' +
      // Vue.ls.get(TENANT_ID)
    console.log('qrMiniUrl=======', this.userInfo(), this.qrMiniUrl, userInfo)
    // getAction(this.url.generateQRCode, { id: this.userInfo().id }).then((res) => {
    //   this.qrMiniUrl = `${window._CONFIG['domianURL']}/${res.message}`
    // })
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  methods: {
    ...mapGetters(['userInfo']),
    handleDetail(record) {
      localStorage.setItem('PatientDetail', JSON.stringify(record))
      this.$store.dispatch('PatientDetail', record)
      this.$router.push('/PaPatientList/PaPatientDetail')
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>