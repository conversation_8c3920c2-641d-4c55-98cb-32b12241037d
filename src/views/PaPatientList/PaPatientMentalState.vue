<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :lg="6" :sm="8">
            <a-form-item label="学生姓名">
              <a-input placeholder="请输入学生姓名" v-model="queryParam.patientName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- table区域-begin -->
    <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange">
    </a-table>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'PaPatientMentalState',
  mixins: [JeecgListMixin, mixinDevice],
  data () {
    return {
      description: '心理状态',
      // 列表字段：学生姓名、症状指标、压力指标、韧性指标、家长调研记录、家长评估记录、班主任评估记录、咨询师访谈记录
      // 查询条件：学生姓名
      columns: [
        {
          title: '学生姓名',
          align: "center",
          dataIndex: 'userName'
        },
        {
          title: '症状指标',
          align: "center",
          dataIndex: 'departName'
        },
        {
          title: '压力指标',
          align: "center",
          dataIndex: 'departName'
        },
        {
          title: '韧性指标',
          align: "center",
          dataIndex: 'nightAsleepDate'
        },
        {
          title: '家长调研记录',
          align: "center",
          dataIndex: 'nightAsleepDate'
        },
        {
          title: '家长评估记录',
          align: "center",
          dataIndex: 'nightAsleepDate'
        },
        {
          title: '班主任评估记录',
          align: "center",
          dataIndex: 'nightAsleepDate'
        },
        {
          title: '咨询师访谈记录',
          align: "center",
          dataIndex: 'nightAsleepDate'
        }
      ],
      url: {
        list: "/diagnosis/dxResult/listPsychologicalState",

      },
      dictOptions: {},
      superFieldList: [],
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>