<template>
  <div class="patient-detail">
    <div class="patient-detail-info">
      <p class="title">
        <span>详情信息</span>
        <a-button type="primary" @click="goBack">返回</a-button>
      </p>
      <div class="content">
        <img class="content-img" v-if="patientDetail.avatarUrl" :src="getImgView(patientDetail.avatarUrl)" />
        <img class="content-img" v-else src="../../assets/img/avatar.png" />
        <div class="content-main">
          <div class="main-name">
            <span class="name">{{ patientDetail.name }}</span>
          </div>

          <div class="main-info">
            <a-row>
              <a-col :span="6">手机：{{ patientDetail.telphone }}</a-col>
              <a-col :span="6">性别：{{ patientDetail.sex_dictText }}</a-col>
              <a-col :span="6">生日：{{ patientDetail.birthday }}</a-col>
              <a-col :span="6">年龄：{{ patientDetail.age }}</a-col>
            </a-row>
            <a-row>
              <a-col :span="6">班级：{{ patientDetail.sysOrgId_dictText }}</a-col>
              <!-- <a-col :span="6">学校：{{ patientDetail.schoolName }}</a-col> -->
              <a-col :span="6">民族：{{ patientDetail.nationality_dictText }}</a-col>
              <a-col :span="6">籍贯：{{ patientDetail.nativePlace }}</a-col>
              <!-- <a-col :span="6">年级：{{ patientDetail.gradeName }}</a-col>
              <a-col :span="6">系/部：{{ patientDetail.deptName }}</a-col>
              <a-col :span="6">学院：{{ patientDetail.collegeName }}</a-col> -->
            </a-row>
            <!-- <a-row>
              <a-col :span="6">学校：{{ patientDetail.schoolName }}</a-col>
              <a-col :span="6">民族：{{ patientDetail.nationality_dictText }}</a-col>
              <a-col :span="6">籍贯：{{ patientDetail.nativePlace }}</a-col>
            </a-row> -->
            <!-- <a-row>
              <a-col :span="6">监护人姓名：{{ patientDetail.guardianList[0].name }}</a-col>
              <a-col :span="6">监护人性别：{{ patientDetail.guardianList[0].sex_dictText }}</a-col>
              <a-col :span="6">监护人电话：{{ patientDetail.guardianList[0].phone }}</a-col>
              <a-col :span="6">监护人年龄：{{ patientDetail.guardianList[0].age }}</a-col>
            </a-row>
            <a-row>
              <a-col :span="23">与学生关系：</a-col>
            </a-row> -->
          </div>
        </div>
      </div>
    </div>

    <div class="patient-detail-content">
      <a-tabs v-model="chooseTab">
        <a-tab-pane :key="1" tab="心理情况">
          <PaPsCondition :id="patientDetail.id"></PaPsCondition>
        </a-tab-pane>
        <a-tab-pane :key="2" tab="心路历程">
          <PaMeJourney :id="patientDetail.id"></PaMeJourney>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import PaPsCondition from './modules/PaPsCondition.vue'
import PaMeJourney from './modules/PaMeJourney.vue'
import { getFileAccessHttpUrl } from '@/api/manage'
export default {
  name: 'pa-patient-detail',

  components: {
    PaPsCondition,
    PaMeJourney,
  },

  data() {
    return {
      patientDetail: JSON.parse(localStorage.getItem("PatientDetail")),
      chooseTab: 1,
    }
  },

  created() {
    this.$store.dispatch('PatientDetail', JSON.parse(localStorage.getItem("PatientDetail")))
    this.chooseTab = Number(this.$route.query.chooseTab) || 1
  },

  methods: {
    goBack() {
      this.$router.go(-1)
    },

    getImgView(text){
      if(text && text.indexOf(",")>0){
        text = text.substring(0,text.indexOf(","))
      }
      return getFileAccessHttpUrl(text)
    },
    bmiNumber() {
      let stature = this.patientDetail.stature  //身高
      let weight = this.patientDetail.weight  //体重
      if (stature && stature != 0 || stature !== null) {
        return (weight / ((stature / 100) * (stature / 100))).toFixed(2)
      }
      return '-'
    }
  }
}
</script>

<style lang="less">
.patient-detail {
  width: 100%;

  .patient-detail-info {
    width: 100%;
    padding: 20px;
    background: #fff;
    border-radius: 12px;

    .title {
      display: flex;
      justify-content: space-between;
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }

    .content {
      display: flex;

      .content-img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }

      .content-main {
        padding-left: 20px;
        flex: 1;

        .main-name {
          display: flex;
          align-items: center;

          .name {
            font-size: 16px;
            font-weight: bold;
            padding-right: 15px;
          }
        }

        .main-info {

          .ant-row {
            padding-top: 10px;
          }
        }
      }
    }
  }

  .patient-detail-content {
    min-height: 400px;
    margin-top: 10px;
    background: #fff;
    border-radius: 12px;
  }

  .ant-card-body {
    padding-top: 0;
  }
}
</style>
