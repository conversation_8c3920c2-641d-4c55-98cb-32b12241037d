<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-card title="基本信息">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="number">
                <a-input v-model="model.number" placeholder="请输入编号"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
                <a-input v-model="model.name" placeholder="请输入姓名"></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="手机号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="telphone">
                <a-input v-model="model.telphone" placeholder="请输入手机号"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sex">
                <j-dict-select-tag type="radio" v-model="model.sex" dictCode="sex"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row v-if="!model.id">
            <a-col :span="12"> 
              <a-form-model-item label="登录密码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="password">
                <a-input type="password" placeholder="请输入登录密码" v-model="model.password" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="确认密码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="confirmpassword">
                <a-input type="password" @blur="handleConfirmBlur" placeholder="请重新输入登录密码" v-model="model.confirmpassword" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="所属组织" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="selecteddeparts">
                <j-select-depart2 v-model="model.selecteddeparts" @parentNodes="getParentNodes" :backDepart="true" :disableOrgCategory="disableOrgCategory"></j-select-depart2>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="生日" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="birthday">
                <j-date placeholder="请选择生日" v-model="model.birthday" style="width: 100%"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nationality">
                <j-dict-select-tag placeholder="请选择民族" v-model="model.nationality" dictCode="nationality"/>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="籍贯" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nativePlace">
                <a-input v-model="model.nativePlace" placeholder="请输入籍贯"></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-card>
        <br/>
        <a-card title="监护人信息">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="guardianName">
                <a-input v-model="model.guardianList[0].name" placeholder="请输入姓名"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="guardianSex">
                <j-dict-select-tag type="radio" v-model="model.guardianList[0].sex" dictCode="sex"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="手机号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="guardianPhone">
                <a-input v-model="model.guardianList[0].phone" placeholder="请输入手机号"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="年龄" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="guardianAge">
                <a-input v-model="model.guardianList[0].age" placeholder="请输入年龄"></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="与学生关系" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stature">
                <a-input v-model="model.stature" placeholder="请输入与学生关系"></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-card>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { mapGetters } from 'vuex'
  import moment from 'moment'
  import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'

  export default {
    name: 'PaPatientForm',
    components: { JLabelSelect },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        disableOrgCategory: ['1', '2', '3', '4'],
        model: {
          labels: null,
          guardianList: [{

          }]
        },
        form: this.$form.createForm(this),
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        validatorRules: {
          labels: [
            { required: false, message: '请输入标签!' }
          ],
          name: [
            { required: true, message: '请输入真实姓名!' }
          ],
          number: [
            { required: false, message: '请输入编号!' }
          ],
          birthday: [
            { required: true, message: '请输入生日!' }
          ],
          sex: [
            { required: true, message: '请输入性别!' }
          ],
          telphone: [
            { required: true, pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号!' }
          ],
          idCard: [
            { required: false, pattern: /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/, message: '请输入正确的身份证号!' }
          ],
          password: [{
            required: true,
            pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,
            message: '密码由8位数字、大小写字母和特殊符号组成!'
          },
            { validator: this.validateToNextPassword, trigger: 'change' }],
          confirmpassword: [{ required: true, message: '请重新输入登录密码!' },
            { validator: this.compareToFirstPassword }],
          // sysOrgId: [{ required: true, message: '请选择组织机构!' }],
          selecteddeparts: [{ required: true, message: '请选择组织机构!' }],
        },
        confirmDirty: false,
        url: {
          add: '/pa/paPatient/add',
          edit: '/pa/paPatient/edit',
          queryById: '/pa/paPatient/queryById',
          queryGroup: '/pa/paGroup/listAll',
          userWithDepart: "/sys/user/userDepartList", // 引入为指定用户查看组织机构信息需要的url
        },
        groups: [],
        schoolOrg: {},
        classOrg: {},
        parentOrgCodes: [],
        nextDepartOptions: []
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
      // this.loadGroupList()
    },
    methods: {
      moment,
      ...mapGetters(['nickname', 'userInfo']),
      changeSelectedGroups(value) {
        this.model.labels = value
      },
      add() {
        this.edit(this.modelDefault)
      },
      async edit(record) {
        let that = this;
        console.log('record', record)
        // if (record.id) {
        //   console.log('1---------111', record.id)
        //   const res = await getAction(this.url.queryById, { id: record.id })
        //   console.log('2222222222', res)
        // }
        if (!record.guardianList[0]) record.guardianList[0] = {}
        this.$nextTick(() => {
          that.model = Object.assign({}, { selecteddeparts: '' }, record);
          console.log('model', this.model)
        })
        if (record.hasOwnProperty("id")) {
          that.getUserDeparts(record.sysUserId);
        }
        this.visible = true
      },
      close () {
        this.$emit('close');
        this.visible = false;
        this.disableSubmit = false;
        this.nextDepartOptions = [];
        this.$refs.form.resetFields();
      },
      getUserDeparts (userid) {
        let that = this;
        getAction(that.url.userWithDepart, { userId: userid }).then((res) => {
          if (res.success) {
            let departOptions = [];
            let selectDepartKeys = []
            for (let i = 0; i < res.result.length; i++) {
              selectDepartKeys.push(res.result[i].key);
              //新增负责组织机构选择下拉框
              departOptions.push({
                value: res.result[i].key,
                label: res.result[i].title
              })
            }
            that.model.selecteddeparts = selectDepartKeys.join(",")
            that.nextDepartOptions = departOptions;
          }
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
              this.model.doctorId = this.userInfo().id
            } else {
              httpurl += this.url.edit
              method = 'put'
              delete this.model.doctorId
            }

            //修改了组织机构之后
            if (this.parentOrgCodes.length > 0) {
              this.model.sysOrgCode = this.parentOrgCodes[0]
              this.model.classCode = this.parentOrgCodes[this.parentOrgCodes.length - 1]
              this.model.sysOrgId = this.schoolOrg.id
              this.model.classId = this.classOrg.id
              if (this.parentOrgCodes.length == 3) {
                this.model.collegeCode = this.parentOrgCodes[1]
              } else if (this.parentOrgCodes.length == 4) {
                this.model.collegeCode = this.parentOrgCodes[1]
                this.model.deptCode = this.parentOrgCodes[2]
              } else if (this.parentOrgCodes.length == 5) {
                this.model.collegeCode = this.parentOrgCodes[1]
                this.model.deptCode = this.parentOrgCodes[2]
                this.model.gradeCode = this.parentOrgCodes[3]
              }
            }

            if (this.model.labels && this.model.labels.length > 0){
              let labelList = JSON.parse(JSON.stringify(this.model.labels))
              this.model.labels = []
              labelList.forEach(it => {
                this.model.labels.push({
                  labelId: it.labelId || it
                })
              })
            }

            //时间格式化
            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }
        })
      },
      loadGroupList() {
        const that = this
        httpAction(that.url.queryGroup, {}, 'get').then((res) => {
          if (res.success) {
            that.groups = res.result
          } else {
            that.$message.warning('分组获取失败')
          }
        })
      },
      handleConfirmBlur(e) {
        const value = e.target.value
        this.confirmDirty = this.confirmDirty || !!value
      },
      validateToNextPassword(rule, value, callback) {
        const confirmpassword = this.model.confirmpassword
        if (value && confirmpassword && value !== confirmpassword) {
          callback('两次输入的密码不一样！')
        }
        if (value && this.confirmDirty) {
          this.$refs.form.validateField(['confirmpassword'])
        }
        callback()
      },
      compareToFirstPassword(rule, value, callback) {
        if (value && value !== this.model.password) {
          callback('两次输入的密码不一样！')
        } else {
          callback()
        }
      },
      getParentNodes(orgNodes) {
        this.schoolOrg = {}
        this.parentOrgCodes = []
        if (orgNodes.length > 0) {
          orgNodes.forEach(item => {
            if (item.orgCategory == '1' || item.orgCategory == 1) {
              this.schoolOrg = item
            }
            if (item.orgCategory == '5' || item.orgCategory == 5) {
              this.classOrg = item
            }
          })
          let nodes = orgNodes.reverse()
          this.parentOrgCodes = nodes.map(item => item.orgCode);
        }
      },
    }
  }
</script>