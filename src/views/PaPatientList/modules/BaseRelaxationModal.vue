<template>
  <j-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" switchFullscreen @ok="handleOk" @cancel="handleCancel" cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">

        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title" label="素材名称">
          <a-input placeholder="请输入素材名称" v-model="model.title" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type" label="素材类型">
          <a-radio-group v-model="model.type">
            <a-radio v-if="this.type != 6 && this.type != 11" :value="1">文章</a-radio>
            <a-radio v-if="this.type != 6 && this.type != 11" :value="2">视频</a-radio>
            <a-radio :value="3">音频</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="标题图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="titleImg">
          <j-image-upload  v-model="model.titleImg" :bizPath="bizPath"></j-image-upload>
        </a-form-model-item>
        <!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description" label="内容">-->
        <!--          <a-input placeholder="请输入内容" v-model="model.description" />-->
        <!--        </a-form-model-item>-->
        <!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status" label="状态">-->
        <!--          <a-radio-group v-model="model.status">-->
        <!--            <a-radio :value="1">启用</a-radio>-->
        <!--            <a-radio :value="2">禁用</a-radio>-->
        <!--          </a-radio-group>-->
        <!--        </a-form-model-item>-->
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="materialCategoryName" label="素材分类">
          <j-dict-select-tag v-model="model.materialCategoryId" placeholder="请选择素材分类" :dictCode="keys[type]" :disabled="formDisabled" />
        </a-form-model-item>
        <!-- <a-form-model-item v-if="model.type == 2" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="videoPath" label="视频地址">
          <a-input placeholder="请输入视频地址" v-model="model.videoPath" />
          <j-upload-2 v-model="model.audioPath" :uploadParams="uploadParams" :multiple="false" :number="1" :returnUrl="true"></j-upload-2>
        </a-form-model-item> -->
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="audioPath" label="音频地址">
          <a-input placeholder="请输入音频地址" v-model="model.audioPath" />
          <j-upload-2 v-model="model.audioPath" :uploadParams="uploadParams" :multiple="false" :number="1" :returnUrl="true"></j-upload-2>
        </a-form-model-item>
        <!--        <a-form-model-item v-if="model.type == 2 || model.type == 3" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="duration" label="时长">-->
        <!--          <a-input placeholder="请输入时长" v-model="model.duration" />-->
        <!--        </a-form-model-item>-->
        <!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="playCount" label="播放次数">-->
        <!--          {{model.playCount || 0}}-->
        <!--        </a-form-model-item>-->
        <!-- <a-form-model-item v-if="model.type == 1" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content" label="内容">
          <j-editor v-model="model.content" />
        </a-form-model-item> -->

      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import moment from "moment"

export default {
  name: "BaseRelaxationModal",
  props: {
    type: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      keys: {
        1: "lull_sleep",
        2: "heart_breath",
        3: "music_therapy",
        4: "meditation_training",
        5: "film_view",
        6: "sleep_aid",
        7: "mental_radio",
        8: "music_modulation",
        9: "mental_movie",
        10: "short_radio",
        11: 'mental_adjustment_type'
      },
      formDisabled: false,
      title: "操作",
      bizPath: 'baseRelaxation',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      uploadParams: {
        'category': 'eduyun'
      },
      validatorRules: {
        title: [{ required: true, message: '请输入素材名称!' }],
        type: [{ required: true, message: '请选择素材类型!' }],
        titleImg: [{ required: true, message: '请上传标题图!' }],
      },
      url: {
        add: "/relax/baseRelaxation/add",
        edit: "/relax/baseRelaxation/edit",
      },
    }
  },
  created () {
  },
  methods: {
    add () {
      //初始化默认值
      this.edit({});
    },
    edit (record) {
      this.model = Object.assign({}, record);
      this.visible = true;
    },
    close () {
      this.$emit('close');
      this.visible = false;
      this.$refs.form.clearValidate();
    },
    handleOk () {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true;
          let httpurl = '';
          let method = '';
          if (!this.model.id) {
            httpurl += this.url.add;
            method = 'post';
          } else {
            httpurl += this.url.edit;
            method = 'put';
          }
          this.model.relaxType = this.type
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          })
        } else {
          return false;
        }
      })
    },
    handleCancel () {
      this.close()
    },


  }
}
</script>

<style lang="less" scoped>
</style>