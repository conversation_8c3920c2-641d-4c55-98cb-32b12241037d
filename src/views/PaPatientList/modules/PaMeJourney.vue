<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :lg="6" :sm="8">
            <a-form-item label="时间">
              <a-date-picker v-model="queryParam.consultTime" showTime format="YYYY-MM-DD" placeholder="请选择时间" />
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8">
            <a-form-item label="事件">
              <a-input placeholder="请输入事件" v-model="queryParam.consultPoint"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- table区域-begin -->
    <div>
      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange">
      </a-table>
    </div>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@/api/manage'

export default {
  name: 'PaMeJourney',
  mixins: [JeecgListMixin, mixinDevice],
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      description: '心路历程',
      // 表头 时间、事件、预警程度、咨询师、主题、时长
      columns: [
        {
          title: '时间',
          align: 'center',
          dataIndex: 'consultTime'
        },
        {
          title: '事件',
          align: 'center',
          dataIndex: 'question'
        },
        {
          title: '预警程度',
          align: 'center',
          dataIndex: 'level'
        },
        {
          title: '咨询师',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '主题',
          align: 'center',
          dataIndex: 'consultPoint'
        },
        // {
        //   title: '时长',
        //   align: 'center',
        //   dataIndex: 'collegeName'
        // }
      ],
      url: {
        list: "/pa/consultRecord/list",
      },
      dictOptions: {},
      superFieldList: [],
    }
  },

  methods: {
    loadData(arg) {
      if(!this.url.list){
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      params.patientId = this.id
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records||res.result;
          if(res.result.total)
          {
            this.ipagination.total = res.result.total;
          }else{
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>