<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="姓名">
              <a-input placeholder="请输入姓名" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="编号">
                <a-input placeholder="请输入编号" v-model="queryParam.number"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('预警人员')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange">

        <span slot="sex" slot-scope="text">
          {{text=="1" ? "男":"女"}}
        </span>
        <span slot="degree" slot-scope="text">
          <a-badge :color="text | statusTypeFilter" :text="text | statusFilter" />
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <dxWaringPatient-modal ref="modalForm" @ok="modalFormOk"></dxWaringPatient-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import DxWaringPatientModal from './modules/DxWaringPatientModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

const degreeMap = {
  0: {
    color: '#D9D9D9',
    text: '未答完'
  },
  1: {
    color: 'green',
    text: '正常'
  },
  2: {
    color: 'yellow',
    text: '轻度'
  },
  3: {
    color: 'red',
    text: '中度'
  },
  4: {
    color: 'purple',
    text: '重度'
  },
  5: {
    color: '#D9D9D9',
    text: '未知'
  }
}
export default {
  name: "DxWaringPatientList",
  mixins: [JeecgListMixin],
  components: {
    DxWaringPatientModal
  },
  filters: {
    statusFilter (type) {
      return degreeMap[type].text
    },
    statusTypeFilter (type) {
      return degreeMap[type].color
    }
  },
  data () {
    return {
      description: '预警人员管理页面',
      // 表头
      columns: [
        {
          title: '姓名',
          align: "center",
          dataIndex: 'name'
        },
        {
          title: '编号',
          align: "center",
          dataIndex: 'number'
        },
        {
          title: '年龄',
          align: "center",
          dataIndex: 'age'
        },
        {
          title: '性别',
          align: "center",
          dataIndex: 'sex',
          scopedSlots: { customRender: 'sex' },
        },
        {
          title: '任务名称',
          align: "center",
          dataIndex: 'taskName'
        },
        {
          title: '地区',
          align: "center",
          dataIndex: 'area'
        },
        {
          title: '预警状态',
          align: "center",
          dataIndex: 'waringStatus',
          scopedSlots: { customRender: 'degree' },
        },
        {
          title: '预警时间',
          align: "center",
          dataIndex: 'waringTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' },
        }
      ],
      url: {
        list: "/diagnosis/dxWaringPatient/list",
        delete: "/diagnosis/dxWaringPatient/delete",
        deleteBatch: "/diagnosis/dxWaringPatient/deleteBatch",
        exportXlsUrl: "diagnosis/dxWaringPatient/exportXls",
        importExcelUrl: "diagnosis/dxWaringPatient/importExcel",
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  methods: {

  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>