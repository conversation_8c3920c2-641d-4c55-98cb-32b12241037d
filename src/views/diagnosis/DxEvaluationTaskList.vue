<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="任务名称">
              <a-input placeholder="请输入任务名称" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8">
            <a-form-item label="开始时间">
              <a-date-picker v-model="queryParam.beginTime" format="YYYY-MM-DD" placeholder="请选择开始时间" />
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8">
            <a-form-item label="结束时间">
              <a-date-picker v-model="queryParam.finishTime" format="YYYY-MM-DD" placeholder="请选择结束时间" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="状态">
              <a-select v-model="queryParam.finishStatus" placeholder="请选择状态">
                <a-select-option value="0">未开始</a-select-option>
                <a-select-option value="1">进行中</a-select-option>
                <a-select-option value="2">已完成</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <!-- <a-button type="primary" icon="download" style="margin-left: 8px" @click="handleExportXls('测评任务')">导出</a-button>               -->
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-has="'measureTask:del'" v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length
        }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" class="j-table-force-nowrap" @change="handleTableChange">
        <template slot="finishStatus" slot-scope="text, record">
          {{ record.finishStatus == 0 ? '未开始' : record.finishStatus == 1 ? '进行中' : '已完成' }}
        </template>

        <span slot="action" slot-scope="text, record">
          <a v-has="'measureTask:edit'" @click="handleEdit(record)">编辑</a>
          <a-divider v-has="'measureTask:edit'" type="vertical" />
          <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical" />
          <a @click="handleAddUser(record)">追加测评人</a>
          <!-- <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm> -->
        </span>

      </a-table>
    </div>

    <DxEvaluationTaskModal ref="modalForm" @ok="modalFormOk"></DxEvaluationTaskModal>
    <DxEvaluationTaskUserModal ref="modalUserForm" @ok="modalFormOk"></DxEvaluationTaskUserModal>
    <a-modal
      title="二维码预览"
      :width="500"
      :visible="visibleCodeImage"
      :footer="null"
      @cancel="visibleCodeImage = false"
      cancelText="关闭">
      <div class="code-image-modal">
        <img class="code-image" alt="example" :src="codeImageSrc" />
        <div class="code-title">{{ codeTitle }}</div>
      </div>
    </a-modal>
  </a-card>
</template>

<script>

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DxEvaluationTaskModal from './modules/DxEvaluationTaskModal'
import DxEvaluationTaskUserModal from './modules/DxEvaluationTaskUserModal'
import { getAction, httpAction } from '@api/manage'
import { colAuthFilter } from "@/utils/authFilter";

export default {
  name: 'TaMeasureTaskList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    DxEvaluationTaskModal,
    DxEvaluationTaskUserModal
  },
  data () {
    return {
      isorter: {
        column: 'beginTime',
        order: 'desc'
      },
      measures: [],
      description: '测评任务',
      // 表头 任务名称、任务说明、开始时间、结束时间、状态
      // 测评任务新增/编辑字段：任务名称、任务说明、开始时间、结束时间、量表/量表包、报告查看权限、测评对象
      // 查询条件：任务名称、开始时间、结束时间、状态
      // 功能：新增、编辑、导出、查询、详情、添加测评人、修改结束时间
      columns: [
        {
          title: '任务名称',
          align: 'center',
          dataIndex: 'name',
        },
        {
          title: '任务说明',
          align: 'center',
          dataIndex: 'details'
        },
        {
          title: '开始时间',
          align: 'center',
          dataIndex: 'beginTime'
        },
        {
          title: '结束时间',
          align: 'center',
          dataIndex: 'finishTime'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'finishStatus',
          scopedSlots: { customRender: 'finishStatus' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        // /psychology/psMeasure/listAllWithTenants
        listAll: '/psychology/psMeasure/listAllWithTenants',
        list: '/psEvaluation/list',
        delete: '/psEvaluation/delete',
        deleteBatch: '/psEvaluation/deleteBatch',
        goContinueQuestionPage: '/ta/taMeasureTask/goContinueQuestionPage',
        downQRCode: '/wx/wxAuthorizationRecords/createQRCode'
      },
      dictOptions: {},
      superFieldList: [],
      visibleCodeImage: false,
      codeImageSrc: '',
      codeTitle: '',
    }
  },
  methods: {
    handleAddUser(record) {
      this.$refs.modalUserForm.add(record.id);
    },

    loadData(arg) {
      if(!this.url.list){
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      if (params.beginTime) params.beginTime = params.beginTime.format('YYYY-MM-DD') + ' 00:00:00'
      if (params.finishTime) params.finishTime = params.finishTime.format('YYYY-MM-DD') + ' 00:00:00'
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records||res.result;
          if(res.result.total)
          {
            this.ipagination.total = res.result.total;
          }else{
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    openQRCode(record) {
      let fileName = '答题二维码'
      getAction(this.url.downQRCode, {
        scene: record.id,
        page: 'pages/answer/answerQr/answer',
        width: 280,
        // appid: 'wxdc6fa6c14a53b1b9'
      }).then((data) => {
        if (data.code != 200) {
          this.$message.warning('获取二维码失败')
          return
        }
        let url = data.result
        this.codeImageSrc = url
        // 设置隐藏量表标题
        if (record.isShowTitle == 1) {
          this.codeTitle = record.measureNames
        } else {
          this.codeTitle = ''
        }
        this.visibleCodeImage = true
      })
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>