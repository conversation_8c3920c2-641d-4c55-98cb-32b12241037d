<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-tree-select showSearch style="width:100%" :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }" :treeData="measureTree" v-model="queryParam.measureId" treeNodeFilterProp="title" placeholder="请选择量表">
            </a-tree-select>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="报告内容(关键词)">
              <a-input placeholder="请输入报告内容" v-model="queryParam.content"></a-input>
            </a-form-item>
          </a-col>
          <a-col v-has="'resultReport:list:isDefault'" :span="6">
            <a-form-item label="是否默认">
              <j-dict-select-tag v-model="queryParam.isDefault" placeholder="请选择默认状态" dictCode="yn" />
            </a-form-item>
          </a-col>
          <a-col v-has="'resultReport:list:isDefault'" :span="6">
            <a-form-item label="组织机构名称">
              <a-input placeholder="请输入组织机构名称" v-model="queryParam.userName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <!--              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>-->
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button v-has="'resultReport:list:add'" @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!--      <a-button type="primary" icon="download" @click="handleExportXls">导出</a-button>
            <a-upload name="file" :showUploadList="false" :multiple="false" :action="importExcelUrl" @change="handleImportExcel">
              <a-button type="primary" icon="import">导入</a-button>
            </a-upload>-->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item v-has="'resultReport:list:delete'" key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <!--      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>-->

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a @click="initReport(record)">初始化</a>
          <a-divider v-has="'resultReport:list:delete'" type="vertical" />
          <a-popconfirm v-has="'resultReport:list:delete'" title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
          <!--          <a-dropdown>-->
          <!--            <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>-->
          <!--            <a-menu slot="overlay">-->
          <!--              <a-menu-item v-has="'resultReport:list:delete'">-->
          <!--                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">-->
          <!--                  <a>删除</a>-->
          <!--                </a-popconfirm>-->
          <!--              </a-menu-item>-->
          <!--&lt;!&ndash;              <a-menu-item>&ndash;&gt;-->
          <!--&lt;!&ndash;                <a @click="initReport(record)">初始化</a>&ndash;&gt;-->
          <!--&lt;!&ndash;              </a-menu-item>&ndash;&gt;-->
          <!--            </a-menu>-->
          <!--          </a-dropdown>-->
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <dxResultReport-modal ref="modalForm" @ok="modalFormOk"></dxResultReport-modal>
  </a-card>
</template>

<script>
import DxResultReportModal from './modules/DxResultReportModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@/api/manage'
import { colAuthFilter } from "@/utils/authFilter"

export default {
  name: 'DxResultReportList',
  mixins: [JeecgListMixin],
  components: {
    DxResultReportModal
  },
  data () {
    return {
      description: '测试报告设置管理页面',
      measureTree: [],
      loading: true,
      // 表头
      columns: [
        {
          title: '量表名称',
          align: 'center',
          dataIndex: 'measureName'
        },
        {
          title: '分数范围，前数',
          align: 'center',
          dataIndex: 'scoreRangeBefor'
        },
        {
          title: '分数范围，后数',
          align: 'center',
          dataIndex: 'scoreRangeAfter'
        },
        {
          title: '内容',
          align: 'center',
          dataIndex: 'contentStr'
        },
        {
          title: '用户',
          align: 'center',
          dataIndex: 'userName'
        },
        {
          title: '是否默认',
          align: 'center',
          dataIndex: 'isDefault_dictText'
        },
        {
          title: '排序',
          align: 'center',
          dataIndex: 'sort'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/diagnosis/dxResultReport/list',
        delete: '/diagnosis/dxResultReport/delete',
        deleteBatch: '/diagnosis/dxResultReport/deleteBatch',
        exportXlsUrl: 'diagnosis/dxResultReport/exportXls',
        importExcelUrl: 'diagnosis/dxResultReport/importExcel',
        treeList: '/psychology/psCategory/queryTreeSelectList',
        initTemplate: 'diagnosis/dxResultReport/initTemplate'
      }
    }
  },
  created () {
    this.loadTreeData();
    this.loadData();
    this.initColumns();
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    loadData (arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams()//查询条件
      if (params.measureId) {
        params.measureId = params.measureId.split(',')[0]
      }
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    loadTreeData () {
      getAction(this.url.treeList, null).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            this.measureTree.push(temp)
          }
          this.loading = false
        }
      })
    },
    initReport (record) {
      let that = this;
      this.$confirm({
        title: '提示',
        content: '初始化成为标准模板，私有定制的修改信息将被清除且不可还原，是否初始化?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          getAction(that.url.initTemplate, { 'id': record.id }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
            }
          })
        }
      })
    },
    initColumns () {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      this.columns = colAuthFilter(this.columns, 'resultReport:');

    }
  }
}
</script>
<style lang="less" scoped>
/** Button按钮间距 */
.ant-btn {
  margin-left: 3px;
}

.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px;
}

.ant-btn-danger {
  background-color: #ffffff;
}

.ant-modal-cust-warp {
  height: 100%;
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden;
}
</style>