<template>
  <div>
    <a-card :bordered="false">
      <!-- 查询区域 预警程度、活动名称、量表名称、因子名称、是否推荐关注、预警时间、访谈状态 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col :span="6">
              <a-form-item label="活动名称">
                <a-input placeholder="请输入活动名称" v-model="queryParam.taskName"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="8">
              <a-form-item label="预警程度">
                <j-dict-select-tag v-model="queryParam.degree" placeholder="请选择预警程度" dictCode="result_degree"/>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="量表名称">
                <a-tree-select
                  showSearch
                  style="width:100%"
                  :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
                  :treeData="measureTree"
                  v-model="queryParam.measureId"
                  treeNodeFilterProp="title"
                  placeholder="请选择量表名称">
                </a-tree-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
<!--            <a-col :span="6">-->
<!--              <a-form-item label="是否推荐关注">-->
<!--                <a-select v-model="queryParam.recommend_follow" placeholder="请选择是否推荐关注">-->
<!--                  <a-select-option value="1">是</a-select-option>-->
<!--                  <a-select-option value="0">否</a-select-option>-->
<!--                </a-select>-->
<!--              </a-form-item>-->
<!--            </a-col>-->
            <a-col :span="6">
              <a-form-item label="预警时间">
                <a-range-picker
                  format="YYYY-MM-DD"
                  :placeholder="['开始时间', '结束时间']"
                  @change="onDateChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="访谈状态">
                <a-select v-model="queryParam.interviewStatus" placeholder="请选择访谈状态">
                  <a-select-option value="0">未访谈</a-select-option>
                  <a-select-option value="1">已访谈</a-select-option>
                  <a-select-option value="2">已解除</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="table-operator">
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay">
            <a-menu-item key="1" @click="batchDel">
              <a-icon type="delete"/>
              删除
            </a-menu-item>
            <a-menu-item key="1" @click="batchEdit">
              <a-icon type="edit"/>
              修改状态
            </a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"> 批量操作
            <a-icon type="down"/>
          </a-button>
        </a-dropdown>
      </div>

      <!-- table区域-begin -->
      <div>

        <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
          <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length
          }}</a>项
          <a style="margin-left: 24px" @click="onClearSelected">清空</a>
        </div>

        <a-table
          ref="table"
          size="middle"
          bordered
          rowKey="id"
          :scroll="{ x: 1300 }"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
          @change="handleTableChange">

          <span slot="degree" slot-scope="text">
            <a-badge :color="text | statusTypeFilter" :text="text | statusFilter"/>
          </span>

          <span slot="follow" slot-scope="text, record"> 
            {{ record.recommend_follow == 1 ? '是' : '否' }}
          </span>

          <span slot="status" slot-scope="text, record"> 
            {{ record.interviewStatus == 1 ? '已访谈' : record.interviewStatus == 2 ? '已解除' : '未访谈' }}
          </span>

          <span slot="action" slot-scope="text, record">
            <a @click="handleView(record)">查看报告</a>
          </span>
        </a-table>
      </div>
      <!-- table区域-end -->
      <!-- 表单区域 -->
      <DxWarningModal ref="modalForm" :measureTree="measureTree" @ok="modalFormOk"/>
    </a-card>
    <!-- 打印表单页 -->
    <report-common-form ref="ReportForm"></report-common-form>
  </div>
</template>
<script>
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import DxWarningModal from './modules/DxWarningModal'
  import { getAction } from '@/api/manage'
  import ReportCommonForm from '@/components/report/ReportCommonForm'

  const degreeMap = {
    0: {
      color: '#D9D9D9',
      text: '未答完'
    },
    1: {
      color: 'green',
      text: '正常'
    },
    2: {
      color: 'yellow',
      text: '轻度'
    },
    3: {
      color: 'red',
      text: '中度'
    },
    4: {
      color: 'purple',
      text: '重度'
    },
    5: {
      color: '#D9D9D9',
      text: '未知'
    }
  }

  export default {
    name: 'DxWarningList',
    mixins: [JeecgListMixin],
    props: {
      isOnly: {
        type: Boolean,
        default: false
      },

      isShow: {
        type: Boolean,
        default: true
      }
    },
    components: {
      DxWarningModal,
      ReportCommonForm
    },
    data() {
      return {
        superFieldList:[],
        description: '预警管理',
        measureTree: [],
        // 表头 被试者姓名、年级、班级、预警程度（重度、中度）、活动名称、量表名称、是否推荐关注、预警时间、访谈状态（未访谈、已访谈、已解除）
        // 查询条件：预警程度、活动名称、量表名称、因子名称、是否推荐关注、预警时间、访谈状态
        columns: [
          {
            title: '被试者姓名',
            align: 'center',
            dataIndex: 'userName',
          },
          {
            title: '年级',
            align: 'center',
            dataIndex: 'gradeName'
          },
          {
            title: '班级',
            align: 'center',
            dataIndex: 'className'
          },
          {
            title: '预警程度',
            align: 'center',
            dataIndex: 'degree',
            scopedSlots: { customRender: 'degree' },
          },
          {
            title: '活动名称',
            align: 'center',
            dataIndex: 'taskName',
          },
          {
            title: '量表名称',
            align: 'center',
            dataIndex: 'measureName',
          },
          // {
          //   title: '是否推荐关注',
          //   align: 'center',
          //   dataIndex: 'recommend_follow',
          //   scopedSlots: { customRender: 'follow' },
          // },
          {
            title: '预警时间',
            align: 'center',
            dataIndex: 'waringTime',
          },
          {
            title: '访谈状态',
            align: 'center',
            dataIndex: 'interviewStatus',
            scopedSlots: { customRender: 'status' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' },
          }
        ],
        // 请求参数
        url: {
          // list: '/diagnosis/dxResult/defaultList',
          list: '/diagnosis/dxResult/selectEarlyWarningList',
          delete: '/diagnosis/dxResult/delete',
          deleteBatch: '/diagnosis/dxResult/deleteBatch',
          exportXlsUrl: 'diagnosis/dxResult/exportXls',
          exportBatch: 'diagnosis/dxResult/exportBatch',
          importExcelUrl: '/diagnosis/dxResult/importExcel',
          treeList: '/psychology/psCategory/queryTreeSelectList',
          exportDocUrl: 'diagnosis/dxResult/exportDoc',
          goContinueQuestionPage: '/diagnosis/dxResult/goContinueQuestionPage',
          exportOptionXls: '/diagnosis/dxResult/exportOptionXls'
        }
      }
    },
    filters: {
      statusFilter(type) {
        return degreeMap[type].text
      },
      statusTypeFilter(type) {
        return degreeMap[type].color
      }
    },
    created() {
      this.loadTreeData()
      // this.loadData()
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      onDateChange(value, dateString) {
        this.queryParam.beginWaringDate = dateString[0]
        this.queryParam.endWaringDate = dateString[1]
      },
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams()//查询条件
        if (params.measureId) {
          params.measureId = params.measureId.split(',')[0]
        }
        if (this.isOnly) params.userId = this.$store.getters.patientDetail.id
        this.loading = true
        if (params.waringTime) params.waringTime = params.waringTime.format('YYYY-MM-DD')
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records
            this.ipagination.total = res.result.total
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      loadTreeData() {
        getAction(this.url.treeList, null).then((res) => {
          if (res.success) {
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              this.measureTree.push(temp)
            }
            this.loading = false
          }
        })
      },
      initDictConfig() {
      },
      handleView(record){
        this.$refs.ReportForm.edit(record)
        this.$refs.ReportForm.title = '内容预览'
      },
      batchEdit() { // 专家鉴别
        this.$refs.modalForm.add(this.selectedRowKeys)
        this.$refs.modalForm.title = '修改状态'
      }
    }
  }
</script>
<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }
</style>