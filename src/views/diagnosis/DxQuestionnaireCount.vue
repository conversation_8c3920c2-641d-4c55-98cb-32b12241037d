<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="问卷名称">
              <a-input placeholder="请输入问卷名称" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="问卷对象">
              <a-input placeholder="请输入问卷对象" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">发布问卷</a-button>
      <a-button type="primary" icon="download" style="margin-left: 8px" @click="handleExportXls('问卷统计')">导出</a-button>              
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange">
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <DxQuestionnaireCountModal ref="modalForm" @ok="modalFormOk" :psCategorys="psCategorys"></DxQuestionnaireCountModal>
  </a-card>
</template>

<script>
  import DxQuestionnaireCountModal from './modules/DxQuestionnaireCountModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'DxQuestionnaireCount',
    mixins: [JeecgListMixin],
    components: {
      DxQuestionnaireCountModal
    },
    data() {
      return {
        description: '问卷统计',
        // 收藏题目、人口属性、用户联系方式、满意度
        psCategorys: [
          {
            id: 1,
            name: '收藏题目'
          },
          {
            id: 2,
            name: '人口属性'
          },
          {
            id: 3,
            name: '用户联系方式'
          },
          {
            id: 4,
            name: '满意度'
          }
        ],
        // 表头 题目类型、问卷名称、问卷对象、答题用时、提交时间、数据来源
        // 查询条件：题目类型、问卷名称、问卷对象
        columns: [
          {
            title: '问卷名称',
            align: 'center',
            dataIndex: 'measureName'
          },
          {
            title: '问卷对象',
            align: 'center',
            dataIndex: 'userName'
          },
          {
            title: '答题状态',
            align: 'center',
            dataIndex: 'status_dictText',
          },
          {
            title: '预警状态',
            align: 'center',
            dataIndex: 'degree_dictText',
          },
          {
            title: '答题用时',
            align: 'center',
            dataIndex: 'timeStr',
          },
          {
            title: '提交时间',
            align: 'center',
            dataIndex: 'updateTime',
            customRender: function (text, record) {
              if (record.timeStr) {
                return text
              } else {
                return ''
              }
            }
          },
        ],
        url: {
          list: '/hk/result/defaultList',
          delete: '/psychology/psMeasure/delete',
          deleteBatch: '/psychology/psMeasure/deleteBatch',
          exportXlsUrl: 'psychology/psMeasure/exportXls',
          importExcelUrl: 'psychology/psMeasure/importExcel'
        }
      }
    },
    created() {
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      handleAdd() {
        this.$refs.modalForm.add();
        this.$refs.modalForm.title = "发布问卷";
      }
    }
  }
</script>
<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }
</style>