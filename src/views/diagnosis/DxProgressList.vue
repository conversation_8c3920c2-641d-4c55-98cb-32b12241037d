<template>
  <a-card :bordered="false">
    <!-- 查询区域 年级、班级、量表名称、完成情况 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :lg="6" :sm="8">
            <a-form-item label="量表名称">
              <j-input placeholder="请输入量表名称" v-model="queryParam.measureNames"></j-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8"> 
            <a-form-item label="完成情况">
              <a-select placeholder="请选择完成情况" v-model="queryParam.finishStatus">
                <a-select-option value="0">未开始</a-select-option>
                <a-select-option value="1">进行中</a-select-option>
                <a-select-option value="2">已完成</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- table区域-begin -->
    <div>
      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange">
        <template slot="finishStatus" slot-scope="text, record">
          {{ record.finishStatus == 0 ? '未开始' : record.finishStatus == 1 ? '进行中' : '已完成' }}
        </template>

        <span slot="progress" slot-scope="text, record">
          <a-progress :percent="record.finishUserRatio" />
        </span>

      </a-table>
    </div>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'DxProgressList',
  mixins: [JeecgListMixin, mixinDevice],
  data () {
    return {
      description: '活动进度',
      // 表头 活动名称、年级、班级、量表名称、完成情况、总人数、已测人数、未测人数、测试进度百分比（图形加数字）、完成时间
      // 查询条件：年级、班级、量表名称、完成情况
      columns: [
        {
          title: '活动名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '量表名称',
          align: 'center',
          dataIndex: 'measureNames'
        },
        {
          title: '完成情况',
          align: 'center',
          dataIndex: 'finishStatus',
          scopedSlots: { customRender: 'finishStatus' }
        },
        {
          title: '总人数',
          align: 'center',
          dataIndex: 'totalUserCount'
        },
        {
          title: '已测人数',
          align: 'center',
          dataIndex: 'finishUserCount'
        },
        {
          title: '未测人数',
          align: 'center',
          dataIndex: 'unFinishUserCount'
        },
        {
          title: '测试进度',
          align: 'center',
          dataIndex: 'finishUserRatio',
          scopedSlots: { customRender: 'progress' }
        },
        {
          title: '完成时间',
          align: 'center',
          dataIndex: 'finishTime',
        },
      ],
      url: {
        list: "/psEvaluation/list",
      },
      dictOptions: {},
      superFieldList: [],
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>