<template>
  <div>
    <a-card :bordered="false">
      <!-- 查询区域 访谈主题，访谈人员，访谈时间，访谈结果 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col :span="6">
              <a-form-item label="访谈主题">
                <j-input placeholder="请输入访谈主题" v-model="queryParam.talkTheme"></j-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="访谈人员">
                <j-input placeholder="请输入访谈人员" v-model="queryParam.patientName"></j-input>
              </a-form-item>
            </a-col>
<!--            <a-col :span="6">-->
<!--              <a-form-item label="访谈时间">-->
<!--                <a-date-picker v-model="queryParam.talkTime" format="YYYY-MM-DD" placeholder="请选择访谈时间" />-->
<!--              </a-form-item>-->
<!--            </a-col>-->
            <a-col :span="6">
              <a-form-item label="访谈结果">
                <j-input placeholder="请输入访谈结果" v-model="queryParam.infoDetail"></j-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="table-operator">
        <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay">
            <a-menu-item key="1" @click="batchDel">
              <a-icon type="delete"/>
              删除
            </a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"> 批量操作
            <a-icon type="down"/>
          </a-button>
        </a-dropdown>
      </div>

      <!-- table区域-begin -->
      <div>

        <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
          <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length
          }}</a>项
          <a style="margin-left: 24px" @click="onClearSelected">清空</a>
        </div>

        <a-table
          ref="table"
          size="middle"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
          @change="handleTableChange">

          <span slot="action" slot-scope="text, record">
<!--            <a @click="handleInterview(record)">开始访谈</a>-->
<!--            <a-divider type="vertical"/>-->
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical"/>
            <a @click="handleDetail(record)">详情</a>
          </span>
        </a-table>
      </div>
      <!-- table区域-end -->
      <!-- 表单区域 -->
      <DxInterviewsModal ref="modalForm" :measureTree="measureTree" @ok="modalFormOk"/>
    </a-card>
  </div>
</template>
<script>
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import DxInterviewsModal from './modules/DxInterviewsModal'
  import { getAction } from '@/api/manage'
  import ReportCommonForm from '@/components/report/ReportCommonForm'

  export default {
    name: 'DxInterviews',
    mixins: [JeecgListMixin],
    props: {
      isOnly: {
        type: Boolean,
        default: false
      },

      isShow: {
        type: Boolean,
        default: true
      }
    },
    components: {
      DxInterviewsModal,
      ReportCommonForm
    },
    data() {
      return {
        superFieldList:[],
        description: '访谈评估',
        measureTree: [],
        // 表头 访谈主题，访谈人员、访谈时间，访谈内容，访谈结果
        // 查询条件：访谈主题，访谈人员，访谈时间，访谈结果
        columns: [
          {
            title: '访谈主题',
            align: 'center',
            dataIndex: 'talkTheme',
          },
          {
            title: '访谈人员',
            align: 'center',
            dataIndex: 'patientName',
          },
          {
            title: '访谈时间',
            align: 'center',
            dataIndex: 'talkTime',
          },
          {
            title: '访谈内容',
            align: 'center',
            dataIndex: 'talkContent',
          },
          {
            title: '访谈结果',
            align: 'center',
            dataIndex: 'infoDetail',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' },
          }
        ],
        // 请求参数
        url: {
          // list: '/diagnosis/dxResult/defaultList',
          list: '/pa/talkRecord/list',
          delete: '/pa/talkRecord/delete',
          deleteBatch: '/pa/talkRecord/deleteBatch',
        }
      }
    },
    created() {
    },
    methods: {
      handleInterview(record) {
        this.$refs.modalForm.edit(record);
        this.$refs.modalForm.title = "开始访谈";
        this.$refs.modalForm.disableSubmit = false;
        this.$refs.modalForm.isInterview = true;
      }
    }
  }
</script>
<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }
</style>