<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="题目类型">
              <a-select
                style="width: 100%"
                placeholder="请选择题目类型"
                v-model="queryParam.categoryId">
                <a-select-option v-for="category in psCategorys" :key="category.id">
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="问卷名称">
              <a-input placeholder="请输入问卷名称" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical"/>
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="goQuesionList(record.id)">问题详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <DxQuestionnaireModal ref="modalForm" @ok="modalFormOk" :psCategorys="psCategorys"></DxQuestionnaireModal>
  </a-card>
</template>

<script>
  import DxQuestionnaireModal from './modules/DxQuestionnaireModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { findHkCategoryAll } from '@/api/api'

  export default {
    name: 'DxQuestionnaire',
    mixins: [JeecgListMixin],
    components: {
      DxQuestionnaireModal
    },
    data() {
      return {
        description: '问卷设计',
        // 收藏题目、人口属性、用户联系方式、满意度
        psCategorys: [],
        // 表头 题目类型、问卷名称、问卷介绍、指导语
        // 查询条件：题目类型、问卷名称
        columns: [
          {
            title: '题目类型',
            align: 'center',
            dataIndex: 'categoryName'
          },
          {
            title: '问卷名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '问卷介绍',
            align: 'center',
            dataIndex: 'briefIntroduction',
          },
          {
            title: '指导语',
            align: 'center',
            dataIndex: 'description',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: '/hk/homework/list',
          delete: '/hk/homework/delete',
          deleteBatch: '/hk/homework/deleteBatch',
        },
      }
    },
    created() { 
      this.loadPsCategory()
    },

    methods: {
      /**
       * 初始化量表类别下拉选
       */
      loadPsCategory() {
        findHkCategoryAll({}).then((res) => {
          if (res.success) {
            this.psCategorys = res.result
          } else {
            // console.log(res.message)
          }
        })
      },
      goQuesionList(id) {
        this.$router.push({ path: '/psychology/PsQuesionList/' + id })
      }
    }
  }
</script>
<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }
</style>