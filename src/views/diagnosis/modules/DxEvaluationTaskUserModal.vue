<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <!-- 主表单区域 -->
      <a-form :form="form">
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="追加测评对象">
              <j-select-patient v-decorator="['patientName']" @back="backPatientInfo" :backInfo="true"></j-select-patient>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import Vue from 'vue'
  import { USER_ROLE } from '@/store/mutation-types'
  import pick from 'lodash.pick'
  import moment from 'moment'
  import { getAction, httpAction } from '@/api/manage'
  import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'

  export default {
    name: 'DxEvaluationTaskUserModal',

    props: {
      measureTree: {
        type: Array,
        default: () => []
      }
    },

    components: { JLabelSelect },
    data() {
      return {
        title: '添加测评人',
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        disableSubmit: false,
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        resultMainModel: { },
        url: {
          queryById: '/psEvaluation/queryById',
          add: '/psEvaluation/addUser',
          edit: '/psEvaluation/edit',
          listAll: '/psychology/psMeasure/listAll',
          list: '/wx/wxAuthorizationRecords/list2',
        },
        hasRole1: Vue.ls.get(USER_ROLE, []).includes('myyywswbi'),
        hasTimeStr: false,
        measures: [],
        measurePackages: []
      }
    },
    methods: {
      moment,
      backPatientInfo(info) {
        this.form.setFieldsValue({
          patientName: info.map(item => item.text).join(","),
        })
        this.resultMainModel.userIdList = info.map(item => item.value)
      },
      add(id) {
        this.resultMainModel.id = id
        this.edit()
      },
      handleCancel() {
        this.close()
      },
      edit() {
        this.form.resetFields()
        this.visible = true
      },
      // 确定
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            console.log('values=======', values)
            let quesionData = Object.assign(this.resultMainModel, values)
            let formData = {
              ...quesionData,
            }
            httpAction(this.url.add, formData, 'post').then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      close() {
        this.$emit('close')
        this.disableSubmit = false
        this.visible = false
      }
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>