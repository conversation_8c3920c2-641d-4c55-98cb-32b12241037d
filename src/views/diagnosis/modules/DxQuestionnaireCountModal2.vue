<template>
  <a-modal
    :title='title'
    :width='800'
    :visible='visible'
    :confirmLoading='confirmLoading'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>

    <a-spin :spinning='confirmLoading'>
      <!-- 问卷名称（下拉选择，多选，选择问卷设计内的问卷）问卷对象（下拉单选角色信息），答题方式（匿名扫码作答），下发时间，结束时间 -->
      <a-form :form='form'>
        <a-form-item label='问卷名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-select
            mode='multiple'
            style='width: 100%'
            placeholder='请选择问卷名称'
            v-model='selectedCategory'>
            <a-select-option v-for='category in psCategorys' :key='category.id'>
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="问卷对象">
          <j-select-patient v-decorator="['patientName']" @back="backPatientInfo" :backInfo="true"
            :isRadio="true"></j-select-patient>
        </a-form-item>
        <a-form-item
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
          label='答题方式'>
          <a-select
            style='width: 100%'
            placeholder='请选择问卷名称'
            default-value="1"
            disabled>
            <a-select-option value="1">
              匿名扫码作答
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="下发时间">
          <a-date-picker v-decorator="['startTime', {}]" format="YYYY-MM-DD" placeholder="请选择下发时间" style="width: 100%" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="结束时间">
          <a-date-picker v-decorator="['endTime', {}]" format="YYYY-MM-DD" placeholder="请选择结束时间" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'

export default {
  name: 'DxQuestionnaireCountModal',
  props: {
    psCategorys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      measureShow: true,
      selectedCategory: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {},
      url: {
        add: '/psychology/psMeasure/add',
        edit: '/psychology/psMeasure/edit'
      }
    }
  },
  created() {
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      if (this.model.categoryId) {
        this.selectedCategory = this.model.categoryId.split(',')
      } else {
        this.selectedCategory = []
      }
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name', 'description', 'briefIntroduction'))
        //时间格式化
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.measureShow = true
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          formData.categoryId = this.selectedCategory.length > 0 ? this.selectedCategory.join(',') : ''
          //时间格式化
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    handleChangeType(e) {
      this.model.type = e
      if (e == '2') {
        this.measureShow = false
      }
    },
    backPatientInfo(info) {
      this.form.setFieldsValue({
        patientName: info[0].text,
      })
      this.model.patientId = info[0].value
    },
  }
}
</script>

<style scoped>

</style>