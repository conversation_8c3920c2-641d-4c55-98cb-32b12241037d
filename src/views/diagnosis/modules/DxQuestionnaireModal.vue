<template>
  <a-modal
    :title='title'
    :width='800'
    :visible='visible'
    :confirmLoading='confirmLoading'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>

    <a-spin :spinning='confirmLoading'>
      <!-- 题目类型（下拉选择、单选：收藏题目、人口属性、用户联系方式、满意度）、问卷名称、问卷介绍、指导语 -->
      <a-form :form='form'>
        <a-form-item label='题目类型' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-select
            style='width: 100%'
            placeholder='请选择题目类型'
            v-decorator="['categoryId', {rules: [{ required: true, message: '请选择题目类型!' }]}]">
            <a-select-option v-for='category in psCategorys' :key='category.id'>
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
          label='问卷名称'>
          <a-input placeholder='请输入问卷名称'
                   v-decorator="['name', {rules: [{ required: true, message: '请输入问卷名称!' }]}]" />
        </a-form-item>
        <a-form-item
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
          label='问卷介绍'>
          <a-textarea placeholder='请输入问卷介绍' v-decorator="['briefIntroduction', {}]" auto-size />
        </a-form-item>
        <a-form-item
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
          label='指导语'>
          <a-textarea placeholder='请输入指导语' v-decorator="['description', {}]" auto-size />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'

export default {
  name: 'DxQuestionnaireModal',
  props: {
    psCategorys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      measureShow: true,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {},
      url: {
        add: '/hk/homework/add',
        edit: '/hk/homework/edit'
      }
    }
  },
  created() {
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name', 'categoryId', 'description', 'briefIntroduction'))
        //时间格式化
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.measureShow = true
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          //时间格式化
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    handleChangeType(e) {
      this.model.type = e
      if (e == '2') {
        this.measureShow = false
      }
    }
  }
}
</script>

<style scoped>

</style>