<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <!-- 主表单区域 访谈主题（文本框），访谈人员（选择学生，单选），访谈时间（格式yyyy- mm-dd  hh-mm），访谈内容（多行文本框），访谈结果（文本框） -->
      <a-form :form="form">
        <template v-if="!isInterview || !disableSubmit">
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="访谈主题">
            <a-input :disabled="disableSubmit" placeholder="请输入访谈主题" v-decorator="['talkTheme', {}]"/>
          </a-form-item>
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="访谈人员">
            <j-select-patient v-decorator="['patientName', {}]" @back="backPatientInfo" :backInfo="true"
              :disabled="disableSubmit" :isRadio="true"></j-select-patient>
          </a-form-item>
          <a-form-item label="访谈时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-date-picker showTime format='YYYY-MM-DD HH:mm:ss' :disabled="disableSubmit" v-decorator="[ 'talkTime', {}]" style="width: 100%" />
          </a-form-item>
        </template>
        
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="访谈内容">
          <a-textarea row="5" v-decorator="['talkContent', {}]" :disabled="disableSubmit" placeholder="请输入访谈内容"></a-textarea>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="访谈结果">
          <a-input :disabled="disableSubmit" placeholder="请输入访谈结果" v-decorator="['infoDetail', {}]"/>
        </a-form-item>
      </a-form>

<!--      <div class="table-operator" v-if="isInterview">-->
<!--        <a-button @click="handleRemoveWarning" type="primary">解除预警</a-button>-->
<!--      </div>-->

<!--      <a-table-->
<!--        v-if="isInterview || disableSubmit"-->
<!--        ref="table"-->
<!--        size="middle"-->
<!--        bordered-->
<!--        rowKey="resultId"-->
<!--        :columns="columns"-->
<!--        :dataSource="warningList"-->
<!--        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"-->
<!--        :pagination="false">-->
<!--        <span slot="degree" slot-scope="text">-->
<!--          <a-badge :color="text | statusTypeFilter" :text="text | statusFilter"/>-->
<!--        </span>-->

<!--        <span slot="status" slot-scope="text, record"> -->
<!--          未处理-->
<!--        </span>-->
<!--      </a-table>-->
    </a-spin>
  </a-modal>
</template>

<script>
  import pick from 'lodash.pick'
  import moment from 'moment'
  import { httpAction, getAction } from '@/api/manage'

  const degreeMap = {
    0: {
      color: '#D9D9D9',
      text: '未答完'
    },
    1: {
      color: 'green',
      text: '正常'
    },
    2: {
      color: 'yellow',
      text: '轻度'
    },
    3: {
      color: 'red',
      text: '中度'
    },
    4: {
      color: 'purple',
      text: '重度'
    },
    5: {
      color: '#D9D9D9',
      text: '未知'
    }
  }

  export default {
    name: 'DxInterviewsModal',
    data() {
      return {
        title: '操作',
        // 新增时子表默认添加几行空数据
        disableSubmit: false,
        isInterview: false,
        visible: false,
        model: {},
        confirmLoading: false,
        form: this.$form.createForm(this),
        warningList: [],
        /* table选中keys*/
        selectedRowKeys: [],
        /* table选中records*/
        selectionRows: [],
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        url: {
          add: '/pa/talkRecord/add',
          edit: '/pa/talkRecord/edit',
          queryById: '/pa/talkRecord/findWarningList',
          removeWarning: '/pa/talkRecord/removeWarningWarning'
        },
        // 姓名、量表名称、预警程度、评估状态
        columns: [
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'patientName',
          },
          {
            title: '量表名称',
            align: 'center',
            dataIndex: 'measureName',
          },
          {
            title: '预警程度',
            align: 'center',
            dataIndex: 'degree',
            scopedSlots: { customRender: 'degree' },
          },
          {
            title: '评估状态',
            align: 'center',
            dataIndex: 'interviewStatus',
          },
        ],
      }
    },
    filters: {
      statusFilter(type) {
        return degreeMap[type].text
      },
      statusTypeFilter(type) {
        return degreeMap[type].color
      }
    },
    methods: {
      moment,
      add() {
        this.edit({})
      },
      handleCancel() {
        this.close()
      },
      edit(record) {
        this.model = Object.assign({}, record);
        this.model.patientName = ''
        this.visible = true
        this.$nextTick(() => {
          if (record.patientName) this.model.patientName = record.patientName
          this.form.setFieldsValue(pick(this.model, 'talkTheme', 'talkContent', 'infoDetail', 'patientName'))
          //时间格式化
          this.form.setFieldsValue({talkTime: this.model.talkTime ? moment(this.model.talkTime, 'YYYY-MM-DD HH:mm:ss') : null})
          if (this.model.id && (this.disableSubmit || this.isInterview)) {
            this.getList()
          }
        })
      },
      getList() { 
        getAction(this.url.queryById, { patientId: this.model.patientId }).then(res => {
          this.warningList = res.result || []
        })
      },
      onSelectChange(selectedRowKeys, selectionRows) {
        this.selectedRowKeys = selectedRowKeys;
        this.selectionRows = selectionRows;
      },
      handleRemoveWarning() {
        if (!this.selectedRowKeys.length) {
          this.$message.warning('请选择至少一条记录！')
          return
        }
        getAction(this.url.removeWarning, { talkId: this.model.id,resultIds: this.selectedRowKeys.join(',') }).then(res => { 
          if (res.success) { 
            this.$message.success('解除预警成功！')
            this.getList()
          }
        })
      },
      // 确定
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let quesionData = Object.assign(this.model, values)
            let formData = {
              ...quesionData,
            }
            //时间格式化
            formData.talkTime = formData.talkTime ? formData.talkTime.format('YYYY-MM-DD HH:mm:ss') : null;
            formData.type = '0'
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      backPatientInfo (info) {
        this.model.patientId = info[0].value
        this.model.patientName = info[0].text
      },
      close() {
        this.$emit('close')
        this.disableSubmit = false
        this.visible = false
        this.form.resetFields()
      }
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>