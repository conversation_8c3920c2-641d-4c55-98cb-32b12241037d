<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <!-- 主表单区域 -->
      <a-form :form="form">
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="访问状态">
              <a-select v-decorator="['status', {}]" placeholder="请选择访谈状态">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="0">未访谈</a-select-option>
                <a-select-option value="1">访谈中</a-select-option>
                <a-select-option value="2">访谈完成</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import Vue from 'vue'
  import { USER_ROLE } from '@/store/mutation-types'
  import pick from 'lodash.pick'
  import moment from 'moment'
  import { getAction, httpAction } from '@/api/manage'
  import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'

  export default {
    name: 'RiskAssessmentModal',

    props: {
      measureTree: {
        type: Array,
        default: () => []
      }
    },

    components: { JLabelSelect },
    data() {
      return {
        title: '操作',
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        disableSubmit: false,
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        resultMainModel: { dxResultOptionList: [{}] },
        url: {
          add: '/diagnosis/dxResult/add',
          edit: '/diagnosis/dxResult/edit',
        },
        hasRole1: Vue.ls.get(USER_ROLE, []).includes('myyywswbi'),
        hasTimeStr: false,
        measures: [],
      }
    },
    methods: {
      moment,
      add(resultIds) {
        this.resultMainModel.resultIds = resultIds
        this.edit({})
      },
      handleCancel() {
        this.close()
      },
      edit(record) {
        this.form.resetFields()
        this.resultMainModel = Object.assign({}, record)
        this.visible = true
      },
      // 确定
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.resultMainModel.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            console.log('values=======', values)
            let quesionData = Object.assign(this.resultMainModel, values)
            let formData = {
              ...quesionData,
            }
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      close() {
        this.$emit('close')
        this.disableSubmit = false
        this.visible = false
      }
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>