<template>
  <a-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="选择量表">
          <a-tree-select
            showSearch
            style="width:100%"
            :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
            :treeData="measureTree"
            v-model="model.measureId"
            treeNodeFilterProp="title"
            placeholder="请选择量表">
          </a-tree-select>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="分数范围，前数">
          <a-input-number v-decorator="[ 'scoreRangeBefor', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="分数范围，后数">
          <a-input-number v-decorator="[ 'scoreRangeAfter', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="报告内容(文本)">
          <a-input placeholder="请输入报告内容" v-decorator="['contentStr', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          style="min-height: 600px;">
          <span slot="label">
            报告内容&nbsp;
            <a-tooltip title="模板中的动态数据项目使用 ${} 格式呈现，在诊断报告中会自动替换成具体的报告数据，例如${userName}会被替换为'张三'">
              <a-icon type="question-circle" />
            </a-tooltip>
          </span>
          <j-editor :j-height="600" v-model="model.content"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="报告内容顺序">
          <a-input placeholder="请输入报告内容顺序" v-decorator="['sort', {}]"/>
        </a-form-item>
      </a-form>
    </a-spin>

  </a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import JEditor from '@/components/jeecg/JEditor'

export default {
  name: 'DxResultReportModal',
  components: {
    JEditor
  },
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      measureTree: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        delFlag: { rules: [{ required: true, message: '请输入删除状态（0，正常，1已删除）!' }] }
      },
      url: {
        add: '/diagnosis/dxResultReport/add',
        edit: '/diagnosis/dxResultReport/edit',
        queryById: '/diagnosis/dxResultReport/queryById',
        treeList: '/psychology/psCategory/queryTreeSelectList'
      }
    }
  },
  created() {
    this.loadTreeData()
  },
  methods: {
    loadTreeData() {
      getAction(this.url.treeList, null).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            this.measureTree.push(temp)
          }
          this.loading = false
        }
      })
    },
    loadResultReportData(id) {
      getAction(this.url.queryById, { id: id }).then((res) => {
        if (res.success) {
          if (res.result.measureId) {
            res.result.measureId = res.result.measureId + ',' + res.result.categoryId
          }
          this.model = res.result
        }
      })
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'scoreRangeBefor', 'scoreRangeAfter', 'contentStr', 'sort'))
        //时间格式化
      })
      if (this.model.id) {
        this.loadResultReportData(this.model.id)
      }
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          if (formData.measureId) {
            formData.measureId = formData.measureId.split(',')[0]
          }
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style scoped>

</style>