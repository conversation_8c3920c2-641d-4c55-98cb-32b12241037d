<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <!-- 主表单区域 -->
      <a-form :form="form">
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="任务名称">
              <a-input placeholder="请输入任务名称" v-decorator="['name', {}]" :disabled="disableSubmit"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="任务说明">
              <a-textarea :rows="2" v-decorator="['details', {}]" placeholder="请输入任务说明" :disabled="disableSubmit"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="开始时间">
              <a-date-picker show-time v-decorator="['beginTime', {}]" format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开始时间" style="width: 100%" :disabled="disableSubmit" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="结束时间">
              <a-date-picker
                show-time
                v-decorator="['finishTime', {}]"
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择结束时间"
                style="width: 100%"
                :disabled="disableSubmit"
                :disabledDate="disabledFinishDate"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="量表类型">
              <a-radio-group v-decorator="['type', {initialValue: '1'}]" :disabled="!!resultMainModel.id">
                <a-radio value="1">量表</a-radio>
                <a-radio value="2">量表包</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="form.getFieldValue('type')!='2'">
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="选择量表">
              <a-select mode="multiple" show-search placeholder="请选择量表" option-filter-prop="children" style="width: 100%" :filter-option="filterOption" v-decorator="['measureIdList', {}]" :disabled="!!resultMainModel.id">
                <a-select-option v-for="measure in measures" :value="measure.id" :key="measure.id">
                  {{ measure.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>        
        <a-row v-else>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="选择量表包">
              <a-select mode="multiple" show-search placeholder="请选择量表包" option-filter-prop="children" style="width: 100%" :filter-option="filterOption" v-decorator="['measurePackageIdList', {}]" :disabled="!!resultMainModel.id">
                <a-select-option v-for="measure in measurePackages" :value="measure.id" :key="measure.id">
                  {{ measure.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="报告查看权限">
              <a-switch v-decorator="['reportShow', { valuePropName: 'checked', initialValue: true }]" :disabled="!!resultMainModel.id" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="测评对象">
              <j-select-patient v-decorator="['patientName']" @back="backPatientInfo" :backInfo="true"
                :disabled="!!resultMainModel.id"></j-select-patient>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import Vue from 'vue'
  import { USER_ROLE } from '@/store/mutation-types'
  import pick from 'lodash.pick'
  import moment from 'moment'
  import { getAction, httpAction } from '@/api/manage'
  import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'

  export default {
    name: 'DxEvaluationTaskModal',

    props: {
      measureTree: {
        type: Array,
        default: () => []
      }
    },

    components: { JLabelSelect },
    data() {
      return {
        title: '操作',
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        disableSubmit: false,
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        resultMainModel: { },
        url: {
          queryById: '/psEvaluation/queryById',
          add: '/psEvaluation/add',
          edit: '/psEvaluation/edit',
          listAll: '/psychology/psMeasure/listAll',
          list: '/wx/wxAuthorizationRecords/list2?isTop=1',
        },
        hasRole1: Vue.ls.get(USER_ROLE, []).includes('myyywswbi'),
        hasTimeStr: false,
        measures: [],
        measurePackages: []
      }
    },
    methods: {
      moment,
      /**
       * 初始化量表下拉选
       */
      loadPsTemplate () {
        getAction(this.url.listAll, {}).then((res) => {
          if (res.success) {
            this.measures = res.result
          }
        })
        getAction(this.url.list, {pageNo: 1, pageSize: 10000}).then((res) => {
          if (res.success) {
            this.measurePackages = res.result.records
          }
        })
      },
      filterOption (input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
      disabledFinishDate(current) {
        const beginTime = this.form.getFieldValue('beginTime');
        if (!beginTime) {
          return false;
        }
        return current < moment(beginTime).startOf('day');
      },
      backPatientInfo(info) {
        this.form.setFieldsValue({
          patientName: info.map(item => item.text).join(","),
        })
        this.resultMainModel.userIdList = info.map(item => item.value)
      },
      add(resultId) {
        this.resultMainModel.resultId = resultId
        this.edit({})
      },
      handleCancel() {
        this.close()
      },
      edit(record) {
        this.loadPsTemplate()
        this.form.resetFields()
        getAction(this.url.queryById, {id: record.id}).then((res) => {
          this.resultMainModel = Object.assign({}, res.result)
          console.log(this.resultMainModel)
          this.visible = true
          this.$nextTick(() => {
            this.form.setFieldsValue(pick(this.resultMainModel, 'name', 'type', 'details', 'measureIdList'))
            this.form.getFieldValue('measurePackageIdList')
            //时间格式化
            this.form.setFieldsValue({
              measurePackageIdList: this.resultMainModel.measurePackageIds ? [this.resultMainModel.measurePackageIds]: null,
              beginTime: this.resultMainModel.beginTime ? moment(this.resultMainModel.beginTime, 'YYYY-MM-DD HH:mm:ss') : null,
              finishTime: this.resultMainModel.finishTime ? moment(this.resultMainModel.finishTime, 'YYYY-MM-DD HH:mm:ss') : null,
              reportShow: !!this.resultMainModel.reportShow,
              patientName: this.resultMainModel.paPatientList && this.resultMainModel.paPatientList.map(item => item.name).join(',')
            })
          })
        })
      },
      // 确定
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.resultMainModel.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            console.log('values=======', values)
            let quesionData = Object.assign(this.resultMainModel, values)
            let formData = {
              ...quesionData,
            }
            formData.reportShow = formData.reportShow ? 1 : 0
            //时间格式化
            formData.beginTime = formData.beginTime ? formData.beginTime.format('YYYY-MM-DD HH:mm:ss') : null;
            formData.finishTime = formData.finishTime ? formData.finishTime.format('YYYY-MM-DD HH:mm:ss') : null;
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      close() {
        this.$emit('close')
        this.disableSubmit = false
        this.visible = false
      }
    }
  }
</script>


<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>