<template>
    <div class="page">
        <div class="box">
            <div class="left">
                <div v-for="(i, index) in list" :key="index" :class="activeindex == index ? 'active' : ''" @click="getChange(index)">
                    {{ i.name }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'list',
    components: {
    },
    data() {
        return {
            activeindex: '0',
            list: [{
                name: '个人信息',
            }, {
                name: '我的收藏',
            }, {
                name: '设置',
            }]
        }
    },
    filters: {

    },
    created() {

    },
    methods: {
        getChange(index) {
            this.activeindex = index
        },
        // 路由跳转
        getRouter() {
            this.$router.push({ path: '/appointment/detail' });
        },
    }
}
</script>

<style lang="less" scoped>
.box {
    padding: 21px 24px;
    background: #FFFFFF;

    .left {
        background: rgba(53, 162, 255, 0.1);
        border-radius: 6px;
        width: 225px;
        height: 696px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;

        div {
            margin-bottom: 42px;
            font-size: 16px;
            color: #333333;
            cursor: pointer;
        }

        .active {
            color: #35A2FF;
            border-radius: 27px;
            border: 1px solid #35A2FF;
            padding: 9px 20px;
        }
    }
}
</style>