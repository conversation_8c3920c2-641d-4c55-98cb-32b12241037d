<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    okText="完成"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <div :style="getBackgroupImg()" class="audio">
      <audio :src="voicePath" controls="controls" @canplay="getDuration" @timeupdate="updateTime"></audio>
    </div>
  </j-modal>
</template>

<script>
  import { getAction, getFileAccessHttpUrl, putAction } from '@/api/manage'

  export default {
    name: 'TrainModal',
    data() {
      return {
        title: '',
        voicePath: '',
        titleImg: '',
        width: 1200,
        visible: false,
        disableSubmit: false,
        duration: 0,
        currentTime: 0,
        schemeId: '',
        url: {
          radioPlayProgress: '/pa/paPatientScheme/radioPlayProgress'
        }
      }
    },
    methods: {
      view() {
        this.visible = true
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        this.putPlayProgress()
      },
      submitCallback() {
        this.$emit('ok')
        this.putPlayProgress()
        this.visible = false
      },
      handleCancel() {
        this.close()
        this.putPlayProgress()
      },
      getBackgroupImg() {
        return "background-image: url(" + this.titleImg + ")"
      },
      // 当前播放进度
      updateTime(e) {
        this.duration = e.target.duration
        this.currentTime = e.target.currentTime
      },
      // 获取音乐总长度
      getDuration(e) {
        this.duration = e.target.duration
        this.currentTime = e.target.currentTime
      },
      putPlayProgress() {
        if (!this.schemeId) return
        const data = {
          id: this.schemeId,
          radioPlayProgress: this.currentTime && this.duration ? (this.currentTime / this.duration).toFixed(2) * 100 : 0,
          radioPlayTime: this.currentTime ? Number(this.currentTime).toFixed(2) : 0
        }
				putAction(this.url.radioPlayProgress, data).then(res => {
					if (!res.success) {
						that.$message.error('上传失败')
					}
				})
			}
    }
  }
</script>
<style lang="less" scoped>
  .audio {
    -moz-background-size: 100% 100%;
    background-size: 100% 100%;
    height: 400px;

    audio {
      position: absolute;
      right: 6%;
      bottom: 20%;
    }
  }
</style>