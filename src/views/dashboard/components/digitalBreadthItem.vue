<template>
  <div class="digital-breadth">
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="readType == 2 ? question.imgPathGd : question.imgPath" type="audio/mpeg" />
    </audio>

    <p class="digital-breadth-title" v-html="question.title"></p>

    <div class="digital-breadth-btns">
      <div :class="['digital-breadth-btn', isPlay && 'no-choose']" v-for="item of 9" :key="item" @click="chooseItem(item)">{{ item }}</div>
    </div>

    <div class="digital-breadth-answer">
      <span class="answer-text">您的答案是：</span>

      <div class="answer-num" v-for="(item, index) of answer" :key="index">{{ item }}</div>
    </div>

    <div class="digital-breadth-footer">
      <a-button :class="[isPlay && 'no-choose']" @click="deleteItem">回删</a-button>
      <a-button :class="[isPlay && 'no-choose']" @click="confirm" type="primary">
        确定
        <a-icon type="arrow-right" />
      </a-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DigitalBreadthItem',

  props: {
    question: {
      type: Object,
      default: () => {}
    },

    audioChange: {
      type: Boolean,
      default: false
    },

    topicEnd: {
      type: Boolean,
      default: false
    },
    
    readType: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      answer: [],
      isPlay: true,
      timer: null
    }
  },

  methods: {
    chooseItem(item) {
      if (this.isPlay) return
      if (this.answer.length >= this.question.answer.split('-').length) {
        this.$message.warning(`本题答案数量最多为${ this.answer.length }个！`)
        return
      }
      this.answer.push(item)
    },

    deleteItem() {
      if (this.isPlay) return
      if (this.answer.length < 1) return
      this.answer.splice(this.answer.length - 1, 1)
    },

    confirm() {
      if (this.isPlay) return
      let score = 0
      if (this.answer.join('-') == this.question.answer) {
        score = 1
      }

      this.$emit('submit', { score, type: '8' })
    },

    handleEnd() {
      this.isPlay = false
    },

    palyAudio() {
      this.isPlay = true
      this.answer = []
      this.$refs.music && this.$refs.music.pause()

      this.$nextTick(() => {
        this.$refs.music.load()
        this.$refs.music.play()
      })
    },

    pauseAudio() {
      this.$refs.music && this.$refs.music.pause()
    }
  },

  watch: {
    topicEnd: {
      handler(newValue) {
        if (newValue) {
          clearTimeout(this.timer)

          this.timer = setTimeout(() => {
            this.palyAudio()
          }, 1200)
        }
      },
      immediate: true
    },
    audioChange(newValue) {
      if (newValue) {
        clearTimeout(this.timer)
        this.pauseAudio()
      }
    }
  }
}
</script>

<style lang="scss">
.digital-breadth {
  padding-left: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .digital-breadth-title {
    width: 100%;
    font-size: 28px;
    padding-bottom: 20px;
    margin: 0;
  }

  .digital-breadth-btns {
    width: 420px;
    height: 420px;
    margin-right: 100px;
    display: flex;
    flex-wrap: wrap;

    .digital-breadth-btn {
      width: 80px;
      height: 80px;
      margin: 30px;
      border-radius: 50%;
      background: linear-gradient(315deg, #86e720, #78c824);

      font-size: 36px;
      text-align: center;
      line-height: 80px;
      color: #fff;
      cursor: pointer;
    }

    .no-choose {
      cursor: not-allowed;
    }
  }

  .digital-breadth-answer {
    min-width: 750px;
    display: flex;
    align-items: center;
    padding-top: 30px;

    .answer-text {
      display: inline-block;
      font-size: 24px;
      line-height: 40px;
    }

    .answer-num {
      width: 40px;
      height: 40px;
      font-size: 24px;
      text-align: center;
      line-height: 40px;
      margin: 0 20px;
      border-bottom: 1px solid #979797;
    }
  }

  .digital-breadth-footer {
    padding-top: 30px;

    .ant-btn {
      height: 40px;
      margin: 0 10px;
      border-radius: 5px;
    }

    .no-choose {
      cursor: not-allowed;
    }
  }
}
</style>