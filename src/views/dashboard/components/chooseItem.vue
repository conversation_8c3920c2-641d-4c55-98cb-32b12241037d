<template>
  <div class="choose-item">
    <div class="choose-item-top">
      <p v-if="question.titleType" class="choose-item-title" v-html="question.title"></p>
      <img
        class="choose-item-img"
        v-if="question.imgPath"
        :src="question.imgPath"
      />
    </div>

    <div class="choose-item-answer">
      <div
        class="answer-item"
        v-for="(item, index) in question.psOptions"
        :key="index"
        :id="item.id"
        @click="chooseItem(item)"
      >
        <a-button type="primary" size="large">{{ item.title }}</a-button>
        <span v-if="item.type == 1" class="item-text">{{ item.content }}</span>
        <img
          class="item-img"
          v-if="item.type == 2"
          :src="item.content"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChooseItem',

  props: {
    question: {
      type: Object,
      default: () => {
      }
    }
  },

  data() {
    return {
      imgerver: window._CONFIG['domianURL']
    }
  },

  methods: {
    chooseItem(item) {
      this.$emit('submit', {optionId: item.id, type: item.type})
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-item {
  padding: 0 100px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .choose-item-top {
    width: 100%;
    min-height: 300px;
    text-align: center;

    .choose-item-title {
      width: 100%;
      margin: 0;
      padding: 0 150px 20px 150px;
      font-size: 28px;
      text-align: left;
    }

    .choose-item-img {
      width: 450px;
    }
  }

  .choose-item-answer {
    width: 100%;
    padding: 50px 130px 0 130px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .answer-item {
      display: flex;
      padding: 10px 20px 20px 20px;

      .ant-btn {
        height: 40px;
        margin-right: 10px;
      }

      .item-text {
        display: inline-block;
        font-size: 24px;
        line-height: 40px;
        cursor: pointer;
      }

      .item-img {
        width: 150px;
        cursor: pointer;
        background: #eee;
        border: 1px solid #eee;
      }
    }
  }
}
</style>