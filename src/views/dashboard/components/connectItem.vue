<template>
  <div class="connect-item">
    <p class="connect-title" v-html="question.title"></p>

    <div class="connect-main">
      <div class="clear-btn" @click="revoke">撤销</div>

      <div class="connect-img" ref="connectImg">
        <canvas id="mycanvas" width="1000" height="460"></canvas>
        <div
          :class="['main-item', 'main-item-' + item.index, choose.includes(item.index) && 'choose-item']"
          v-for="item in itemList"
          :key="item.index"
          :ref="`item${item.index}`"
          @click="clickItem(item.index)"
        >{{ item.text }}
        </div>
      </div>
    </div>

    <div class="connect-item-answer">
      <div
        class="answer-item"
        v-for="(item, index) in question.psOptions"
        :key="index"
        :id="item.id"
        @click="chooseItem(item.id)"
      >
        <a-button type="primary" size="large">{{ item.title }}</a-button>
        <span v-if="item.type == 1" class="item-text">{{ item.content }}</span>
        <img
          class="item-img"
          v-if="item.type == 2"
          :src="item.content"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import html2canvas from 'html2canvas'
import {ACCESS_TOKEN} from '@/store/mutation-types'

export default {
  name: 'ConnectItem',

  props: {
    question: {
      type: Object,
      default: () => {
      }
    }
  },

  data() {
    return {
      choose: [],
      itemList: [
        {
          index: 1,
          text: 1
        },
        {
          index: 2,
          text: '甲'
        },
        {
          index: 3,
          text: 2
        },
        {
          index: 4,
          text: '乙'
        },
        {
          index: 5,
          text: 3
        },
        {
          index: 6,
          text: '丙'
        },
        {
          index: 7,
          text: 4
        },
        {
          index: 8,
          text: '丁'
        },
        {
          index: 9,
          text: 5
        },
        {
          index: 10,
          text: '戊'
        }
      ],
      path: []
    }
  },

  methods: {
    clickItem(index) {
      if (!this.choose.length) {
        this.choose.push(index)
        return
      }

      const ref1 = this.$refs[`item${this.choose[this.choose.length - 1]}`][0]
      const x1 = (Number(ref1.offsetLeft) + Number(ref1.offsetHeight) / 2)
      const y1 = (Number(ref1.offsetTop) + Number(ref1.offsetWidth) / 2)
      const ref2 = this.$refs[`item${index}`][0]
      const x2 = (Number(ref2.offsetLeft) + Number(ref2.offsetHeight) / 2)
      const y2 = Number(ref2.offsetTop) + Number(ref2.offsetWidth) / 2
      this.drawLine(x1, y1, x2, y2)
      this.path.push({
        x1: x1,
        y1: y1,
        x2: x2,
        y2: y2
      })

      this.choose.push(index)
    },

    drawLine(x1, y1, x2, y2) {
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.save()
      ctx.beginPath() //不写每次都会重绘上次的线
      ctx.lineCap = "round"
      ctx.lineJoin = "round"

      ctx.moveTo(x1, y1)
      ctx.lineTo(x2, y2)
      ctx.closePath()
      ctx.strokeStyle = "#333"
      ctx.lineWidth = 2
      ctx.stroke()
      ctx.restore()
    },

    revoke() {
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, 1200, 700)
      this.choose.pop()
      this.path.pop()
      this.path.forEach(item => {
        this.drawLine(this.drawLine(item.x1, item.y1, item.x2, item.y2))
      })
    },

    async chooseItem(id) {
      let flieParam = new FormData()
      const img = await this.toImg()
      flieParam.append('file', new File([this.convertBase64UrlToBlob(img)], 'connectLine.png'))
      flieParam.append('biz', 'temp/image/connectLine')
      fetch(window._CONFIG['domianURL'] + '/sys/common/upload', {
        method: 'post',
        body: flieParam,
        headers: {'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)},
      })
        .then((response) => response.text())
        .then((result) => {
          const canvasImgUrl = JSON.parse(result).message
          this.$emit('submit', {optionId: id, answer: canvasImgUrl, type: '9'})
        })
    },

    // base64转formData
    convertBase64UrlToBlob(urlData) {
      //去掉url的头，并转换为byte
      var bytes = window.atob(urlData.split(',')[1]);
      //处理异常,将ascii码小于0的转换为大于0
      var ab = new ArrayBuffer(bytes.length);
      var ia = new Uint8Array(ab);
      for (var i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      return new Blob([ia], {type: 'image/png'});
    },

    // 绘制图片
    toImg() {
      let canvas2 = document.createElement('canvas')
      const ref = this.$refs['connectImg']
      let w = ref.offsetWidth
      let h = ref.offsetHeight
      canvas2.width = w * 2
      canvas2.height = h * 2
      const context = canvas2.getContext('2d')
      context.scale(2, 2)
      let scale = 2
      let opts = {
        scale,
        canvas2: context,
        w,
        h,
        scrollX: 0,
        scrollY: 0,
        // 【重要】开启跨域配置
        useCORS: true,
        allowTaint: true
      }
      return new Promise((resolve, reject) => {
        html2canvas(ref, opts).then(function (canvas) {
          const imgUrl = canvas.toDataURL('image/png')
          resolve(imgUrl)
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.connect-item {
  padding: 0 100px;

  #mycanvas {
    position: absolute;
    top: 0;
    left: 0;
  }

  .connect-title {
    font-size: 28px;
    padding-bottom: 20px;
  }

  .connect-main {
    position: relative;
    width: 1000px;
    height: 460px;

    .connect-img {
      position: relative;
      width: 1000px;
      height: 460px;
      border: 1px solid #ccc;
    }

    .clear-btn {
      position: absolute;
      top: 0;
      right: 0;
      padding: 10px;
      background-color: #448ef7;
      color: #fff;
      cursor: pointer;
    }

    .main-item {
      position: absolute;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: 1px solid #333;
      z-index: 1;

      font-size: 30px;
      font-weight: 500;
      line-height: 50px;
      text-align: center;
      color: #333;
      background: #fff;
      cursor: pointer;
    }

    .choose-item {
      box-shadow: 0 1px 0.625rem #777;
    }

    .main-item-1 {
      top: 180px;
      left: 250px;

      &::after {
        content: '开始';
        position: absolute;
        width: 60px;
        bottom: -40px;
        left: -0.48rem;
        font-size: 12px;
        line-height: 16px;
        color: #333;
      }
    }

    .main-item-2 {
      top: 30px;
      left: 520px;
    }

    .main-item-3 {
      top: 150px;
      left: 900px;
    }

    .main-item-4 {
      top: 200px;
      left: 620px;
    }

    .main-item-5 {
      top: 320px;
      left: 820px;
    }

    .main-item-6 {
      top: 360px;
      left: 280px;
    }

    .main-item-7 {
      top: 290px;
      left: 570px;
    }

    .main-item-8 {
      top: 260px;
      left: 80px;
    }

    .main-item-9 {
      top: 100px;
      left: 130px;
    }

    .main-item-10 {
      top: 30px;
      left: 300px;

      &::after {
        content: '结束';
        position: absolute;
        width: 60px;
        bottom: -40px;
        left: -0.48rem;
        font-size: 16px;
        line-height: 16px;
        color: #333;
      }
    }
  }

  .connect-item-answer {
    width: 1200px;
    padding-top: 30px;
    display: flex;
    flex-wrap: wrap;

    .answer-item {
      display: flex;
      width: 300px;
      padding: 10px;

      .ant-btn {
        height: 40px;
        margin-right: 10px;
      }

      .item-text {
        display: inline-block;
        font-size: 24px;
        line-height: 40px;
        cursor: pointer;
      }

      .item-img {
        width: 150px;
        cursor: pointer;
      }
    }
  }
}
</style>