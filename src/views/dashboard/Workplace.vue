<template>
  <page-layout :avatar="avatar">
    <div slot="headerContent">
      <div class="title">{{ timeFix }}，{{ nickname() }}</div>
      <div>归属：{{doctorInfo.name}} | {{doctorInfo.departName}} - {{doctorInfo.campus}} - {{doctorInfo.section}}</div>
    </div>
    <div slot="extra">
      <a-row class="more-info">
        <!--        <a-col :span="8">
                  <head-info title="项目数" content="56" :center="false" :bordered="false"/>
                </a-col>
                <a-col :span="8">
                  <head-info title="团队排名" content="8/24" :center="false" :bordered="false"/>
                </a-col>
                <a-col :span="8">
                  <head-info title="项目访问" content="2,223" :center="false"/>
                </a-col>-->
      </a-row>
    </div>

    <div>
      <a-row :gutter="24">
        <a-col :xl="16" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card class="project-list" :loading="loading" style="margin-bottom: 24px;" :bordered="false" title="进行中的项目" :body-style="{ padding: 0 }">
            <div>
              <a-card-grid class="project-card-grid" :key="i" v-for="(item, i) in projects" @click="schemeHandle(item)">
                <a-card :bordered="false" :body-style="{ padding: 0 }">
                  <a-card-meta>
                    <div slot="title" class="card-title">
                      <img v-if="item.avatarUrl" size="small" :src="getImgView(item.avatarUrl)" />
                      <a>{{ item.title }}</a>
                      <a-tag v-if="item.status == 1" color="green" style="float:right">已完成</a-tag>
                      <a-tag v-else color="red" style="float:right">未完成</a-tag>
                    </div>
                    <div slot="description" class="card-description">
                      {{ item.content }}
                    </div>
                  </a-card-meta>
                  <div class="project-item">
                    <a-tag v-if="item.type == 1" color="green">
                      今日方案
                    </a-tag>
                    <a-tag v-else-if="item.type == 2" color="blue">
                      量表任务
                    </a-tag>
                    <a-tag v-else-if="item.type == 3" color="purple">
                      医嘱任务
                    </a-tag>
                    <a-tag v-else-if="item.type == 4" color="orange">
                      放松训练
                    </a-tag>
                  </div>
                </a-card>
              </a-card-grid>
            </div>
          </a-card>

          <a-card class="train-list" :loading="trainloading" style="margin-bottom: 24px;" :bordered="false" title="放松训练" :body-style="{ padding: 0 }">
            <div>
              <a-card-grid class="project-card-grid" :key="i" v-for="(item, i) in trains" @click="trainHandle(item)">
                <a-card :bordered="false" :body-style="{ padding: 0 }">
                  <a-card-meta>
                    <div slot="title" class="card-title">
                      <a>{{ item.title }}</a>
                    </div>
                  </a-card-meta>
                </a-card>
              </a-card-grid>
            </div>
          </a-card>

          <a-card :loading="loading" title="动态" :bordered="false">
            <a-list>
              <a-list-item :key="index" v-for="(item, index) in projects">
                <a-list-item-meta>
                  <div slot="title">
                    <span>{{getPrefix(item.type)}}{{ item.title }}</span>&nbsp;
                  </div>
                  <div slot="description">{{ item.content }}</div>
                </a-list-item-meta>
              </a-list-item>
            </a-list>
          </a-card>
        </a-col>
        <a-col style="padding: 0 12px" :xl="8" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card title="快速开始 / 便捷导航" style="margin-bottom: 24px" :bordered="false" :body-style="{padding: 0}">
            <div class="item-group">
              <a @click="goMessageList">老师留言</a>
              <a @click="goDoctorAdvice">医嘱任务</a>
              <a @click="goMeasureTask">量表任务</a>
            </div>
          </a-card>
          <a-card title="周睡眠指数" style="margin-bottom: 24px" :loading="radarLoading" :bordered="false" :body-style="{ padding: 0 }">
            <div>
              <v-chart :force-fit="true" :height="405" :data="pieData" :scale="pieScale">
                <v-tooltip :showTitle="false" dataKey="item*percent" />
                <v-axis />
                <!-- position="right" :offsetX="-140" -->
                <v-legend dataKey="item" />
                <v-pie position="percent" color="item" :vStyle="pieStyle" />
                <v-coord type="theta" :radius="0.75" :innerRadius="0.6" />
              </v-chart>
            </div>
          </a-card>
          <a-card :loading="loading" title="团队" :bordered="false">
            <div class="members">
              <a-row>
                <a-col :span="24">
                  <img size="small" src="../../assets/BiazfanxmamNRoxxVxka.png" />
                  <span class="member">{{doctorInfo.departName}} - {{doctorInfo.campus}} - {{doctorInfo.section}}</span>
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <scheme-modal ref="schemeModal" @ok="modalFormOk"></scheme-modal>
      <train-modal ref="trainModal" @ok="modalFormOk"></train-modal>
    </div>
  </page-layout>
</template>

<script>
import { getAction, getFileAccessHttpUrl, httpAction } from '@/api/manage'
import { timeFix } from '@/utils/util'
import { mapGetters } from 'vuex'
import SchemeModal from './SchemeModal'
import TrainModal from './TrainModal'
import PageLayout from '@/components/page/PageLayout'
import HeadInfo from '@/components/tools/HeadInfo'
import Radar from '@/components/chart/Radar'

const DataSet = require('@antv/data-set')
const pieScale = [{
  dataKey: 'percent',
  min: 0,
  formatter: '.0%'
}]

export default {
  name: 'Workplace',
  components: {
    PageLayout,
    HeadInfo,
    Radar,
    SchemeModal,
    TrainModal
  },
  data () {
    return {
      timeFix: timeFix(),
      pieScale,
      pieData: [],
      avatar: '',
      user: {},
      doctorInfo: {},
      projects: [],
      trains: [],
      trainloading: true,
      loading: true,
      radarLoading: true,
      activities: [],
      pieStyle: {
        stroke: '#fff',
        lineWidth: 1
      },
      url: {
        findDoctorInfo: '/home/<USER>',
        findTodayScheme: '/home/<USER>',
        startAnswer: '/home/<USER>',
        sleepPie: '/home/<USER>',
        measureTaskEdit: '/ta/taDoctorAdvice/edit',
        trainList: '/base/baseTrain/list',
      }
    }
  },
  computed: {
    userInfo () {
      return this.$store.getters.userInfo
    }
  },
  created () {
    this.user = this.userInfo
    this.avatar = getFileAccessHttpUrl(this.userInfo.avatar)
  },
  mounted () {
    this.getProjects()
    this.getActivity()
    //睡眠饼图
    this.initPie()
    //查询老师信息
    this.findDoctorInfo()
    //放松训练
    this.getTrains();
  },
  methods: {
    ...mapGetters(['nickname', 'welcome']),
    /* 图片预览 */
    getImgView (text) {
      if (text && text.indexOf(',') > 0) {
        text = text.substring(0, text.indexOf(','))
      }
      return getFileAccessHttpUrl(text) ? getFileAccessHttpUrl(text) : '../../assets/gaOngJwsRYRaVAuXXcmB.png'
    },
    getProjects () {
      httpAction(this.url.findTodayScheme, {}, 'get').then((res) => {
        if (res.success) {
          this.projects = res.result
          this.loading = false
        }
      })
    },
    getTrains () {
      this.trainloading = true
      httpAction(this.url.trainList, { pageNo: 1, pageSize: 6 }, 'get').then((res) => {
        if (res.success) {
          this.trains = res.result.records
          this.trainloading = false
        }
      })
    },
    initPie () {
      httpAction(this.url.sleepPie, {}, 'get').then((res) => {
        if (res.success) {
          const dv = new DataSet.View().source(res.result)
          dv.transform({
            type: 'percent',
            field: 'count',
            dimension: 'item',
            as: 'percent'
          })
          this.pieData = dv.rows
          this.radarLoading = false
        }
      })
    },
    getActivity () {
      // this.$http.get('/mock/api/workplace/activity')
      //   .then(res => {
      //     this.activities = res.result
      //   })
    },
    findDoctorInfo () {
      httpAction(this.url.findDoctorInfo, {}, 'get').then((res) => {
        if (res.success) {
          this.doctorInfo = res.result
        }
      })
    },
    trainHandle (item, type = 1) {
      this.$refs.trainModal.title = item.title;
      this.$refs.trainModal.disableSubmit = true
      this.$refs.trainModal.voicePath = type == 2 ? item.avatarUrl : item.voicePath;
      this.$refs.trainModal.titleImg = item.titleImg;
      this.$refs.trainModal.schemeId = type == 2 ? item.id : '';
      this.$refs.trainModal.view();
    },
    schemeHandle (item) {
      let that = this
      if (item.locked) {
        that.$message.error('此课程未解锁，请先解锁上一课程!')
        return
      }
      //今日方案
      if (item.type == 1) {
        // TODO: 是否有文章返回
        if (item.status == 0) {
          this.$refs.schemeModal.schemeId = item.id;
          this.$refs.schemeModal.view(item.id);
        } else {
          that.$message.info('已完成')
        }
      }
      //量表任务
      if (item.type == 2) {
        if (item.status == 0) {
          getAction(this.url.startAnswer, { id: item.id }).then((res) => {
            if (res.success) {
              this.$notification['success']({
                message: '添加成功',
                duration: 3,
                description: '已在选择的终端推送答题信息'
              })
            } else {
              that.$message.error(res.message)
            }
          })
        } else {
          that.$message.info('已完成')
        }
      }
      //医嘱任务
      if (item.type == 3) {
        if (item.status == 0) {
          this.$confirm({
            title: item.title,
            content: item.content,
            okText: '完成',
            cancelText: '取消',
            onOk () {
              httpAction(that.url.measureTaskEdit, { id: item.id, status: 1 }, 'put').then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.getProjects()
                }
              })
            },
            onCancel () {
            }
          })
        } else {
          that.$message.info('已完成')
        }
      }
      // 放松训练
      if (item.type == 4) {
        // TODO: 列表接口加上音频url或者提供根据方案id获取详情的接口
        that.trainHandle(item, 2)
      }
    },
    goMessageList () {
      this.$router.push({
        // name: 'messageList'
        path: '/pa/PaLeaveMessageList'
      })
    },
    goDoctorAdvice () {
      console.log(this.$router)
      this.$router.push({
        path: '/ta/TaDoctorAdviceList'
      })
    },
    goMeasureTask () {
      this.$router.push({
        // name: 'measureTask'
        path: '/ta/TaMeasureTaskList'
      })
    },
    modalFormOk () {
      this.getProjects()
    },
    getPrefix (type) {
      if (type == 1) {
        return '系统推送：'
      } else if (type == 2) {
        return "老师推送，请尽快开始答题："
      } else if (type == 3) {
        return "医嘱任务，请根据提示进行确认："
      }
    }
  }
}
</script>

<style lang="less" scoped>
.project-list {
  .card-title {
    font-size: 0;

    a {
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      height: 24px;
      display: inline-block;
      vertical-align: top;
      font-size: 14px;

      &:hover {
        color: #1890ff;
      }
    }

    img {
      width: 24px;
      height: 24px;
      line-height: 24px;
      border-radius: 50%;
      margin-right: 12px;
    }
  }

  .card-description {
    color: rgba(0, 0, 0, 0.45);
    height: 44px;
    line-height: 22px;
    overflow: hidden;
  }

  .project-item {
    display: flex;
    margin-top: 8px;
    overflow: hidden;
    font-size: 12px;
    height: 20px;
    line-height: 20px;

    a {
      color: rgba(0, 0, 0, 0.45);
      display: inline-block;
      flex: 1 1 0;

      &:hover {
        color: #1890ff;
      }
    }

    .datetime {
      color: rgba(0, 0, 0, 0.25);
      flex: 0 0 auto;
      float: right;
    }
  }

  .ant-card-meta-description {
    color: rgba(0, 0, 0, 0.45);
    height: 44px;
    line-height: 22px;
    overflow: hidden;
  }
}

.train-list {
  .card-title {
    font-size: 0;

    a {
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      height: 24px;
      display: inline-block;
      vertical-align: top;
      font-size: 14px;

      &:hover {
        color: #1890ff;
      }
    }

    img {
      width: 24px;
      height: 24px;
      line-height: 24px;
      border-radius: 50%;
      margin-right: 12px;
    }
  }
}

.item-group {
  padding: 20px 0 8px 24px;
  font-size: 0;

  a {
    color: rgba(0, 0, 0, 0.65);
    display: inline-block;
    font-size: 14px;
    margin-bottom: 13px;
    width: 25%;
  }
}

.members {
  a {
    display: block;
    margin: 12px 0;
    line-height: 24px;
    height: 24px;

    .member {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 24px;
      max-width: 100px;
      vertical-align: top;
      margin-left: 12px;
      transition: all 0.3s;
      display: inline-block;
    }

    &:hover {
      span {
        color: #1890ff;
      }
    }
  }

  img {
    width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 50%;
    margin-right: 12px;
  }
}

.mobile {
  .project-list {
    .project-card-grid {
      width: 100%;
    }
  }

  .more-info {
    border: 0;
    padding-top: 16px;
    margin: 16px 0 16px;
  }

  .headerContent .title .welcome-text {
    display: none;
  }
}
</style>