<template>
  <div>
    <!-- <workplace v-if="roleId == '1436146582834745345'"></workplace>-->
    <!-- <index-chart></index-chart>  -->
    <education-home v-if="code.includes('yyhz')"></education-home>
    <counselor-home v-if="code.includes('admin')"></counselor-home>
    <!--    <index-bdc v-if="indexStyle==2"></index-bdc>
        <index-task v-if="indexStyle==3"></index-task>
        <div style="width: 100%;text-align: right;margin-top: 20px">
          请选择首页样式：
          <a-radio-group v-model="indexStyle">
            <a-radio :value="1">统计图表</a-radio>
            <a-radio :value="2">统计图表2</a-radio>
            <a-radio :value="3">任务表格</a-radio>
          </a-radio-group>
        </div>-->
  </div>
</template>

<script>
import IndexChart from './IndexChart'
import IndexTask from './IndexTask'
import IndexBdc from './IndexBdc'
import Workplace from './Workplace'
import EducationHome from './educationHome'
import CounselorHome from './counselorHome'
import { mapGetters } from 'vuex'
  import Vue from 'vue'
  import { ROLE_CODES } from "@/store/mutation-types"
export default {
  name: 'Analysis',
  components: {
    IndexChart,
    IndexTask,
    IndexBdc,
    Workplace,
    EducationHome,
    CounselorHome
  },
  data() {
    return {
      indexStyle: 1,
      roleId: '',
      code: null
    }
  },
  created() {
    this.roleId = this.userInfo().roleId
    this.code = Vue.ls.get(ROLE_CODES)
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo'])
  }
}
</script>