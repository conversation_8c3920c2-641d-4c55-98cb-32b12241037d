<template>
  <div class="page-header-index-wide" style="user-select:none;">
    <a-row :gutter="24">
      <a-col :sm="24" :md="12" :xl="6" :style="{ marginBottom: '24px' }">
        <chart-card :loading="loading" title="总学生数" :total="totalPatient | NumberFormat">
          <a-tooltip title="来访的学生总数" slot="action">
            <a-icon type="info-circle-o"/>
          </a-tooltip>
          <div>
          </div>
          <template slot="footer">日均学生量<span> {{averagePatientNum}}</span></template>
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="6" :style="{ marginBottom: '24px' }">
        <chart-card :loading="loading" title="诊断量" :total="totalDiagnose | NumberFormat">
          <a-tooltip title="统计每天答题的次数" slot="action">
            <a-icon type="info-circle-o"/>
          </a-tooltip>
          <div>
            <div class="antv-chart-mini">
              <div class="chart-wrapper" :style="{ height: 46 }">
                <v-chart :force-fit="true" height="100" :data="diagnoseNum" :padding="[36, 0, 18, 0]">
                  <v-tooltip/>
                  <v-smooth-area position="时间*诊断量"/>
                </v-chart>
              </div>
            </div>
          </div>
          <template slot="footer">当日诊断量<span> {{ diagnoseDay | NumberFormat }}</span></template>
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="6" :style="{ marginBottom: '24px' }">
        <chart-card :loading="loading" title="睡眠日记完成率" :total="sleepDiaryCompleteRate+`%`">
          <a-tooltip title="已完成的次数和总次数的比值" slot="action">
            <a-icon type="info-circle-o"/>
          </a-tooltip>
          <div>
            <mini-progress color="rgb(19, 194, 194)" :target="sleepDiaryCompleteRate" :percentage="sleepDiaryCompleteRate"
                           height="8px"/>
          </div>
          <template slot="footer">
            今日完成率 <span>{{toDaySleepDiaryCompleteRate}} %</span>
          </template>
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="6" :style="{ marginBottom: '24px' }">
        <chart-card :loading="loading" title="心理测评完成率" :total="diagnoseCompleteRate+`%`">
          <a-tooltip title="已完成的次数和总次数的比值" slot="action">
            <a-icon type="info-circle-o"/>
          </a-tooltip>
          <div>
            <mini-progress color="rgb(19, 194, 194)" :target="diagnoseCompleteRate" :percentage="diagnoseCompleteRate"
                           height="8px"/>
          </div>
          <template slot="footer">
            今日完成率 <span>{{toDayCompleteRate}} %</span>
          </template>
        </chart-card>
      </a-col>
    </a-row>

    <a-card :loading="loading" :bordered="false" :body-style="{padding: '0'}">
      <div class="salesCard">
        <a-tabs default-active-key="1" size="large" :tab-bar-style="{marginBottom: '24px', paddingLeft: '16px'}">
          <div class="extra-wrapper" slot="tabBarExtraContent">
            <div class="extra-item">
              <a @click="measureRank(0)">今日</a>
              <a @click="measureRank(7)">本周</a>
              <a @click="measureRank(30)">本月</a>
              <a @click="measureRank(365)">本年</a>
              <a @click="getMeasureNumStatistics()">全部</a>
            </div>
            <a-range-picker
              :style="{width: '256px'}"
              @change="onDateChange"/>
          </div>
          <a-tab-pane loading="true" tab="新增学生" key="1">
            <a-row>
              <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24">
                <bar :data="addPatientList" title="新增学生数据"/>
              </a-col>
              <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                <div class="rank">
                  <h4 class="title">量表使用排行</h4>
                  <ul class="list">
                    <li :key="index" v-for="(item, index) in measureList">
                      <span :class="index < 3 ? 'active' : null">{{ index + 1 }}</span>
                      <span>{{ item.measureName }}</span>
                      <span>{{ item.measureNum }}</span>
                    </li>
                  </ul>
                </div>
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-card>

    <div class="antd-pro-pages-dashboard-analysis-twoColLayout" :class="isDesktop() ? 'desktop' : ''">
      <a-row :gutter="24" type="flex" :style="{ marginTop: '24px' }">
        <a-col :xl="12" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :loading="loading" :bordered="false" title="报告预警" :style="{ height: '100%' }">
            <a-row :gutter="68">
              <a-col :xs="24" :sm="12" :style="{ marginBottom: ' 24px'}">
                <number-info :total="warningTotal" :sub-total="17.1">
                  <span slot="subtitle">
                    <span>预警总数</span>
                    <a-tooltip title="包含轻度至重度所有预警数据" slot="action">
                      <a-icon type="info-circle-o" :style="{ marginLeft: '8px' }"/>
                    </a-tooltip>
                  </span>
                </number-info>
                <!-- miniChart -->
                <div>
                  <mini-smooth-area :style="{ height: '45px' }" :dataSource="warningNumList1" :scale="searchUserScale"/>
                </div>
              </a-col>
              <a-col :xs="24" :sm="12" :style="{ marginBottom: ' 24px'}">
                <number-info :total="averageWarningNum" :sub-total="26.2" status="down">
                  <span slot="subtitle">
                    <span>日均报警次数</span>
                    <a-tooltip title="日平均报警数/每天中重度报警次数" slot="action">
                      <a-icon type="info-circle-o" :style="{ marginLeft: '8px' }"/>
                    </a-tooltip>
                  </span>
                </number-info>
                <!-- miniChart -->
                <div>
                  <mini-smooth-area :style="{ height: '45px' }" :dataSource="warningNumList2" :scale="searchUserScale"/>
                </div>
              </a-col>
            </a-row>
            <div class="ant-table-wrapper">
              <a-table
                row-key="warningList"
                size="small"
                rowKey="id"
                :columns="searchTableColumns"
                :dataSource="warningList"
                :pagination="{ pageSize: 5 }"
              >
                <span slot="degree" slot-scope="text">
                  <a-badge :color="text | statusTypeFilter" :text="text | statusFilter"/>
                </span>
              </a-table>
            </div>
          </a-card>
        </a-col>
        <a-col :xl="12" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card class="antd-pro-pages-dashboard-analysis-salesCard" :loading="loading" :bordered="false"
                  title="学生信息占比" :style="{ height: '100%' }">
            <div slot="extra" style="height: inherit;">
              <div class="analysis-salesTypeRadio">
                <a-radio-group @change="handleRadio" defaultValue="1">
                  <a-radio-button value="1">性别</a-radio-button>
                  <a-radio-button value="2">学历</a-radio-button>
                  <a-radio-button value="3">年龄</a-radio-button>
                </a-radio-group>
              </div>
            </div>
            <h4>{{userInfoRadioTitle}}</h4>
            <div>
              <!-- style="width: calc(100% - 240px);" -->
              <div>
                <v-chart :force-fit="true" :height="405" :data="patientRatio" :scale="pieScale">
                  <v-tooltip :showTitle="false" dataKey="item*percent"/>
                  <v-axis/>
                  <!-- position="right" :offsetX="-140" -->
                  <v-legend dataKey="item"/>
                  <v-pie position="percent" color="item" :vStyle="pieStyle"/>
                  <v-coord type="theta" :radius="0.75" :innerRadius="0.6"/>
                </v-chart>
              </div>

            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
  import moment from 'moment'
  import { getAction } from '@/api/manage'
  import { Bar, ChartCard, MiniProgress, MiniSmoothArea, NumberInfo, Trend } from '@/components'
  import { mixinDevice } from '@/utils/mixin'


  const searchUserData = []
  for (let i = 0; i < 7; i++) {
    searchUserData.push({
      x: moment().add(i, 'days').format('YYYY-MM-DD'),
      y: Math.ceil(Math.random() * 10)
    })
  }
  const searchUserScale = [
    {
      dataKey: 'x',
      alias: '时间'
    },
    {
      dataKey: 'y',
      alias: '预警数'
    }]

  const searchTableColumns = [
    {
      dataIndex: 'measureName',
      title: '量表名称'
    },
    {
      dataIndex: 'userName',
      title: '学生姓名'
    },
    {
      dataIndex: 'telphone',
      title: '学生电话'
    },
    {
      dataIndex: 'degree',
      title: '程度',
      align: 'right',
      scopedSlots: { customRender: 'degree' }
    }
  ]

  const DataSet = require('@antv/data-set')

  const pieScale = [{
    dataKey: 'percent',
    min: 0,
    formatter: '.0%'
  }]


  const degreeMap = {
    0: {
      color: '#D9D9D9',
      text: '未答完'
    },
    1: {
      color: 'green',
      text: '正常'
    },
    2: {
      color: 'yellow',
      text: '轻度'
    },
    3: {
      color: 'red',
      text: '中度'
    },
    4: {
      color: 'purple',
      text: '重度'
    },
    5: {
      color: '#D9D9D9',
      text: '未知'
    }
  }

  export default {
    name: 'Analysis',
    mixins: [mixinDevice],
    components: {
      ChartCard,
      MiniProgress,
      Bar,
      Trend,
      NumberInfo,
      MiniSmoothArea
    },
    data() {
      return {
        loading: true,
        // 搜索用户数
        searchUserData,
        searchUserScale,
        searchTableColumns,
        pieScale,
        /*学生数量统计*/
        totalPatient: 0,
        patientWeekRate: '0% ',
        patientDayRate: '0%',
        averagePatientNum: 0,
        /*诊断量统计*/
        totalDiagnose: 0,
        diagnoseDay: 0,
        diagnoseNum: {},
        /*答题完成率*/
        diagnoseCompleteRate: 0,
        toDayCompleteRate: 0,
        /*睡眠日记完成率*/
        sleepDiaryCompleteRate: 0,
        toDaySleepDiaryCompleteRate: 0,
        /*新增学生数据*/
        addPatientList: [],
        /*量表使用排行*/
        measureList: [],
        /*学生属性分部*/
        patientRatio: [],
        userInfoRadioTitle: '性别占比',
        /*用时*/
        totalTime: 0,
        hoursNumList: [],
        averageHours: 0,
        pieStyle: {
          stroke: '#fff',
          lineWidth: 1
        },
        /*预警*/
        warningTotal: 0,
        averageWarningNum: 0,
        warningNumList1: [],
        warningNumList2: [],
        warningList: [],
        url: {
          getPatientStatistics: '/report/commonBi/getPatientStatistics',
          getDiagnoseStatistics: '/report/commonBi/getDiagnoseStatistics',
          getDiagnoseCompleteRate: '/report/commonBi/getDiagnoseCompleteRate',
          getSleepDiaryCompleteRate: '/report/commonBi/getSleepDiaryCompleteRate',
          getAddPatientStatistics: '/report/commonBi/getAddPatientStatistics',
          getMeasureNumStatistics: '/report/commonBi/getMeasureNumStatistics',
          getPatientSexStatistics: '/report/commonBi/getPatientSexStatistics',
          getPatientCulturalStatistics: '/report/commonBi/getPatientCulturalStatistics',
          getPatientAgeStatistics: '/report/commonBi/getPatientAgeStatistics',
          getHoursStatistics: '/report/commonBi/getHoursStatistics',
          getWarningStatistics: '/report/commonBi/getWarningStatistics'
        }
      }
    },
    filters: {
      statusFilter(type) {
        return degreeMap[type].text
      },
      statusTypeFilter(type) {
        return degreeMap[type].color
      }
    },
    created() {
      setTimeout(() => {
        this.loading = !this.loading
      }, 700)
      this.loadPatientStatistics()
      this.loadDiagnoseStatistics()
      this.loadDiagnoseCompleteRate()
      this.loadSleepDiaryCompleteRate()
      this.loadAddPatientStatistics()
      this.getMeasureNumStatistics()
      this.getPatientSexStatistics()
      this.getHoursStatistics()
      this.getWarningNumStatistics()
    },
    methods: {
      /*学生数据统计*/
      loadPatientStatistics() {
        let that = this
        let httpurl = that.url.getPatientStatistics
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            that.totalPatient = res.result.totalPatient
            that.averagePatientNum = res.result.averagePatientNum
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*诊断数据统计*/
      loadDiagnoseStatistics() {
        let that = this
        let httpurl = that.url.getDiagnoseStatistics
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            that.diagnoseNum = res.result.diagnoseNum
            that.totalDiagnose = res.result.totalDiagnose
            that.diagnoseDay = res.result.diagnoseDay
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*答题完成率*/
      loadDiagnoseCompleteRate() {
        let that = this
        let httpurl = that.url.getDiagnoseCompleteRate
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            that.diagnoseCompleteRate = res.result.diagnoseCompleteRate
            that.toDayCompleteRate = res.result.toDayCompleteRate
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*睡眠日记完成率*/
      loadSleepDiaryCompleteRate() {
        let that = this
        let httpurl = that.url.getSleepDiaryCompleteRate
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            that.sleepDiaryCompleteRate = res.result.sleepDiaryCompleteRate
            that.toDaySleepDiaryCompleteRate = res.result.toDaySleepDiaryCompleteRate
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*新增用户人数*/
      loadAddPatientStatistics() {
        let that = this
        let httpurl = that.url.getAddPatientStatistics
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            that.addPatientList = res.result.addPatientList
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*用时*/
      getHoursStatistics() {
        let that = this
        let httpurl = that.url.getHoursStatistics
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            that.hoursNumList = res.result.hoursNumList
            that.totalTime = res.result.totalHours + 's'
            that.averageHours = res.result.averageHours
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*量表排行*/
      getMeasureNumStatistics() {
        let that = this
        let httpurl = that.url.getMeasureNumStatistics
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            that.measureList = res.result
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*按钮操作时间*/
      measureRank(val) {
        let that = this
        let httpurl = that.url.getMeasureNumStatistics
        getAction(httpurl, { dateDiff: val }).then((res) => {
          if (res.success) {
            that.measureList = res.result
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*性别分布*/
      getPatientSexStatistics() {
        let that = this
        let httpurl = that.url.getPatientSexStatistics
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            const sourceData = [
              { item: '男', count: res.result.man },
              { item: '女', count: res.result.woman }
            ]
            const dv = new DataSet.View().source(sourceData)
            dv.transform({
              type: 'percent',
              field: 'count',
              dimension: 'item',
              as: 'percent'
            })
            that.patientRatio = dv.rows
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*学历分布*/
      getPatientCulturalStatistics() {
        let that = this
        let httpurl = that.url.getPatientCulturalStatistics
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            const sourceData = [
              { item: '文盲', count: res.result[0] ? res.result[0].value : 0 },
              { item: '小学', count: res.result[1] ? res.result[1].value : 0 },
              { item: '初中', count: res.result[2] ? res.result[2].value : 0 },
              { item: '高中', count: res.result[3] ? res.result[3].value : 0 },
              { item: '大学', count: res.result[4] ? res.result[4].value : 0 },
              { item: '大学以上', count: res.result[5] ? res.result[5].value : 0 }
            ]
            const dv = new DataSet.View().source(sourceData)
            dv.transform({
              type: 'percent',
              field: 'count',
              dimension: 'item',
              as: 'percent'
            })
            that.patientRatio = dv.rows
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*年龄分布*/
      getPatientAgeStatistics() {
        let that = this
        let httpurl = that.url.getPatientAgeStatistics
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            const sourceData = [
              { item: '0-20', count: res.result[0] },
              { item: '20-30', count: res.result[1] },
              { item: '30-40', count: res.result[2] },
              { item: '40-50', count: res.result[3] },
              { item: '50-60', count: res.result[4] },
              { item: '60-70', count: res.result[5] },
              { item: '70以上', count: res.result[6] }
            ]
            const dv = new DataSet.View().source(sourceData)
            dv.transform({
              type: 'percent',
              field: 'count',
              dimension: 'item',
              as: 'percent'
            })
            that.patientRatio = dv.rows
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      handleRadio(e) {
        if (e.target.value == '1') {
          this.userInfoRadioTitle = '性别占比'
          this.getPatientSexStatistics()
        } else if (e.target.value == '2') {
          this.userInfoRadioTitle = '学历占比'
          this.getPatientCulturalStatistics()
        } else if (e.target.value == '3') {
          this.userInfoRadioTitle = '年龄占比'
          this.getPatientAgeStatistics()
        }
      },
      /*报告预警*/
      getWarningNumStatistics() {
        let that = this
        let httpurl = that.url.getWarningStatistics
        getAction(httpurl, {}).then((res) => {
          if (res.success) {
            that.warningTotal = res.result.warningTotal
            that.averageWarningNum = res.result.averageWarningNum
            that.warningNumList1 = res.result.warningNumList1
            that.warningNumList2 = res.result.warningNumList2
            that.warningList = res.result.warningList
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      onDateChange: function(value, dateString) {
        let that = this
        let httpurl = that.url.getMeasureNumStatistics
        getAction(httpurl, { startDate: dateString[0], endDate: dateString[1] }).then((res) => {
          if (res.success) {
            that.measureList = res.result
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .extra-wrapper {
    line-height: 55px;
    padding-right: 24px;

    .extra-item {
      display: inline-block;
      margin-right: 24px;

      a {
        margin-left: 24px;
      }
    }
  }

  .antd-pro-pages-dashboard-analysis-twoColLayout {
    position: relative;
    display: flex;
    display: block;
    flex-flow: row wrap;
  }

  .antd-pro-pages-dashboard-analysis-salesCard {
    height: calc(100% - 24px);

    /deep/ .ant-card-head {
      position: relative;
    }
  }

  .dashboard-analysis-iconGroup {
    i {
      margin-left: 16px;
      color: rgba(0, 0, 0, .45);
      cursor: pointer;
      transition: color .32s;
      color: black;
    }
  }

  .analysis-salesTypeRadio {
    position: absolute;
    right: 54px;
    bottom: 12px;
  }

  .antv-chart-mini {
    position: relative;
    width: 100%;

    .chart-wrapper {
      position: absolute;
      bottom: -28px;
      width: 100%;
    }
  }


  .rank {
    padding: 0 32px 32px 72px;

    .list {
      margin: 25px 0 0;
      padding: 0;
      list-style: none;

      li {
        margin-top: 16px;

        span {
          color: rgba(0, 0, 0, .65);
          font-size: 14px;
          line-height: 22px;

          &:first-child {
            background-color: #f5f5f5;
            border-radius: 20px;
            display: inline-block;
            font-size: 12px;
            font-weight: 600;
            margin-right: 24px;
            height: 20px;
            line-height: 20px;
            width: 20px;
            text-align: center;
          }

          &.active {
            background-color: #314659;
            color: #fff;
          }

          &:last-child {
            float: right;
          }
        }
      }
    }
  }

  .mobile .rank {
    padding: 0 32px 32px 32px;
  }
</style>
