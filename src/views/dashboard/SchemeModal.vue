<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    okText="完成"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <div v-if="schemeInfo.type == 1">
      <p v-html="schemeInfo.content"></p>
    </div>
    <div v-if="schemeInfo.type == 2">
      <video-player class="video-player vjs-custom-skin"
                    ref="videoPlayer"
                    @loadeddata="onPlayerLoadeddata($event)"
                    @timeupdate="onPlayerTimeupdate($event)"
                    :playsinline="true"
                    :options="playerOptions">
      </video-player>
    </div>
  </j-modal>
</template>

<script>
  import { getAction, putAction } from '@/api/manage'
  import { videoPlayer } from 'vue-video-player'
  import 'video.js/dist/video-js.css'
  import 'vue-video-player/src/custom-theme.css'

  export default {
    name: 'SchemeModal',
    components: {
      videoPlayer
    },
    data() {
      return {
        title: '',
        schemeId: '',
        schemeInfo: {},
        playerOptions: {},
        width: 1200,
        visible: false,
        disableSubmit: false,
        duration: 0,
        currentTime: 0,
        url: {
          getSchemeInfo: '/home/<USER>',
          editSub: '/pa/paPatientScheme/editSub',
          videoPlayProgress: '/pa/paPatientScheme/videoPlayProgress'
        }
      }
    },
    methods: {
      onPlayerLoadeddata(player) {
        this.duration = player.cache_.duration
        this.currentTime = player.cache_.currentTime
      },
      onPlayerTimeupdate(player) {
        this.duration = player.cache_.duration
        this.currentTime = player.cache_.currentTime
      },
      view(id) {
        this.schemeId = id
        this.visible = true
        this.loadData()
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        let that = this
        putAction(that.url.editSub, { id: that.schemeId, status: 1 }).then((res) => {
          if (res.success) {
            this.$emit('ok')
            this.putPlayProgress()
            this.visible = false
          } else {
            that.$message.error(res.message)
          }
        })
      },
      submitCallback() {
        this.$emit('ok')
        this.putPlayProgress()
        this.visible = false
      },
      handleCancel() {
        this.close()
        this.putPlayProgress()
      },
      loadData() {
        let that = this
        getAction(that.url.getSchemeInfo, { id: that.schemeId }).then((res) => {
          if (res.success) {
            that.schemeInfo = res.result
            that.title = '今日课程：' + res.result.title
            this.playerOptions = {
              playbackRates: [0.7, 1.0, 1.5, 2.0], // 播放速度
              autoplay: false, // 如果true,浏览器准备好时开始回放。
              muted: false, // 默认情况下将会消除任何音频。
              loop: false, // 导致视频一结束就重新开始。
              preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
              language: 'zh-CN',
              aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
              fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
              sources: [{
                type: 'video/mp4', // 这里的种类支持很多种：基本视频格式、直播、流媒体等，具体可以参看git网址项目
                src: this.schemeInfo.videoPath // url地址
              }],
              poster: '', // 你的封面地址
              notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
              controlBar: {
                timeDivider: true,
                durationDisplay: true,
                remainingTimeDisplay: false,
                fullscreenToggle: true // 全屏按钮
              }
            }
          } else {
            that.$message.error(res.message)
          }
        })
      },
      putPlayProgress() {
        const data = {
          id: this.schemeId,
          videoPlayProgress: this.currentTime && this.duration ? (this.currentTime / this.duration).toFixed(2) * 100 : 0,
          videoPlayTime: this.currentTime ? Number(this.currentTime).toFixed(2) : 0
        }
				putAction(this.url.videoPlayProgress, data).then(res => {
					if (!res.success) {
						this.$message.error('上传失败')
					}
				})
			}
    }
  }
</script>