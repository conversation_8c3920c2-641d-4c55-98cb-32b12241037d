<template>
  <div class="page-header-index-wide" style="user-select:none;">
    <div>
      <a-row :gutter="24" type="flex" :style="{ marginTop: '24px' }">
        <a-col :xl="14" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false">
            <a-row :gutter="20">
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }">
                <div class="tab tabBg1" @click="getRouter('/psychological/student/index')">
                  <div class="title">
                    <div class="name">
                      心理普查
                    </div>
                    <div class="eName">
                      Psychological survey
                    </div>
                  </div>

                  <div class="typeName">
                    <span></span>
                    心理测评
                  </div>
                </div>
              </a-col>
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }">
                <div class="tab tabBg2" @click="getRouter('/appointment/index')">
                  <div class="title">
                    <div class="name">
                      咨询预约
                    </div>
                    <div class="eName">
                      Consultation appointment
                    </div>
                  </div>

                  <div class="typeName">
                    <span></span>
                    心理咨询
                  </div>
                </div>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }">
                <div class="tab tabBg3">
                  <div class="title">
                    <div class="name">
                      活动中心
                    </div>
                    <div class="eName">
                      Activity Center
                    </div>
                  </div>

                  <div class="typeName">
                    <span></span>
                    活动报名
                  </div>
                </div>
              </a-col>
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }">
                <div class="tab tabBg4">
                  <div class="title">
                    <div class="name">
                      科普宣传
                    </div>
                    <div class="eName">
                      Science popularization and publicity
                    </div>
                  </div>

                  <div class="typeName">
                    <span></span>
                    心理科普
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
        <a-col :xl="10" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false">
            <div class="topTitle">
              <div class="left">
                <span></span>
                <div class="titleBg">
                  测评
                </div>
                <div class="name">
                  任务
                </div>
              </div>
              <div class="right" @click="getRouter('/WxAuthorizationRecordsList/WxAuthorizationRecordsList')">
                更多 >
              </div>
            </div>
            <div class="progress">
              <div class="progressDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <a-progress :percent="30" />
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
              <div class="progressDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <a-progress :percent="30" />
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
              <div class="progressDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <a-progress :percent="30" />
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <a-row :gutter="24" type="flex" :style="{ marginTop: '24px' }">
        <a-col :xl="14" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false">
            <a-row :gutter="20">
              <div class="other">
                <div class="otherInfo">
                  <img src="../../assets/image/other.png" alt="">
                  <div class="otherTitle">
                    心理咨询室
                  </div>
                  <div class="othermess">
                    解忧赋能<br />
                    拥抱阳光成长
                  </div>
                </div>
                <div class="line"></div>
                <div class="otherInfo">
                  <img src="../../assets/image/other1.png" alt="">
                  <div class="otherTitle">
                    放松减压
                  </div>
                  <div class="othermess">
                    舒缓身心<br /> 轻松释放压力
                  </div>
                </div>
                <div class="line"></div>
                <div class="otherInfo">
                  <img src="../../assets/image/other2.png" alt="">
                  <div class="otherTitle">
                    学习中心
                  </div>
                  <div class="othermess">
                    汇聚知识资源<br /> 提供学习支持与服务
                  </div>
                </div>
              </div>
            </a-row>
          </a-card>
        </a-col>
        <a-col :xl="10" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false">
            <div class="topTitle">
              <div class="left">
                <span></span>
                <div class="titleBg">
                  新闻
                </div>
                <div class="name">
                  中心
                </div>
              </div>
              <div class="right">
                更多 >
              </div>
            </div>
            <div class="progress">
              <div class="newsDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
              <div class="newsDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
              <div class="newsDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { psEvaluationUser } from '@/api/api'
export default {
  name: 'EducationHome',
  components: {},
  data () {
    return {
    }
  },
  filters: {

  },
  created () {
    this.getPatient()
  },
  methods: {
    // 页面跳转
    getRouter (path) {
      this.$router.push({ path: path });
    },
    // 获取测评任务
    getPatient () {
      psEvaluationUser({}).then((res) => {
        if (res.success) {
        } else {
        }
      });
    }
  }
}
</script>

<style lang="less" scoped>
.page-header-index-wide {
  background: url(../../assets/image/homeBg.png) no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  padding: 30px;
}

.tab {
  width: 100%;
  height: 170px;
}

.tabBg1 {
  background: url(../../assets/image/bg.png) no-repeat;
}

.tabBg2 {
  background: url(../../assets/image/bg4.png) no-repeat;
}

.tabBg3 {
  background: url(../../assets/image/bg1.png) no-repeat;

  .typeName {
    color: #333333 !important;

    span {
      background: #333333;
    }
  }

  .title {
    .name {
      color: #333333;
    }

    .eName {
      color: #333333;
    }
  }
}

.tabBg4 {
  background: url(../../assets/image/bg3.png) no-repeat;

  .typeName {
    color: #333333 !important;

    span {
      background: #333333;
    }
  }

  .title {
    .name {
      color: #333333;
    }

    .eName {
      color: #333333;
    }
  }
}

.title {
  display: flex;
  align-items: flex-end;
  min-width: 248px;
  margin-left: 35px;
  padding-top: 20px;

  .name {
    font-size: 22px;
    color: #ffffff;
  }

  .eName {
    font-size: 12px;
    color: #ffffff;
    margin-left: 10px;
  }
}

.typeName {
  display: flex;
  align-items: center;
  color: #fff;
  margin-left: 35px;
  margin-top: 30px;

  span {
    display: inline-block;
    width: 3px;
    height: 3px;
    background: #ffffff;
    border-radius: 50%;
    margin-right: 5px;
  }
}

.topTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .left {
    display: flex;
    align-items: center;

    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #0486fe;
      border-radius: 50%;
    }

    .titleBg {
      width: 49px;
      height: 25px;
      background: url(../../assets/image/titleBg.png) no-repeat;
      background-size: 100% 100%;
      text-align: center;
      line-height: 25px;
      color: #fff;
      font-size: 18px;
      margin-left: 5px;
      margin-right: 5px;
    }

    .name {
      color: #0486fe;
      font-size: 18px;
    }
  }
}

.progressDetail {
  padding: 12px 30px;
  border-radius: 5px;
  margin-bottom: 15px;

  .progressTitle {
    font-size: 14px;
    color: #333333;
  }

  .annotation {
    margin-top: 15px;
    color: #999999;
    font-size: 12px;
  }
}

.progressDetail:first-child {
  background: rgba(187, 177, 255, 0.07);
}

.progressDetail:nth-child(2) {
  background: rgba(231, 246, 255, 0.45);
}

.progressDetail:last-child {
  background: rgba(255, 245, 221, 0.33);
}

.newsDetail {
  border-bottom: 1px dashed #ebebeb;
  padding-bottom: 12px;
  margin-bottom: 12px;

  .progressTitle {
    font-size: 14px;
    color: #333333;
  }

  .annotation {
    margin-top: 15px;
    color: #999999;
    font-size: 12px;
  }
}

.newsDetail:last-child {
  border: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.other {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 43px 30px;

  .line {
    width: 1px;
    height: 39px;
    border: 1px solid #cdd3df;
  }

  .otherInfo {
    text-align: center;

    .otherTitle {
      color: #333333;
      font-size: 18px;
      font-weight: bold;
      margin: 20px 0;
    }

    .othermess {
      font-size: 14px;
      line-height: 24px;
      color: #999999;
    }
  }
}
</style>
