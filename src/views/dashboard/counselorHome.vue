<template>
  <div class="page-header-index-wide" style="user-select:none;">
    <div>
      <a-row :gutter="24" type="flex" :style="{ marginTop: '24px' }">
        <a-col :xl="14" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false">
            <a-row :gutter="20">
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }">
                <div class="tab tabBg1" @click="getRouter('/psychological/student/index')">
                  <div class="title">
                    <div class="name">
                      心理档案
                    </div>
                    <div class="eName">
                      Psychological Archiv
                    </div>
                  </div>

                  <div class="typeName">
                    <span></span>
                    用户管理
                  </div>
                </div>
              </a-col>
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }">
                <div class="tab tabBg2" @click="getRouter('/appointment/index')">
                  <div class="title">
                    <div class="name">
                      心理普查
                    </div>
                    <div class="eName">
                      Psychological survey
                    </div>
                  </div>

                  <div class="typeName">
                    <span></span>
                    测评活动
                  </div>
                </div>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }">
                <div class="tab tabBg3">
                  <div class="title">
                    <div class="name">
                      咨询预约
                    </div>
                    <div class="eName">
                      Consultation appoint
                    </div>
                  </div>

                  <div class="typeName">
                    <span></span>
                    心理咨询
                  </div>
                </div>
              </a-col>
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }" @click="openNewWindow()">
                <div class="tab tabBg4">
                  <div class="title">
                    <div class="name">
                      数据中心
                    </div>
                    <div class="eName">
                      Data Center
                    </div>
                  </div>

                  <div class="typeName">
                    <span></span>
                    数据可视化
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
        <a-col :xl="10" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false">
            <div class="topTitle">
              <div class="left">
                <span></span>
                <div class="titleBg">
                  测评
                </div>
                <div class="name">
                  任务
                </div>
              </div>
              <div class="right" @click="getRouter('/WxAuthorizationRecordsList/WxAuthorizationRecordsList')">
                更多 >
              </div>
            </div>
            <div class="progress">
              <div class="progressDetail">
                <div class="progressTitle">2024年度上半学期心理筛查</div>
                <a-progress :percent="100" />
                <div class="annotation">
                  开始时间：2024年09月02日 结束时间：2024年09月30日
                </div>
              </div>
              <div class="progressDetail">
                <div class="progressTitle">2024年度下半学期心理筛查</div>
                <a-progress :percent="100" />
                <div class="annotation">
                  开始时间：2024年02月26日 结束时间：2024年03月29日
                </div>
              </div>
              <div class="progressDetail">
                <div class="progressTitle">2023年度上半学期心理筛查</div>
                <a-progress :percent="100" />
                <div class="annotation">
                  开始时间：2023年09月01日 结束时间：2023年09月29日
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <a-row :gutter="24" type="flex" :style="{ marginTop: '24px' }">
        <a-col :xl="14" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false">
            <a-row :gutter="20">
              <div class="other">
                <div class="otherInfo">
                  <img src="../../assets/image/counselor/weiji.png" alt="">
                  <div class="otherTitle">
                    危机干预中心
                  </div>
                  <div class="othermess">
                    认知训练<br />
                    CBTI课程
                  </div>
                </div>
                <div class="line"></div>
                <div class="otherInfo">
                  <img src="../../assets/image/counselor/shequ.png" alt="">
                  <div class="otherTitle">
                    心理咨询室
                  </div>
                  <div class="othermess">
                    解忧赋能<br />
                    拥抱阳光成长
                  </div>
                </div>
                <div class="line"></div>
                <div class="otherInfo">
                  <img src="../../assets/image/counselor/fangsong.png" alt="">
                  <div class="otherTitle">
                    放松减压
                  </div>
                  <div class="othermess">
                    舒缓身心</br> 轻松释放压力
                  </div>
                </div>
              </div>
            </a-row>
          </a-card>
        </a-col>
        <a-col :xl="10" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false">
            <div class="topTitle">
              <div class="left">
                <span></span>
                <div class="titleBg">
                  亲子
                </div>
                <div class="name">
                  资讯
                </div>
              </div>
              <div class="right">
                更多 >
              </div>
            </div>
            <div class="progress">
              <div class="newsDetail">
                <div class="progressTitle">应对“开学综合征”</div>
                <div class="annotation">
                  1.用「清单法」化解不确定性
                  整理待办事项，把开学要做的琐事写下来，如买文具、交作业、打印课表等，每完成一项就打勾，减少大脑的混乱感。
                  制定简单计划，写下开学第一周的小目标，如“认识同桌”“整理错题本”，目标越具体越好，避免空泛。
                  2.用「身体行动」缓解情绪
                  运动出汗，让身体动起来能快速释放焦虑激素。
                  整理书包和书桌，通过整理物品梳理心情，增加对开学的掌控感。
                  3.应对社交焦虑的「破冰技巧」
                  准备轻松话题，提前想好和同学聊天的内容，如假期趣事、新电影/游戏，减少见面尴尬。
                  带小礼物分享，比如一包糖果、几张书签，用分享快速拉近距离。
                  遇到小矛盾，可以运用心理学上的“非暴力沟通”，多陈述事实和感受，少做评价。
                  4.情绪急救包：3分钟“478呼吸法”
                  深呼吸练习，用舌头抵住上颚，吸气4秒→屏住7秒→呼气8秒，重复5次，快速平静心跳。
                  写下担忧并撕掉，把焦虑的事情写在纸上，揉成团扔进垃圾桶，暗示自己“丢掉烦恼”。
                </div>
              </div>
              <div class="newsDetail">
                <div class="progressTitle">温暖和谐的亲子关系是心理健康基石</div>
                <div class="annotation">
                  第一：克制任性

                  6岁之前和孩子说“不”非常重要。一个三四岁的孩子闹，顶多就是在地上打滚，可等他到了十三四岁，他和你闹就不是在地上打滚了，他会离家出走，也可能跳楼或服毒自杀。

                  “今日说法”中有过这种例子：一个12岁的男孩就因为他妈妈骂了他一顿，服毒自杀了。

                  为什么？因为父母的爱在他的眼里是没有限制的，他知道你爱他，所以他用你的爱来威胁你，只是他不知道死的含义。

                  第二：防止压抑

                  闹没用之后你也不能让他太压抑了，怎么办？

                  可以鼓励他跟你交流。交流的原则是什么呢？三比一。三次满足他一次就好了，让他知道交流有用，但又不是每次都满足他！

                  第三：学会控制/第四：学会忍耐

                  家长不在的时候，孩子能不能做好自我管理自我控制？通过一些“小诱惑”，让孩子知道等待能获得更大的满足。

                  第五：防止自私

                  自私的养成就是他只要管好自己的成绩，家里什么都不用他管，所以孩子小的时候就要来矫正。

                  比如吃饭的时候给他立一规矩：大人没动筷子，小孩不能动，或是吃饭的时候家里谁没回来，饭菜就要给他拨出来一份，这些小事很简单但是很有效。

                  第六：经历挫折

                  挫折训练也是人生当中非常重要的，有很多人读到大学，硕士，博士跳楼的很多，失恋了跳楼，论文延迟答辩了跳楼。孩子小时候得让他吃点体力之苦，比如走路，有时候走不动也得走。

                  李玫瑾教授，1977年就读于人民大学哲学系，82年开始在中国人民公安大学任教，研究犯罪心理学。

                  为什么一位公安大学的教授，分析孩子教育问题会如此一针见血？

                  在研究犯罪心理学的过程中，李教授发现成年人的很多行为都和早年的成长环境、家庭教育有很大的关系。

                  1、一个人如果小的时候没有对善待过，你就不要指望他成年之后会善待这个社会。

                  2、孩子一岁以前，甚至三岁之前，最好由母亲亲自抚养。人在生命的早期是很无助的，他所有需要的满足和快乐都和抚养人息息相关。认人是最早的情感现象，也叫依恋，由此父母才获得抚养孩子的心理资本。

                  3、心理发展有关键期，孩子也一样，未成年时期大体可以分为12岁之前的依恋期，12-18岁叫青春期，依恋期是家庭教育孩子的最关键期，青春期需要一些社会的参与。

                  4、以性格为核心的人格，是决定人一生，尤其是45岁生活的关键，所以人格教育家庭要重视。

                  5、孩子的问题往往是成年人造就的，孩子的每种心理或行为问题，一定和父母的行为有关，和父母的教育方式有关。

                  6、有一种人脾气暴躁、容易激动的人，还有一种人平和大度，前一种人早年的抚养一定是亏欠的，后一种人早年的抚养一定是非常到位的。

                  7、真正的养育过程，肯定是唠叨的，是有言语的，这叫做有亲情抚养。言语发育和社会性发展（愿不愿意和人交流、亲近）、认知等能力都会在有亲情抚养和无亲情抚养的背景下表现不同。

                  8、孩子成长过程的心理阳光比智力更重要，所以我宁可让孩子上不了名牌大学，我一定让他活得快快乐乐。

                  9、孩子提的要求不合理，他的东西不能给他，怎么办？做到四个不要：一不要骂；二不要打；三不要说教，这个时候说什么孩子都听不进去，在他耳边的都是噪音。第四，你不要走开，他闹给家长看 ，所以你一定要看着他闹，温柔而坚定的拒绝。

                  10、未成年人的很多问题是滞后问题，比如当你发现孩子已经很难教育了，其实问题发生在很早之前。

                  11、青春期的孩子，教育一定要有方法。我们话有时候只要说到了，孩子知道了，你就不要非得让他承认错误。青春期的孩子让他承认错误是很难得。不要和孩子争执到底，那吵到最后反而会让孩子对你不恭敬。

                  12、告诉孩子，人一定能找到自己的一个位置，高考不是唯一出路。如果考不上，不要紧，你想做什么，我尽量支持你。

                  13、人在早年，吃的苦越多，他到后来承受力就越好。除了他可能暴力一点，有些事情容易暴力，但他绝对不会有天有地，遇到挫折就去跳楼自杀。尤其家里有儿子的，一定要苦着养，千万不要照顾太周到。逆子很多时候是百依百顺造成的。

                  14、孩子6岁之前管什么呢？很重要一点，就是几个问题，克制任性，防止压抑，学会控制，学会忍耐，防止自私，经历挫折。

                  15、挫折忍耐力和意志力相关，而意志力的培养，不是靠智力培养出来的，而是靠体力培养出来的，所以孩子小的时候，要让他吃一点体力之苦。

                  16、二胎家庭两个孩子发生争执怎么办？把教育的重点放在大的身上，不要放在护小的上面。

                  17、天生我才必有用，人的成功在于他的自信，孩子学习很重要，但是不是唯一。分数是老师管的，父母要管的是孩子你上学快乐不快乐，有没有人欺负你，有没有遇到什么困难，我可以帮助你什么。

                  18、要让孩子“参与”到家中的大事，不管他什么样的学习状态，家中的大事都要告诉他，听他的意见，让他做一点没有坏处。

                  19、关注孩子最好的朋友和家庭背景，孩子最好的朋友往往是孩子最大倾诉者，也是对孩子影响最大的人。观察孩子最好的朋友的行为，是了解孩子的最好方式。如果交的朋友不太好，你也可以问问孩子认为朋友的优缺点，表达你的意见就好。

                  20、善于从孩子的态度发现自己教育背景中的问题，有的时候你用什么方式教育，他就用什么方式对你。你尊重他，他就尊重你；你不尊重他，他就不尊重你。
                </div>
              </div>
              <div class="newsDetail">
                <div class="progressTitle">家校共育机制建设</div>
                <div class="annotation">
                  建立平等、尊重和互信的亲子关系是每个家庭都需要努力的目标。以下是一些建议，希望能够帮助您与孩子建立更加紧密的关系。

                  1. 平等交流

                  与孩子建立开放的沟通渠道，让他们感到自己的意见和想法被重视。

                  避免居高临下的态度，尝试站在孩子的角度看问题，理解他们的感受和需求。

                  鼓励孩子表达自己，即使他们的观点与您不同，也要耐心倾听并尊重他们的意见。

                  2. 相互尊重

                  尊重孩子作为个体的独立性和尊严，不随意贬低或嘲笑他们。

                  尊重孩子的隐私和个人空间，避免未经允许就进入他们的房间或查看个人物品。

                  尊重孩子的兴趣和选择，即使你不完全同意，也要给予适当的支持和鼓励。

                  3. 建立信任

                  通过言行一致来建立信任，承诺要做到的事情一定要兑现。

                  在孩子遇到困难时，给予支持和帮助，让他们感受到你是他们可以依靠的人。

                  避免过度干预孩子的生活，相信他们能够处理好自己的事务，同时在必要时提供适当的指导。

                  4. 共同成长

                  与孩子一起设定目标，并共同努力实现，让他们感受到成长的喜悦。

                  在孩子面临挑战时，鼓励他们勇敢面对，提供必要的支持和鼓励。

                  分享自己的经验和知识，但要避免将自己的价值观强加给孩子，而是让他们自主形成自己的价值观。

                  5. 积极参与

                  参与孩子的学校活动，了解他们在学校的生活和学习情况。

                  定期与孩子的老师沟通，了解孩子在学校的表现和进步。

                  在孩子的重要决策时刻，如选择学校或专业，提供建议和支持。

                  6. 榜样作用

                  作为家长，要以身作则，成为孩子学习的榜样。

                  展示出诚实、尊重和负责任的行为，让孩子从你身上学到正面的价值观。

                  在面对困难和挑战时，展现出坚韧不拔的精神，激励孩子也能勇敢面对生活的挑战。

                  7. 持续的努力

                  建立平等、尊重和互信的亲子关系不是一蹴而就的，需要持续的努力和不断的调整。

                  随着孩子年龄的增长，他们的需求和想法也会发生变化，家长需要不断适应和更新自己的教育方式。

                  定期反思自己的教育方法，与孩子一起成长和进步。
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { psEvaluationUser } from '@/api/api'
export default {
  name: 'EducationHome',
  components: {},
  data () {
    return {
    }
  },
  filters: {

  },
  created () {
    this.getPatient()
  },
  methods: {
    // 打开大屏
    openNewWindow () {
      window.open(this.$router.resolve({ name: 'screen' }).href, '_blank');
    },
    // 页面跳转
    getRouter (path) {
      this.$router.push({ path: path });
    },
    // 获取测评任务
    getPatient () {
      psEvaluationUser({}).then((res) => {
        if (res.success) {
        } else {
        }
      });
    }
  }
}
</script>

<style lang="less" scoped>
.page-header-index-wide {
  background: url(../../assets/image/homeBg.png) no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  padding: 30px;
}

.tab {
  width: 100%;
  height: 170px;
}

.tabBg1 {
  background: url(../../assets/image/counselor/one.png) no-repeat;
}

.tabBg2 {
  background: url(../../assets/image/counselor/two.png) no-repeat;
}

.tabBg3 {
  background: url(../../assets/image/counselor/three.png) no-repeat;

  .typeName {
    color: #333333 !important;

    span {
      background: #333333;
    }
  }

  .title {
    .name {
      color: #333333;
    }

    .eName {
      color: #333333;
    }
  }
}

.tabBg4 {
  background: url(../../assets/image/counselor/four.png) no-repeat;

  .typeName {
    color: #333333 !important;

    span {
      background: #333333;
    }
  }

  .title {
    .name {
      color: #333333;
    }

    .eName {
      color: #333333;
    }
  }
}

.title {
  display: flex;
  align-items: flex-end;
  min-width: 248px;
  margin-left: 35px;
  padding-top: 20px;

  .name {
    font-size: 22px;
    color: #ffffff;
  }

  .eName {
    font-size: 12px;
    color: #ffffff;
    margin-left: 10px;
  }
}

.typeName {
  display: flex;
  align-items: center;
  color: #fff;
  margin-left: 35px;
  margin-top: 30px;

  span {
    display: inline-block;
    width: 3px;
    height: 3px;
    background: #ffffff;
    border-radius: 50%;
    margin-right: 5px;
  }
}

.topTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .left {
    display: flex;
    align-items: center;

    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #0486fe;
      border-radius: 50%;
    }

    .titleBg {
      width: 49px;
      height: 25px;
      background: url(../../assets/image/titleBg.png) no-repeat;
      background-size: 100% 100%;
      text-align: center;
      line-height: 25px;
      color: #fff;
      font-size: 18px;
      margin-left: 5px;
      margin-right: 5px;
    }

    .name {
      color: #0486fe;
      font-size: 18px;
    }
  }
}

.progressDetail {
  padding: 12px 30px;
  border-radius: 5px;
  margin-bottom: 15px;

  .progressTitle {
    font-size: 14px;
    color: #333333;
  }

  .annotation {
    margin-top: 15px;
    color: #999999;
    font-size: 12px;
  }
}

.progressDetail:first-child {
  background: rgba(187, 177, 255, 0.07);
}

.progressDetail:nth-child(2) {
  background: rgba(231, 246, 255, 0.45);
}

.progressDetail:last-child {
  background: rgba(255, 245, 221, 0.33);
}

.newsDetail {
  border-bottom: 1px dashed #ebebeb;
  padding-bottom: 12px;
  margin-bottom: 12px;

  .progressTitle {
    font-size: 14px;
    color: #333333;
  }

  .annotation {
    margin-top: 15px;
    color: #999999;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.newsDetail:last-child {
  border: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.other {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 43px 30px;

  .line {
    width: 1px;
    height: 39px;
    border: 1px solid #cdd3df;
  }

  .otherInfo {
    text-align: center;

    .otherTitle {
      color: #333333;
      font-size: 18px;
      font-weight: bold;
      margin: 20px 0;
    }

    .othermess {
      font-size: 14px;
      line-height: 24px;
      color: #999999;
    }
  }
}
</style>
