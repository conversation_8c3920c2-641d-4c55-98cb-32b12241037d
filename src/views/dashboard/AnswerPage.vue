<template>
  <div class="answer-page">
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="audioUrlPrefix + audioUrl" type="audio/mpeg" />
    </audio>
    <div class="page-title">{{ measure.name }}</div>

    <div class="page-content">
      <div class="content-top">
        <div class="top-item">
          <a-icon type="ordered-list"/>
          <span>{{ (isShowQuestion ? questionNum : '--') + '/' + questionList.length }}</span>
        </div>

        <div class="top-item">
          <a-icon type="clock-circle" theme="twoTone"/>
          <span>{{ timer }}</span>
        </div>

        <div class="top-item" v-if="!isShowQuestion || measure.id !== 'd12d1f23e45c413c8179da35f7d9f7b0'">
          <a-switch v-model="audioChange" @change="handleAudioChange"/>
          <span>语音读题</span>
          <a-select v-model="readType" :disabled="!readType" style="width: 120px" @change="handleChange">
            <a-select-option :value="0">
              文本阅读
            </a-select-option>
            <a-select-option :value="1">
              普通话
            </a-select-option>
            <a-select-option :value="2">
              粤语
            </a-select-option>
          </a-select>
        </div>
        <!-- <div class="top-item" v-if="!isShowGesell && !isShowQuestion">
          <a-switch v-model="audioChange" @change="handleAudioChange"/>
          <span>语音读题</span>
        </div> -->
      </div>

      <!-- 量表确认信息 -->
      <div class="content-info" v-if="!isShowQuestion">
        <!-- <h3 class="info-sub-title" v-if="subsectionTitle">{{ subsectionTitle }}</h3> -->

        <p class="info-description" v-html="measure.description"></p>

        <a-button type="primary" @click="quesionStart">开始</a-button>
      </div>

      <div class="cotent-main" v-if="isShowQuestion && !isShowGesell">
        <a-alert v-if="notAnswerArr.length > 0" message="有未答完的题目，请点击左侧滑动条中未完成的题目信息进行补充答题。" type="error"/>

        <h3 class="main-title" v-if="subsectionTitle">{{ subsectionTitle }}</h3>
        <h3 class="main-sub-title" v-if="subsectionSubTitle">{{ subsectionSubTitle }}</h3>

        <div class="main-content">
          <div class="content-left">
            <a-slider :marks="marks" vertical :max="questionList.length" :value="questionNum" @change="onChangeSlider"/>
          </div>

          <div class="content-right">
            <ChooseItem
              v-if="questionList[questionIndex].titleType === '1' || questionList[questionIndex].titleType === '2'"
              :question="questionList[questionIndex]" @submit="submit"></ChooseItem>
            <ManualEntryItem
              v-if="['3', '4', '5', '6', '11', '12', '13', '14'].includes(questionList[questionIndex].titleType)"
              :question="questionList[questionIndex]" @submit="submit"></ManualEntryItem>
            <CanvasItem v-if="questionList[questionIndex].titleType === '7'" :question="questionList[questionIndex]"
                        @submit="submit"></CanvasItem>
            <DigitalBreadthItem v-if="questionList[questionIndex].titleType === '8'"
                    :question="questionList[questionIndex]" :audioChange="audioChange" :topicEnd="topicEnd"
                    :readType="readType" @submit="submit"></DigitalBreadthItem>
            <ConnectItem v-if="questionList[questionIndex].titleType === '9'" :question="questionList[questionIndex]"
                         @submit="submit"></ConnectItem>

            <div class="right-btn">
              <a-button :class="[questionNum <= 1 && 'no-choose']" @click="previousPage" type="primary">
                <a-icon type="left"/>
                上一题
              </a-button>
              <a-button :class="[questionNum >= questionList.length && 'no-choose']" @click="nextPage" type="primary">
                下一题
                <a-icon type="right"/>
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <Gesell v-if="isShowQuestion && isShowGesell" :questionList="questionList" @submit="submit" @commit="commit"></Gesell>
    </div>
  </div>
</template>

<script>
import {getAction, postAction} from '@/api/manage'
import {mixinDevice} from '@/utils/mixin.js'
import {voiceCancel, voicePrompt} from '@/utils/util'
import ChooseItem from './components/chooseItem.vue'
import ManualEntryItem from './components/manualEntryItem.vue'
import DigitalBreadthItem from './components/digitalBreadthItem.vue'
import CanvasItem from './components/canvasItem.vue'
import ConnectItem from './components/connectItem.vue'
import Gesell from './components/GesellPage.vue'
// 子组件需要传回的参数: score、answer、type(都不是必填，视情况而定)
// 题目类型通过titleType区分，目前存在的类型：
// 1 - 题目、选项均为文字
// 2 - 题目、选项均为图片
// 3 - 自输入分值
// 4 - 自输入内容
// 5 - 选择时间
// 6 - 自输入数字
// 7 - 画图
// 8 - 数字广度
// 9 - 顺序连线
// 11 - 选择日期
// 12 - 选择月份
// 13 - 选择星期几
// 14 - 选择城市

export default {
  name: 'AnswerForm',
  components: {
    [ChooseItem.name]: ChooseItem,
    [ManualEntryItem.name]: ManualEntryItem,
    [CanvasItem.name]: CanvasItem,
    [DigitalBreadthItem.name]: DigitalBreadthItem,
    [ConnectItem.name]: ConnectItem,
    [Gesell.name]: Gesell,
  },
  mixins: [mixinDevice],
  data() {
    return {
      timer: '00:00:00',
      time: 0,
      totalTime: 0,

      audioChange: false,
      questionNum: 1,
      userId: '',
      type: '',
      subsectionTitle: '',
      subsectionSubTitle: '',
      subsectionContent: '',
      measure: {},
      //未答完题目标准指针
      notAnswerArr: [],
      isShowQuestion: false,
      questionList: [],
      questionIndex: 0,
      dxResult: {},
      marks: {},
      url: {
        loadAnswerPageData: '/diagnosis/dxAnswer/loadAnswerPageData', // 获取题目配置
        addOption: '/diagnosis/dxResult/addOption', // 保存答案
        getNotAnswer: '/diagnosis/dxAnswer/getNotAnswer', // 获取没答题的题目
        uploadResult: '/diagnosis/dxResult/uploadResult' // Gesell量表提交
      },
      isShowGesell: false,
      readType: 0,
      audioUrlPrefix: window._CONFIG['measureAudioURL'], //音频前缀，默认为空，由于本地没有音频文件，本地调试的时候此处配置线上绝对路径  https://zcpm.zhisongkeji.com
      audioUrl: '',
      palyStatus: '',
      topicEnd: true
    }
  },
  watch: {
    audioChange(val) {
      if (val) {
        this.readType = 1
        this.playAudio()
      } else {
        this.readType = 0
        this.pauseAudio()
        if (this.questionList[this.questionIndex].titleType === '8') this.topicEnd = true
      }
    }
  },
  created() {
    //初始化答题页数据
    this.loadData()
  },
  methods: {
    handleChange(value) {
      this.readType = Number(value)
      if (this.readType) {
        this.playAudio()
      } else {
        this.audioChange = false
        this.pauseAudio()
      }
    },

    // 播放语音
    playAudio() {
      if (this.audioChange) {
        //题目信息
        if (!this.isShowQuestion) {
          this.playTopic(1)
        } else {
          this.playQuestion(1)
          if (this.questionList[this.questionIndex].titleType === '8') this.topicEnd = false
        }
      }
    },

    playTopic(step) {
      if (step === 1) {
        this.audioUrl = this.readType === 1 ? '/static/measure/common/pt/measure_prefix.mp3' : '/static/measure/common/gd/measure_prefix.mp3'
        this.palyStatus = 'topicPrefix'
      }
      if (step === 2) {
        this.audioUrl = this.readType === 1 ? this.measure.audioPath : this.measure.audioPathGd
        this.palyStatus = 'topic'
      }
      if (step === 3) {
        this.audioUrl = this.readType === 1 ? '/static/measure/common/pt/measure_suffix.mp3' : '/static/measure/common/gd/measure_suffix.mp3'
        this.palyStatus = 'topicSuffix'
      }
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
      })
    },

    playQuestion(step) {
      if (step === 1) {
        this.audioUrl = this.readType === 1 ? this.questionList[this.questionIndex].audioPath : this.questionList[this.questionIndex].audioPathGd
        this.palyStatus = 'question'
      }
      if (step === 2) {
        this.audioUrl = this.readType === 1 ? '/static/measure/common/pt/question_suffix.mp3' : '/static/measure/common/gd/question_suffix.mp3'
        this.palyStatus = 'questionSuffix'
      }
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
      })
    },

    pauseAudio() {
      this.$refs.music.pause()
      this.palyStatus = ''
    },

    handleEnded() {
      if (this.palyStatus === 'topicPrefix') {
        this.playTopic(2)
      } else if (this.palyStatus === 'topic') {
        this.playTopic(3)
      } else if (this.measure.id != 'ff511081d69248f0b27f1d329d280675' && this.measure.id != 'd803a9716d4b415cbc9c0c6396580b85' && this.palyStatus === 'question' && this.questionList[this.questionIndex].titleType == 1) {
        this.playQuestion(2)
      } else if (this.palyStatus === 'question' && this.questionList[this.questionIndex].titleType == 8) {
        this.topicEnd = true
      } else {
        this.palyStatus = ''
      }
    },


    handleAudioChange(fixed) {
      this.audioChange = fixed
    },
    /**
     * 加载量表信息数据
     */
    loadData() {
      this.type = this.$route.params.type
      let params = {}
      if (this.type == '1' || this.type == '0') {
        this.userId = this.$route.params.userId
        params = {userId: this.userId, type: this.$route.params.type}
      } else if (this.type == '2') {
        params = {resultId: this.$route.params.resultId, type: this.$route.params.type}
      }

      getAction(this.url.loadAnswerPageData, params).then((res) => {
        if (res.success) {
          if (res.result) {
            this.isShowQuestion = false
            this.notAnswerArr = []
            if (res.result.measure) this.measure = res.result.measure
            if (res.result.quesionList) this.questionList = res.result.quesionList
            this.isShowGesell = (this.measure.id === 'c178fc3050c1d8f7e287f1230e1881e7')

            if (res.result.quesionIndex != null) {
              this.questionIndex = res.result.quesionIndex
              this.questionNum = res.result.quesionIndex + 1
              this.subsectionContent = res.result.quesionList[res.result.quesionIndex].subsectionContent
              this.subsectionTitle = res.result.quesionList[res.result.quesionIndex].subsectionTitle
              this.subsectionSubTitle = res.result.quesionList[res.result.quesionIndex].subsectionSubTitle
            }
            if (res.result.dxResult) this.dxResult = res.result.dxResult
          } else {
            this.$router.push({
              name: 'dashboard',
            })
            this.$notification['success']({
              message: '测试完成',
              description: this.measure.name + '量表测试已完成',
            })
          }
        } else {
          this.$message.warning('数据异常，请返回首页后重新开始答题。（我们保存了答题进度，点击继续答题开始续答）', 5)
          setTimeout(function () {
            this.$router.push({
              name: 'dashboard',
            })
          }, 5000)
        }
      })
    },
    /**
     * 答题开始
     */
    quesionStart() {
      this.isShowQuestion = true
      let date = new Date()
      this.time = date.getTime()
      this.totalTime = date.getTime()
      setInterval(() => {
        this.startTimer()
      }, 10)
      this.playAudio()
    },

    // 时间格式化
    startTimer() {
      const intervals = new Date().getTime() - this.time
      const b = (intervals % 60000) / 1000
      const c = (intervals % 3600000) / 60000
      const d = intervals / 3600000
      const timerSecond = b < 10 ? '0' + Math.floor(b) : Math.floor(b)
      const timerMinute = c < 10 ? '0' + Math.floor(c) : Math.floor(c)
      const timerHour = d < 10 ? '0' + Math.floor(d) : Math.floor(d)
      this.timer = timerHour + ':' + timerMinute + ':' + timerSecond
    },
    /**
     * 上一页
     * */
    previousPage() {
      if (this.questionIndex <= 0) return
      if (this.questionIndex >= 1) {
        this.questionIndex--
        if (this.questionList[this.questionIndex].subsectionContent) {
          this.isShowQuestion = false
        }
        this.subsectionContent = this.questionList[this.questionIndex].subsectionContent
        this.subsectionTitle = this.questionList[this.questionIndex].subsectionTitle
        this.subsectionSubTitle = this.questionList[this.questionIndex].subsectionSubTitle
      }
      this.questionNum--
      this.playAudio()
      this.refreshNotAnswer(this.questionNum)
    },
    /**
     * 下一页
     */
    nextPage() {
      if (this.questionIndex >= this.questionList.length - 1) return
      if (this.questionIndex + 1 < this.questionList.length) {
        this.questionIndex++
        if (this.questionList[this.questionIndex].subsectionContent) {
          this.isShowQuestion = false
        }
        this.subsectionContent = this.questionList[this.questionIndex].subsectionContent
        this.subsectionTitle = this.questionList[this.questionIndex].subsectionTitle
        this.subsectionSubTitle = this.questionList[this.questionIndex].subsectionSubTitle
      }
      this.questionNum++
      this.playAudio()
      this.refreshNotAnswer(this.questionNum)
    },

    // 提交答案
    submit(params, flag = false) {
      const optionInfo = {
        ...params,
        userId: this.userId,
        measureId: this.measure.id,
        resultId: this.dxResult.id,
        time: new Date() - this.totalTime,
        totalTime: new Date() - this.time
      }
      if (!flag) optionInfo.questionId = this.questionList[this.questionIndex].id
      this.totalTime = new Date()

      postAction(this.url.addOption, optionInfo)
        .then((res) => {
          if (res.success) {
            if (res.code === 10001) {
              this.$router.push({
                path: '/ta/TaMeasureTaskList'
              })
            } else {
              if (this.questionNum == this.questionList.length) {
                this.$message.warning('还有题目未答完')
              } else {
                this.questionNum++
              }
            }
            this.refreshNotAnswer(this.questionNum)
          } else {
            this.$message.warning(res.message)
          }
        })
        .catch((err) => {
          if (this.notAnswerArr.indexOf(this.questionNum) === -1) {
            this.notAnswerArr.push(this.questionNum)
          }
          this.questionNum++
          this.refreshNotAnswerArr()
        })

      //下一题
      if (this.questionIndex + 1 < this.questionList.length) {
        this.questionIndex++
        if (this.questionList[this.questionIndex].subsectionContent) {
          this.isShowQuestion = false
        }
        this.subsectionContent = this.questionList[this.questionIndex].subsectionContent
        this.subsectionTitle = this.questionList[this.questionIndex].subsectionTitle
        this.subsectionSubTitle = this.questionList[this.questionIndex].subsectionSubTitle
        this.playAudio()
      }
    },

    // Gesell量表最后提交
    commit() {
      getAction(this.url.uploadResult, {resultId: this.dxResult.id}).then((res) => {
        if (res.success) {
          this.$router.push({
            name: 'dashboard',
          })
          return
        }
        this.$message.warning('数据异常，请返回首页后重新开始答题。（我们保存了答题进度，点击继续答题开始续答）', 5)
        setTimeout(function () {
          this.$router.push({
            name: 'dashboard',
          })
        }, 5000)
      })
    },

    // 选择题号
    onChangeSlider(value) {
      this.questionNum = value
      this.questionIndex = value - 1
      this.refreshNotAnswer(this.questionNum)
    },

    // 获取未回答的题目index
    refreshNotAnswer(val) {
      getAction(this.url.getNotAnswer, {
        measureId: this.measure.id,
        resultId: this.dxResult.id,
        questionNum: val,
      }).then((res) => {
        if (res.success && res.result) {
          this.notAnswerArr = res.result.questionArr
          this.refreshNotAnswerArr()
        }
      })
    },

    // 渲染未答题样式
    refreshNotAnswerArr() {
      let marks = {}
      for (let key in this.notAnswerArr) {
        marks[this.notAnswerArr[key]] = {
          style: {
            color: '#f50',
          },
          label: '第' + this.notAnswerArr[key] + '题未答完',
        }
      }
      this.marks = marks
    },

    // 去重
    unique(arr, arr2) {
      for (var i = 0; i < arr2.length; i++) {
        if (arr.indexOf(arr2[i]) === -1) {
          arr.push(arr2[i])
        }
      }
      return arr
    },
  },
  destroyed() {
    clearInterval(this.timer)
    voiceCancel()
  }
}
</script>

<style lang="scss">
@import './AnswerPage.scss';
</style>