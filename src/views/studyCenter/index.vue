<template>
  <div class="page" style="user-select:none;">
    <div class="bg">
      <img src="@/assets/image/studyCenter.png" alt="">
    </div>
    <div class="box">
      <div class="boxLeft">
        <div class="type">
          <div @click="getChange(index, 'lingyuIndex')" :class="index == lingyuIndex ? 'active' : ''"
            v-for="(i, index) in lingyuList" :key="index">
            {{ i }}
          </div>
        </div>
        <div class="list">
          <div v-for="item, index in 10" :key="index">
            <img src="@/assets/img/img.png" alt="">
            <div class="button">
              开始学习
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EducationHome',
  components: {
  },
  data() {
    return {
      lingyuIndex: '0',
      lingyuList: ['全部', '家庭教育', '名师讲堂', '心理与生活'],
      url: {
        list: '/base/baseMaterial/defaultList',
        category: '/base/baseMaterialCategory/listAll',
      }
    }
  },
  filters: {

  },
  created() {

  },
  methods: {
    getChange(index, type) {
      this[type] = index
    },
    // 路由跳转
    getRouter() {
      this.$router.push({ path: '/study/detail' });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  display: flex;
  align-items: flex-start;

  .top {
    padding: 21px 24px;
    background: #FFFFFF;
    border-radius: 16px;
  }

  .bottom {
    margin-top: 20px;
    padding: 21px 24px;
    background: #FFFFFF;
    border-radius: 16px;

    .bottomInfo {
      border-top: 1px dashed #D5D5D5;
      margin-top: 20px;

      div {
        display: flex;
        align-items: center;
        border-radius: 2px;
        font-size: 14px;
        color: #333333;
        height: 48px;
        margin-top: 20px;

        img {
          margin-left: 50px;
          margin-right: 10px;
        }
      }

      div:nth-child(1) {
        background: rgba(125, 171, 250, 0.09);
      }

      div:nth-child(2) {
        background: rgba(250, 139, 125, 0.09);
      }

      div:nth-child(3) {
        background: rgba(255, 191, 95, 0.09);
      }
    }
  }

  .list {
    margin-top: 28px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    >div {
      margin-bottom: 20px;
      margin-right: 20px;
      img {
        width: 187px;
        height: 110px;
        border-radius: 4px;
        margin-bottom: 10px;
      }

      .button {
        width: 58px;
        height: 26px;
        background: #35A2FF;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        text-align: center;
        line-height: 26px;
      }
    }
  }

  .tab {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .tabLeft {
      font-size: 16px;
      color: #333333;
    }
  }

  .leftTop {
    border-bottom: 1px solid #EBEBEB
  }

  .leftBottom {
    margin-top: 20px;


  }

  .tabRight {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: 10px;

    div {
      margin-right: 30px;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
    }

    .active {
      color: #0486FE;
    }
  }

  .boxLeft {
    flex: 1;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 30px 26px;
  }

  .boxRight {
    width: 320px;
    border-radius: 16px;
    margin-left: 20px;
  }
}

.titile {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .leftTitle {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333333;

    span {
      display: inline-block;
      width: 4px;
      height: 20px;
      background: #0486FE;
      margin-right: 13px;
    }
  }
}

.historyInfo {
  border-top: 1px dashed #D5D5D5;
  margin-top: 20px;
  padding-top: 20px;

  .button {
    margin: 20px auto;
    width: 100px;
    height: 28px;
    border-radius: 14px;
    border: 1px solid #D5D5D5;
    text-align: center;
    line-height: 26px;
    font-size: 14px;
    color: #999999;
    cursor: pointer;
  }

  .xinde {
    border: 1px solid #35A2FF;
    color: #35A2FF;
  }

  .name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
  }

  .right {
    font-size: 14px;
    display: flex;
    align-items: center;
  }

  .queren {
    color: #35A51B;

    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #35A51B;
      border-radius: 50%;
      margin-right: 5px;
    }
  }

  .wancheng {
    color: #999999;

    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #999999;
      border-radius: 50%;
      margin-right: 5px;
    }
  }
}

.bg {
  img {
    width: 100%;
    margin-bottom: 20px;
  }
}

.type {
  display: flex;
  align-items: center;
  justify-content: center;

  >div {
    font-size: 16px;
    color: #666666;
    margin-right: 150px;
    cursor: pointer;
  }

  .active {
    color: #35A2FF;
    position: relative;
  }

  .active::after {
    content: "";
    position: absolute;
    width: 18px;
    height: 3px;
    background: #35A2FF;
    left: 0px;
    right: 0px;
    bottom: 0px;
    top: 30px;
    margin: auto;
    border-radius: 1px;
  }
}
</style>
