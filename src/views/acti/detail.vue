<template>
  <div class="page" style="user-select:none;">

    <div class="box">
      <div class="title">
        心理学入行必看！和你聊聊心理学与心理工作
      </div>
      <div class="time">
        <div>
          2023/08/23 14:00:00
        </div>
        <div class="shoucang">
          <img src="@/assets/image/noheart.png" alt="">收藏
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'detail',
  components: {
  },
  data() {
    return {
    }
  },
  filters: {

  },
  created() {

  },
  methods: {
    // 路由跳转
    getRouter() {
      this.$router.push({ path: '/appointment/detail' });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  background: #fff;
  padding: 40px 60px;
  border-radius: 16px;

  .title {
    text-align: center;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 28px;
  }
  .time{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 20px;
    .shoucang{
      margin-right: 300px;
      margin-left: 260px;
      img{
        width: 22px;
        margin-right: 10px;
      }
    }
  }
}
</style>
