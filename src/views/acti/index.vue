<template>
  <div class="page" style="user-select:none;">
    <div class="bg">
      <img src="@/assets/image/huodong.png" alt="">
    </div>
    <div class="box">
      <div class="boxLeft">
        <div class="type">
          <div @click="getChange(index, 'lingyuIndex')" :class="index == lingyuIndex ? 'active' : ''" v-for="(i, index) in lingyuList" :key="index">
            {{ i }}
          </div>
        </div>
        <div class="list">
          <div class="listInfo" @click="getRouter">
            <img src="@/assets/img/img.png" alt="">
            <div class="right">
              <div class="title">
                “心连心”亲子沟通工作坊
              </div>
              <div class="mess">
                以家庭为单位参与合作任务（如盲行挑战、拼图共创），改善亲子沟通模式，增强相互理解与信任
              </div>
              <div class="time">
                2025/02/20 14:00:00
              </div>
            </div>
          </div>
          <div class="listInfo" @click="getRouter">
            <img src="@/assets/img/img.png" alt="">
            <div class="right">
              <div class="title">
                “情绪小管家”成长营
              </div>
              <div class="mess">
                通过绘画、角色扮演等互动游戏，帮助7-12岁学生识别情绪、掌握疏导技巧，培养情绪管理能力
              </div>
              <div class="time">
                2025/02/15 14:00:00
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="boxRight">
        <div class="top">
          <div class="titile">
            <div class="leftTitle">
              <span></span>
              活动记录
            </div>
            <div>
              更多 >
            </div>
          </div>
          <div class="history">
            <div class="historyInfo">
              <div class="name">
                <div class="left">
                  活动名称： “心连心”亲子沟通工作坊
                </div>
              </div>
              <div class="name">
                <div class="left">
                  活动时间：2025/02/20
                </div>
              </div>
              <div class="name">
                <div class="left">
                  报名时间：2025/02/18
                </div>
              </div>
              <div class="name">
                <div class="left">
                  状 态：已结束
                </div>
              </div>
              <!-- <div class="button">
                取消报名
              </div> -->
            </div>
            <div class="historyInfo">
              <div class="name">
                <div class="left">
                  活动名称：“情绪小管家”成长营
                </div>
              </div>
              <div class="name">
                <div class="left">
                  活动时间：2025/02/15
                </div>
              </div>
              <div class="name">
                <div class="left">
                  报名时间：2025/02/12
                </div>
              </div>
              <div class="name">
                <div class="left">
                  状 态：已结束
                </div>
              </div>
              <!-- <div class="button xinde">
                写心得
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EducationHome',
  components: {
  },
  data () {
    return {
      lingyuIndex: '0',
      lingyuList: ['全部', '进行中', '已结束'],
      zixunIndex: '0',
      zixunList: ['全部', '视频咨询', '语音咨询', '面对面咨询'],
      list: [{
        url: require('@/assets/image/zixun.png'),
        mess: '我需要心理咨询吗？'
      }, {
        url: require('@/assets/image/zixun1.png'),
        mess: '如何选择咨询师？'
      }, {
        url: require('@/assets/image/zixun2.png'),
        mess: '第一次咨询会发生什么？'
      }]
    }
  },
  filters: {

  },
  created () {

  },
  methods: {
    getChange (index, type) {
      this[type] = index
    },
    // 路由跳转
    getRouter () {
      this.$router.push({ path: '/acti/detail' });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  display: flex;
  align-items: flex-start;

  .top {
    padding: 21px 24px;
    background: #ffffff;
    border-radius: 16px;
  }

  .bottom {
    margin-top: 20px;
    padding: 21px 24px;
    background: #ffffff;
    border-radius: 16px;

    .bottomInfo {
      border-top: 1px dashed #d5d5d5;
      margin-top: 20px;

      div {
        display: flex;
        align-items: center;
        border-radius: 2px;
        font-size: 14px;
        color: #333333;
        height: 48px;
        margin-top: 20px;

        img {
          margin-left: 50px;
          margin-right: 10px;
        }
      }

      div:nth-child(1) {
        background: rgba(125, 171, 250, 0.09);
      }

      div:nth-child(2) {
        background: rgba(250, 139, 125, 0.09);
      }

      div:nth-child(3) {
        background: rgba(255, 191, 95, 0.09);
      }
    }
  }

  .list {
    margin-top: 28px;

    .listInfo {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .infoButton {
        display: flex;
        align-items: center;

        div {
          width: 62px;
          height: 28px;
          border-radius: 14px;
          border: 1px solid #148efe;
          text-align: center;
          line-height: 26px;
          font-size: 14px;
          color: #188ffe;
          margin-left: 20px;
          cursor: pointer;
        }
      }

      .infoCenter {
        flex: 1;
        margin-left: 20px;

        .name {
          font-size: 16px;
          color: #333333;
          margin-bottom: 15px;
        }

        .tag {
          display: flex;
          align-items: center;
          margin-bottom: 15px;

          div {
            padding: 6px 10px;
            background: rgba(176, 176, 176, 0.14);
            border-radius: 2px;
            font-size: 14px;
            color: #666666;
            margin-right: 20px;
          }
        }

        .introduce {
          font-size: 14px;
          color: #666666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .tab {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .tabLeft {
      font-size: 16px;
      color: #333333;
    }
  }

  .leftTop {
    border-bottom: 1px solid #ebebeb;
  }

  .leftBottom {
    margin-top: 20px;
  }

  .tabRight {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: 10px;

    div {
      margin-right: 30px;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
    }

    .active {
      color: #0486fe;
    }
  }

  .boxLeft {
    flex: 1;
    background: #ffffff;
    border-radius: 16px;
    padding: 30px 26px;
  }

  .boxRight {
    width: 320px;
    border-radius: 16px;
    margin-left: 20px;
  }
}

.titile {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .leftTitle {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333333;

    span {
      display: inline-block;
      width: 4px;
      height: 20px;
      background: #0486fe;
      margin-right: 13px;
    }
  }
}

.historyInfo {
  border-top: 1px dashed #d5d5d5;
  margin-top: 20px;
  padding-top: 20px;

  .button {
    margin: 20px auto;
    width: 100px;
    height: 28px;
    border-radius: 14px;
    border: 1px solid #d5d5d5;
    text-align: center;
    line-height: 26px;
    font-size: 14px;
    color: #999999;
    cursor: pointer;
  }

  .xinde {
    border: 1px solid #35a2ff;
    color: #35a2ff;
  }

  .name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
  }

  .right {
    font-size: 14px;
    display: flex;
    align-items: center;
  }

  .queren {
    color: #35a51b;

    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #35a51b;
      border-radius: 50%;
      margin-right: 5px;
    }
  }

  .wancheng {
    color: #999999;

    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #999999;
      border-radius: 50%;
      margin-right: 5px;
    }
  }
}

.bg {
  img {
    width: 100%;
    margin-bottom: 20px;
  }
}

.type {
  display: flex;
  align-items: center;
  justify-content: center;

  > div {
    font-size: 16px;
    color: #666666;
    margin-right: 150px;
    cursor: pointer;
  }

  .active {
    color: #35a2ff;
    position: relative;
  }

  .active::after {
    content: '';
    position: absolute;
    width: 18px;
    height: 3px;
    background: #35a2ff;
    left: 0px;
    right: 0px;
    bottom: 0px;
    top: 30px;
    margin: auto;
    border-radius: 1px;
  }
}

.listInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #d9d9d9;
  padding-bottom: 20px;
  margin-bottom: 20px;
  cursor: pointer;
  img {
    width: 125px;
    height: 125px;
    margin-right: 20px;
  }

  .title {
    font-size: 16px;
    color: #333333;
    font-weight: 600;
  }
  .mess {
    font-size: 14px;
    color: #666666;
    line-height: 29px;
    margin: 10px 0;
  }
  .time {
    font-size: 14px;
    color: #999999;
  }
  .right {
    flex: 1;
  }
}
.listInfo:last-child {
  border-bottom: none;
}
</style>
