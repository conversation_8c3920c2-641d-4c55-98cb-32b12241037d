<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :lg="6" :sm="8">
            <a-form-item label="主题">
              <j-input placeholder="请输入主题" v-model="queryParam.title"></j-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8">
            <!-- TODO: -->
            <a-form-item label="发帖时间">
              <a-range-picker
                style="width: 100%"
                v-model="queryParam.createTimeRange"
                format="YYYY-MM-DD"
                :placeholder="['开始时间', '结束时间']"
                @change="onDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator" v-has="'mailboxList:edit'">
      <a-button @click="handleAdd" type="primary" icon="plus">发布</a-button>
    </div>

    <!-- table区域-begin -->
    <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange">
      <span slot="sendUserName" slot-scope="text, record">
        {{ record.nameShow == 1 ? "【匿名用户】" : text }}
      </span>
      <span v-has="'mailboxList:edit'" slot="action" slot-scope="text, record">
        <a @click="handleDetail(record)">查看</a>
        <a-divider type="vertical" />
        <a @click="handleEdit(record)">评论</a>
      </span>

    </a-table>

    <CommunityModal ref="modalForm" :type="nameShowType" @ok="modalFormOk"></CommunityModal>
    <MailboxCommentModal ref="modalFormComment" @ok="modalFormOk"></MailboxCommentModal>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CommunityModal from './modules/CommunityModal.vue'
import MailboxCommentModal from './modules/MailboxCommentModal.vue'
import { getAction, httpAction } from '@/api/manage'

export default {
  name: 'CommunityList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CommunityModal,
    MailboxCommentModal
  },
  data () {
    return {
      description: '社区互动',
      // 表头 发布时间、发布主题和发布内容；主题和内容自定义
      // 查询：主题、时间
      columns: [
        {
          title: '发帖人',
          align:"center",
          dataIndex: 'sendUserName',
          scopedSlots: { customRender: 'sendUserName' }
        },
        {
          title: '主题',
          align: "center",
          dataIndex: 'title'
        },
        {
          title: '发贴时间',
          align: "center",
          dataIndex: 'createTime'
        },

        // {
        //   title: '发布内容',
        //   align: "center",
        //   dataIndex: 'content'
        // },

        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: "/message/psychologicalTalk/list",
        delete: "/pa/paSleepDiary/delete",
        deleteBatch: "/pa/paSleepDiary/deleteBatch",
        exportXlsUrl: "/pa/paSleepDiary/exportXls",
        importExcelUrl: "pa/paSleepDiary/importExcel",

      },
      dictOptions: {},
      superFieldList: [],
      nameShowType: '3'
    }
  },

  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    },
  },
  methods: {
    // handleDetail (record) {
    //   this.$refs.modalForm.show(record);
    // },
    loadData(arg) {
      if(!this.url.list){
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      //type 1信箱
      params.type = this.nameShowType
      if (params.createTime) {
        params.createTime = params.createTime.format("YYYY-MM-DD")+ " 00:00:00"
      }
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records||res.result;
          if(res.result.total)
          {
            this.ipagination.total = res.result.total;
          }else{
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    handleEdit (record) {
      this.$refs.modalFormComment.edit(record);
    },

    onDateChange (value, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>