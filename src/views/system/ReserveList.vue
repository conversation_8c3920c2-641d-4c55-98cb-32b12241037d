<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :lg="6" :sm="8">
            <a-form-item label="用户名">
              <a-input placeholder="请输入用户名" v-model="queryParam.patientName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8">
            <a-form-item label="预约时间">
              <j-date placeholder="请选择预约时间" date-format="YYYY-MM-DD" v-model="queryParam.visitDate" style="width: 100%"/>
              <!-- <a-date-picker v-model="queryParam.visitDate" format="YYYY-MM-DD" placeholder="请选择预约时间" /> -->
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- v-has="'sleepDiary:add'" -->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange"
              :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}">

        <span slot="action" slot-scope="text, record">
          <!-- <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical" /> -->
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'CollegeList',
  mixins: [JeecgListMixin, mixinDevice],
  data () {
    return {
      description: '预约数据',
      // 表头 用户名、性别、年龄、手机号、预约时间、预约原因
      // 查询：用户名、预约时间
      columns: [
        {
          title: '用户名',
          align: "center",
          dataIndex: 'patientName'
        },
        {
          title: '性别',
          align: "center",
          dataIndex: 'sex',
          customRender: (text) => {
            return text == 1 ? '男' : '女'
          }
        },
        {
          title: '年龄',
          align: "center",
          dataIndex: 'age'
        },
        {
          title: '手机号',
          align: "center",
          dataIndex: 'patientPhone'
        },
        {
          title: '预约日期',
          align: "center",
          dataIndex: 'visitDate'
        },
        {
          title: '预约时间',
          align: "center",
          dataIndex: 'visitTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/pa/paAppointment/list',
        delete: '/pa/paAppointment/delete',
        edit: '/pa/paAppointment/edit',
        deleteBatch: '/pa/paAppointment/deleteBatch',
        exportXlsUrl: '/pa/paAppointment/exportXls',
        importExcelUrl: 'pa/paAppointment/importExcel'
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>