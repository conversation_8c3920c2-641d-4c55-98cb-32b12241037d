<template>
  <j-modal
    :title="title"
    :width="800"
    :visible="visible"
    switchFullscreen
    @ok="submitForm"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-if="disableSubmit" label="发布时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <span>{{model.createTime}}</span>
        </a-form-item>

        <a-form-item label="发布主题" :labelCol="labelCol" :wrapperCol="wrapperCol" >
          <a-input v-decorator="['title', {}]" :disabled="disableSubmit" placeholder="请输入发布主题" />
        </a-form-item>

        <a-form-item label="发布内容" :labelCol="labelCol" :wrapperCol="wrapperCol" >
          <a-textarea v-decorator="['content', {}]" :disabled="disableSubmit" :rows="4" placeholder="请输入发布内容" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="是否匿名">
          <a-switch v-decorator="['nameShow', { valuePropName: 'checked', initialValue: true }]" :disabled="disableSubmit" />
        </a-form-item>
        <a-form-item v-if="disableSubmit" label="评论" :labelCol="labelCol" :wrapperCol="wrapperCol" >
          <p v-for="item in recordList" :key="item.id">
            {{ item.replayUserName }}：{{ item.content }}
          </p>
        </a-form-item>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
  import { getAction, httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  export default {
    name: "MailboxModal",
    props: {
      type: {
        type: String,
        default: '1',
      }
    },
    data () {
      return {
        title:'',
        visible: false,
        confirmLoading: false,
        disableSubmit: false,
        recordList: [],
        model: {},

        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        form:this.$form.createForm(this),
        paht: this.$route.path,
        url: {
          add: '/message/psychologicalTalk/add',
          edit: '/message/psychologicalTalk/edit',
          queryById: '/message/psychologicalTalk/queryById',
          listByPid: '/message/psychologicalTalk/listByPid'
        }
      }
    },
    created () {
      console.log("created");
    },

    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.model = Object.assign({}, record)
        console.log(this.model)
        if (record.id) { 
          this.getList()
        }
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'title', 'content'))
          this.form.setFieldsValue({
              nameShow: !!this.model.nameShow,
            })
        })
      },
      getList() {
        getAction(this.url.listByPid, { pageNo: 1, pageSize: 10000,pid: this.model.id }).then((res) => { 
          this.recordList = res.result.records
        })
      },
      handleCancel () {
        this.$emit('close');
        this.visible = false;
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            formData.nameShow = formData.nameShow ? 1 : 0
            formData.type = this.type
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.handleCancel()
            })


          }
        })
      }
    }
  }
</script>