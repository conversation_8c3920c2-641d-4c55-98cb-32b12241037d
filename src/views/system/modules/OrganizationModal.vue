<template>
  <j-modal
    :title='title'
    :width='width'
    :visible='visible'
    switchFullscreen
    @ok='submitForm'
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='formDisabled'>
        <a-form-model ref='form' :model='model' :rules='validatorRules' slot='detail'>
          <!-- 机构名称、简介 -->
          <a-form-model-item label='学校名称' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='orgId'>
            <!--            <j-select-depart v-model="model.orgId" @back="backDepartInfo" :backDepart="true" :></j-select-depart>-->
            <j-dict-select-tag v-model='model.orgId' placeholder='请选择学校名称'
                               :dictCode='calcDictCode' />
          </a-form-model-item>
          <a-form-model-item label="学校图片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="titleImg">
            <j-image-upload v-model="model.titleImg"></j-image-upload>
          </a-form-model-item>
          <a-form-model-item label='简介' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='description'>
            <a-textarea row='10' v-model='model.description' placeholder='请输入简介'></a-textarea>
          </a-form-model-item>
          <a-form-model-item label='内容' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='content'>
            <j-editor :j-height='600' placeholder='请输入简介' v-model='model.content' :disabled='disableSubmit' />
          </a-form-model-item>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction } from '@api/manage'
import moment, { Moment } from 'moment'

export default {
  name: 'MailboxCommentModal',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        orgId: [
          { required: true, message: '请选择学校名称', trigger: 'blur' }
        ],
        description: [
          { min: 0, max: 200, message: '长度在 0 到 200 个字符', trigger: 'blur' }
        ]
      },
      url: {
        add: '/system/sysOrg/add',
        edit: '/system/sysOrg/edit',
        queryById: '/system/sysOrg/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    },
    calcDictCode() {
      const userInfo = this.$store.getters.userInfo
      if (!!userInfo.username && userInfo.username === 'admin') {
        return `sys_depart,depart_name,id, org_category='1' AND del_flag='0' `
      } else {
        return 'sys_depart,depart_name,id, org_code = \'' + userInfo.orgCode + '\''
      }
    }
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    disabledDate() {
      return new Date() && new Date() < moment().endOf('day')
    },
    moment,
    add() {
      this.edit(this.modelDefault)
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
    },
    handleCancel() {
      this.$emit('close')
      this.visible = false
    },
    backDepartInfo(info) {
      this.model.orgName = info[0].text
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.handleCancel()
          })
        }

      })
    }
  }
}
</script>