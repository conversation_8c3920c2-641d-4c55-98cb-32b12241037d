<template>
  <a-modal :title="title" :width="800" :ok=false :visible="visible" :confirmLoading="confirmLoading" :okButtonProps="{ props: {disabled: disableSubmit} }" @ok="handleOk" @cancel="handleCancel" cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="机构名称" prop="departName">
          <a-input placeholder="请输入机构名称" v-model="model.departName" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" :hidden="seen" label="上级机构">
          <a-tree-select style="width:100%" :dropdownStyle="{maxHeight:'200px',overflow:'auto'}" :treeData="departTree" v-model="model.parentId" placeholder="请选择上级组织机构" :disabled="condition">
          </a-tree-select>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="机构类型">
          <a-radio-group v-model="model.orgCategory" placeholder="请选择机构类型" :disabled="dictDisabled">
            <a-radio value="1" disabled>
              学校
            </a-radio>
            <a-radio value="2" :disabled="disabled2">
              学院
            </a-radio>
            <a-radio value="3" :disabled="disabled3">
              系/部
            </a-radio>
            <a-radio value="4" :disabled="disabled4">
              年级
            </a-radio>
            <a-radio value="5">
              班级
            </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="contactPerson" label="联系人">
          <a-input placeholder="请输入联系人" v-model="model.contactPerson" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="mobile" label="电话">
          <a-input placeholder="请输入电话" v-model="model.mobile" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="省市区" v-if="shouldShowArea">
          <j-area-linkage v-model="model.areaCode" @change="handleAreaChange" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address" label="地址">
          <a-input placeholder="请输入地址" v-model="model.address" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="departOrder" label="排序">
          <a-input-number v-model="model.departOrder" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="memo" label="备注">
          <a-textarea placeholder="请输入备注" v-model="model.memo" />
        </a-form-model-item>

      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import { queryIdTree } from '@/api/api'
import pick from 'lodash.pick'
import ATextarea from 'ant-design-vue/es/input/TextArea'
import JAreaLinkage from '@comp/jeecg/JAreaLinkage'
export default {
  name: "SysDepartModal",
  components: { ATextarea, JAreaLinkage },
  data () {
    return {
      departTree: [],
      orgTypeData: [],
      phoneWarning: '',
      title: "操作",
      seen: false,
      visible: false,
      condition: true,
      disableSubmit: false,
      disabled2: false,
      disabled3: false,
      disabled4: false,
      shouldShowArea: false,
      parentDepart: {},
      model: {
        departOrder: 0,
        orgCategory: '1'
      },
      menuhidden: false,
      menuusing: true,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      validatorRules: {
        departName: [{ required: true, message: '请输入机构/组织机构名称!' }],
        orgCode: [{ required: true, message: '请输入机构编码!' }],
        mobile: [{ validator: this.validateMobile }],
        orgCategory: [{ required: true, message: '请输入机构类型!' }]
      },
      url: {
        add: "/sys/sysDepart/add",
      },
      dictDisabled: true,
    }
  },
  created () {
  },
  methods: {
    loadTreeData () {
      var that = this;
      queryIdTree().then((res) => {
        if (res.success) {
          that.departTree = [];
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i];
            that.departTree.push(temp);
          }
        }

      })
    },
    add (depart) {
      if (depart) {
        this.seen = false;
        this.dictDisabled = false;
      } else {
        this.seen = true;
        this.dictDisabled = true;
      }
      this.edit(depart);
    },
    edit (record) {
      this.visible = true;
      this.loadTreeData();
      this.model.parentId = record != null ? record.toString() : null;
      if (this.seen) {
        this.model.orgCategory = '1';
        this.shouldShowArea = true
      } else {
        console.log(this.parentDepart)
        this.shouldShowArea = false
        if (this.parentDepart.orgCategory == '1') {
          this.model.orgCategory = '2';
        } else if (this.parentDepart.orgCategory == '2') {
          this.disabled2 = true
          this.model.orgCategory = '3';
        } else if (this.parentDepart.orgCategory == '3') {
          this.disabled2 = true
          this.disabled3 = true
          this.model.orgCategory = '4';
        } else if (this.parentDepart.orgCategory == '4') {
          this.disabled2 = true
          this.disabled3 = true
          this.disabled4 = true
          this.model.orgCategory = '5';
        }
      }
    },
    close () {
      this.$emit('close');
      this.disableSubmit = false;
      this.visible = false;
      this.disabled2 = false
      this.disabled3 = false
      this.disabled4 = false
      this.shouldShowArea = false;
      this.model.orgCategory = '1';
      this.$refs.form.resetFields();
    },
    handleOk () {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true;
          httpAction(this.url.add, this.model, "post").then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.loadTreeData();
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          })

        } else {
          return false;
        }
      })
    },
    handleCancel () {
      this.close()
    },
    validateMobile (rule, value, callback) {
      if (!value || new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/).test(value)) {
        callback();
      } else {
        callback("您的手机号码格式不正确!");
      }
    },
    handleAreaChange(areaCode, areas) {
      this.model.province = areas.province.text
      this.model.city = areas.city.text
      this.model.district = areas.district.text
    }
  }
}
</script>

<style scoped>
</style>