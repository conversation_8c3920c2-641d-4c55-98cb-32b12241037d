<template>
  <a-card :bordered="false">
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-upload style="margin-left: 8px" name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" :data="uploadParams"
                @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
          selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        filterMultiple="filterMultiple"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange,type:type}"
        @change="handleTableChange"
        :customRow="clickThenCheck"
      >

        <template slot="avatarslot" slot-scope="text, record, index">
          <div class="anty-img-wrap">
            <a-avatar v-if="record.titleType==2" shape="square" :src="getAvatarView(record.title)" icon="user" />
            <span v-if="record.titleType!=2">{{ record.title }}</span>
          </div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <a-tabs defaultActiveKey="1">
      <a-tab-pane tab="选项信息" key="1">
        <PsOptionList ref="PsOptionList"></PsOptionList>
      </a-tab-pane>
    </a-tabs>

    <!-- 表单区域 -->
    <PsQuesionModal ref="modalForm" @ok="modalFormOk" />

  </a-card>
</template>

<script>
import PsOptionList from './PsOptionList'
import PsOptionModal from './modules/PsOptionModal'
import PsQuesionModal from './modules/PsQuesionModal'
import { deleteAction, getAction, getFileAccessHttpUrl } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'JeecgOrderDMainList',
  mixins: [JeecgListMixin],
  components: {
    PsQuesionModal,
    PsOptionModal,
    PsOptionList
  },
  data() {
    return {
      description: '问题管理页面',
      /* 数据源 */
      dataSource: [],
      uploadParams: {},
      measureId:'',
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '20'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      // 表头
      columns: [
        {
          title: '排序',
          align: 'center',
          dataIndex: 'sort'
        },
        {
          title: '标题类型',
          align: 'center',
          dataIndex: 'titleType_dictText'
        },
        {
          title: '标题内容',
          align: 'center',
          dataIndex: 'title',
          scopedSlots: { customRender: 'avatarslot' }
        },
        /*          {
                    title: '分数',
                    align: 'center',
                    dataIndex: 'score'
                  },
                  {
                    title: '选项',
                    align: 'center',
                    dataIndex: 'answer'
                  },*/
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }],
      // 分页参数
      type: 'radio',
      url: {
        list: '/psychology/psQuesion/list',
        delete: '/psychology/psQuesion/delete',
        deleteBatch: '/psychology/psQuesion/deleteBatch',
        imgerver: window._CONFIG['domianURL'],
        importExcelUrl: 'psychology/psQuesion/importExcel',
      }
    }
  },
  mounted() {
    this.loadData()
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    getAvatarView: function(avatar) {
      return getFileAccessHttpUrl(avatar, this.url.imgerver, 'http')
    },
    loadData(arg) {
      var measureId = this.$route.params.id
      this.uploadParams = {
        measureId: measureId
      }
      this.measureId = measureId;
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams()//查询条件
      getAction(`${this.url.list}?measureId=${measureId}`, params, { measureId: measureId }).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records
          this.loading = false
          this.ipagination.total = res.result.total
        } else {
          this.dataSource = null
        }
      })
    },
    clickThenCheck(record) {
      return {
        on: {
          click: () => {
            this.onSelectChange(record.id.split(','), [record])
          }
        }
      }
    },
    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
      this.$refs.PsOptionList.getOrderMain(this.selectedRowKeys[0])
    },
    onClearSelected() {
      this.selectedRowKeys = []
      this.selectionRows = []
      this.$refs.PsOptionList.queryParam.mainId = null
      this.$refs.PsOptionList.loadData()
      this.$refs.PsOptionList.selectedRowKeys = []
      this.$refs.PsOptionList.selectionRows = []
    },
    handleDelete: function(id) {
      var that = this
      deleteAction(that.url.delete, { id: id }).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
          this.$refs.PsOptionList.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    searchQuery: function() {
      this.selectedRowKeys = []
      this.selectionRows = []
      this.$refs.PsOptionList.queryParam.mainId = null
      this.$refs.PsOptionList.loadData()
      this.$refs.PsOptionList.selectedRowKeys = []
      this.$refs.PsOptionList.selectionRows = []
      this.loadData()
    },
    handleAdd: function() {
      this.$refs.modalForm.add(this.$route.params.id)
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
  }
}
</script>
<style scoped>
.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}
</style>