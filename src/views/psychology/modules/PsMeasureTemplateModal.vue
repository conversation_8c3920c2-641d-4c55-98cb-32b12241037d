<template>
  <a-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">
    <!-- 主表单区域 -->
    <a-form :form="form">
      <a-row>
        <a-col :span="12" :gutter="8">
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="模板名称">
            <a-input placeholder="请输入模板名称" v-decorator="['name', {}]"/>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 子表单区域 -->
      <a-tabs>
        <a-tab-pane tab="量表单价设置">
          <div>
            <a-row type="flex" style="margin-bottom:10px" :gutter="16">
              <a-col :span="7">量表</a-col>
              <a-col :span="7">量表单价</a-col>
              <a-col :span="7">服务费抽成比例(万分之)</a-col>
              <a-col :span="3">操作</a-col>
            </a-row>
            <a-row type="flex" style="margin-bottom:10px" :gutter="16"
                   v-for="(item, index) in model.psMeasureUnitPriceTemplateTable" :key="index">
              <a-col :span="7">
                <a-form-item>
                  <a-tree-select
                    showSearch
                    style="width:100%"
                    :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
                    :treeData="measureTree"
                    v-decorator="['psMeasureUnitPriceTemplateTable['+index+'].measureId', {'initialValue':item.measureId,rules: [{ required: true, message: '请输入用户名!' }]}]"
                    treeNodeFilterProp="title"
                    placeholder="请选择量表">
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item>
                  <a-input placeholder="量表单价"
                           v-decorator="['psMeasureUnitPriceTemplateTable['+index+'].price', {'initialValue':item.price,rules: [{required: true, message: '请输入量表单价!' }]}]"/>
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item>
                  <a-input placeholder="服务费抽成比例(万分之)"
                           v-decorator="['psMeasureUnitPriceTemplateTable['+index+'].serviceChargeRatio', {'initialValue':item.serviceChargeRatio,rules: [{ required: true, message: '请输入服务费抽成比例!' }]}]"/>
                </a-form-item>
              </a-col>
              <a-col :span="3">
                <a-form-item>
                  <a-button @click="delRowCustom(index)" icon="minus"></a-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-button style="width: 98%; margin-bottom: 8px" type="dashed" icon="plus" @click="addRowCustom">新增
            </a-button>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>
</template>
<script>

  import pick from 'lodash.pick'
  import { getAction, httpAction } from '@/api/manage'

  export default {
    name: 'PsMeasureTemplateModal',
    data() {
      return {
        title: '量表单价设置',
        visible: false,
        validatorRules: {},
        confirmLoading: false,
        measureTree: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        model: {
          psMeasureUnitPriceTemplateTable: [{}]
        },
        form: this.$form.createForm(this),
        url: {
          add: '/psychology/psMeasureTemplate/add',
          edit: '/psychology/psMeasureTemplate/edit',
          treeList: '/psychology/psCategory/queryTreeSelectList',
          psMeasureUnitPriceTemplate: {
            list: '/psychology/psMeasureTemplate/queryPsMeasureUnitPriceTemplateByMainId'
          }
        }
      }
    },
    created() {
      this.loadTreeData()
    },
    methods: {
      add() {
        this.edit({})
      },
      loadTreeData() {
        getAction(this.url.treeList, null).then((res) => {
          if (res.success) {
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              this.measureTree.push(temp)
            }
            this.loading = false
          }
        })
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.model.psMeasureUnitPriceTemplateTable = [{}]
        if (this.model.id) {
          let params = { id: this.model.id }
          getAction(this.url.psMeasureUnitPriceTemplate.list, params).then((res) => {
            if (res.success) {
              res.result.forEach((item, index, arr) => {
                if (arr[index].categoryId) {
                  arr[index].measureId = arr[index].measureId + ',' + arr[index].categoryId
                }
              })
              this.model.psMeasureUnitPriceTemplateTable = res.result
              this.$forceUpdate()
            }
          })
        }
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'name', 'price', 'serviceChargeRatio'))
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let mainData = Object.assign(this.model, values)
            let formData = {
              ...mainData,
              psMeasureUnitPriceTemplateList: mainData.psMeasureUnitPriceTemplateTable
            }
            formData.psMeasureUnitPriceTemplateList.forEach((item, index, arr) => {
              if (arr[index].measureId) {
                arr[index].measureId = arr[index].measureId.split(',')[0]
              }
            })
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      addRowCustom() {
        this.model.psMeasureUnitPriceTemplateTable.push({})
        this.$forceUpdate()
      },
      delRowCustom(index) {
        this.model.psMeasureUnitPriceTemplateTable.splice(index, 1)
        this.$forceUpdate()
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>