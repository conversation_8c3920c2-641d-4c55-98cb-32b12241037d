<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="量表名称">
          <a-input placeholder="请输入量表名称" v-decorator="['name', {rules: [{ required: true, message: '请输入量表名称!' }]}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="副标题">
          <a-input placeholder="请输入副标题" v-decorator="['subhead', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="量表简介">
          <a-textarea placeholder="请输入量表简介" v-decorator="['description', {}]" auto-size/>
        </a-form-item>
        <a-form-item label="标题图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="picture">
          <j-image-upload isMultiple v-model="model.picture"></j-image-upload>
        </a-form-item>
        <a-form-item label="量表类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select
            mode="multiple"
            style="width: 100%"
            placeholder="请选择量表类别"
            v-model="selectedCategory">
            <a-select-option v-for="category in psCategorys" :key="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="年龄范围">
          <a-input-number v-decorator="[ 'ageRangeBefor', {}]"/>
          -
          <a-input-number v-decorator="[ 'ageRangeAfter', {}]"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { findPsCategoryAll } from '@/api/api'

  export default {
    name: 'PsMeasureModal',
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        psCategorys: [],
        selectedCategory: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: '/psychology/psMeasure/add',
          edit: '/psychology/psMeasure/edit'
        }
      }
    },
    created() {
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.loadPsTemplate()
        this.visible = true
        this.selectedCategory = this.model.categoryId.split(",")
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'name', 'subhead', 'description', 'categoryId', 'ageRangeBefor', 'ageRangeAfter'))
          //时间格式化
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            formData.categoryId = this.selectedCategory.length > 0 ? this.selectedCategory.join(',') : ''
            //时间格式化
            formData.updateTime = null;
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      /**
       * 初始化量表类别下拉选
       */
      loadPsTemplate() {
        findPsCategoryAll({}).then((res) => {
          if (res.success) {
            this.psCategorys = res.result
          } else {
            // console.log(res.message)
          }
        })
      },
      handleCancel() {
        this.close()
      }


    }
  }
</script>

<style scoped>

</style>