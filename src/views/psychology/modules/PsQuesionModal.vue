<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">
    <!-- 主表单区域 -->
    <a-form :form="form">
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="标题类型">
        <j-dict-select-tag @change="handleChangeTitleType"
                           v-decorator="['titleType', { rules: [{ required: true, message: '请选择标题类型!' }] }]"
                           :triggerChange="true" placeholder="请选择标题类型" dictCode="quesion_type"/>
      </a-form-item>
      <a-form-item
        v-if="quesionMainModel.titleType != 2"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="标题内容">
        <a-textarea :min="0" :max="100" rows="4" :precision="0" placeholder="请输入问题的标题"
                 v-decorator="['title', { rules: [{ required: true, message: '请输入问题的标题!' }] } ]"/>
      </a-form-item>
      <a-form-item
        v-if="quesionMainModel.titleType == 2"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="标题内容">
        <j-image-upload class="avatar-uploader" text="上传" v-model="fileList"></j-image-upload>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="排序">
        <a-input placeholder="请输入排序" v-decorator="['sort', {rules: [{ required: true, message: '请输入排序!' }]}]"/>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="题目解释">
        <a-textarea :min="0" :max="100" rows="4" :precision="0" placeholder="请输入题目解释"
                 v-decorator="['titleExplain']"/>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>

  import Vue from 'vue'
  import pick from 'lodash.pick'
  import { httpAction } from '@/api/manage'
  import { ajaxGetDictItems } from '@/api/api'
  import { ACCESS_TOKEN } from '@/store/mutation-types'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import JImageUpload from '@/components/jeecg/JImageUpload.vue'

  export default {
    name: 'PsQuesionModal',
    components: {
      JDictSelectTag,
      JImageUpload
    },
    data() {
      return {
        title: '操作',
        visible: false,
        confirmLoading: false,
        dictCode: 'quesion_type',
        dictOptions: [],
        form: this.$form.createForm(this),
        fileList: [],
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        previewVisible: false,
        quesionMainModel: {},
        previewImage: '',
        url: {
          imgerver: window._CONFIG['domianURL'],
          fileUpload: window._CONFIG['domianURL'] + '/sys/common/upload',
          add: '/psychology/psQuesion/add',
          edit: '/psychology/psQuesion/edit',
          psQuesionOption: {
            list: '/psychology/psQuesion/queryPsQuesionOptionByMainId'
          }
        }
      }
    },
    created() {
      const token = Vue.ls.get(ACCESS_TOKEN)
      this.headers = { 'X-Access-Token': token }
    },
    computed: {
      uploadAction: function() {
        return this.url.fileUpload
      }
    },
    methods: {
      add(measureId) {
        this.quesionMainModel.measureId = measureId
        this.edit({ measureId: measureId })
      },
      handleCancel() {
        this.close()
      },
      edit(record) {
        this.form.resetFields()
        this.visible = true
        this.quesionMainModel = Object.assign({}, record)
        if (!this.quesionMainModel.id) {
          this.quesionMainModel.titleType = '1'
        }
        this.$forceUpdate()
        this.initDictData()
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.quesionMainModel, 'titleType', 'title', 'sort', 'titleExplain'))
        })
        setTimeout(() => {
          this.fileList = record.title
        }, 5)
      },
      initDictData() {
        //根据字典Code, 初始化字典数组
        ajaxGetDictItems(this.dictCode, null).then((res) => {
          if (res.success) {
            this.dictOptions = res.result
            this.$forceUpdate()
          }
        })
      },
      // 确定
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.quesionMainModel.id) {
              httpurl += that.url.add
              method = 'post'
            } else {
              httpurl += that.url.edit
              method = 'put'
            }
            let formData = Object.assign(that.quesionMainModel, values)
            if (this.quesionMainModel.titleType == 2) {
              formData.title = that.fileList
            }
            formData.type = '0'
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleChangeTitleType(e) {
        this.quesionMainModel.titleType = e
      }
    }
  }
</script>

<style scoped>
  /** 主表单行间距 */
  .ant-form {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content {
    margin-bottom: 0px;
  }
</style>