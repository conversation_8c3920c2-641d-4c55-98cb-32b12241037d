<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="类别">
              <a-select
                style="width: 100%"
                placeholder="请选择量表类别"
                v-model="queryParam.categoryId">
                <a-select-option v-for="category in psCategorys" :key="category.id">
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="量表名称">
              <a-input placeholder="请输入量表名称" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :md="6" :sm="8">
              <a-form-item label="移动端状态">
                <j-dict-select-tag  v-model="queryParam.isMobile" placeholder="请输入移动端状态" dictCode="yn"/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange">


        <span slot="status" slot-scope="text">
          <a-badge :status="text | statusTypeFilter" :text="text | statusFilter" />
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <psMeasure-modal ref="modalForm" @ok="modalFormOk"></psMeasure-modal>
  </a-card>
</template>

<script>
  import PsMeasureModal from './modules/PsMeasureModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { findPsCategoryAll } from '@/api/api'

  const statusMap = {
    0: {
      status: 'success',
      text: '正常'
    },
    1: {
      status: 'error',
      text: '维护中'
    }
  }

  export default {
    name: 'PsMeasureList',
    mixins: [JeecgListMixin],
    components: {
      PsMeasureModal
    },
    data() {
      return {
        description: '量表评估',
        psCategorys: [],
        // 表头
        columns: [
          {
            title: '量表名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '量表类别',
            align: 'center',
            dataIndex: 'categoryName'
          },
          {
            title: '年龄范围',
            align: 'center',
            dataIndex: 'ageRangeBefor',
            customRender: (value, row) => {
              if (row.ageRangeBefor == null || row.ageRangeAfter == null) {
                return '无'
              } else {
                const ageRange = row.ageRangeBefor + '-' + row.ageRangeAfter
                return ageRange
              }
            }
          },
          {
            title: '状态',
            align: 'center',
            dataIndex: 'status',
            scopedSlots: { customRender: 'status' }
          },
          {
            title: '移动端状态',
            align: 'center',
            dataIndex: 'isMobile_dictText',
          }
        ],
        url: {
          list: '/psychology/psMeasure/list',
          delete: '/psychology/psMeasure/delete',
          deleteBatch: '/psychology/psMeasure/deleteBatch',
          exportXlsUrl: 'psychology/psMeasure/exportXls',
          importExcelUrl: 'psychology/psMeasure/importExcel'
        }
      }
    },
    filters: {
      statusFilter (type) {
        return statusMap[type].text
      },
      statusTypeFilter (type) {
        return statusMap[type].status
      }
    },
    created() {
      this.loadPsCategory()
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      /**
       * 初始化量表类别下拉选
       */
      loadPsCategory() {
        findPsCategoryAll({}).then((res) => {
          if (res.success) {
            this.psCategorys = res.result
          } else {
            // console.log(res.message)
          }
        })
      },
      goQuesionList(id) {
        this.$router.push({ path: '/psychology/PsQuesionList/' + id })
      }
    }
  }
</script>
<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }
</style>