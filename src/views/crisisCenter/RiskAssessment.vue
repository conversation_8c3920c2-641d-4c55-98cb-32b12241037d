<template>
  <div>
    <a-card :bordered="false">
      <!-- 查询区域 用户名、预警来源、预警程度 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col :span="6">
              <a-form-item label="用户名">
                <a-input placeholder="请输入用户名" v-model="queryParam.userName"></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="预警来源">
                <a-tree-select
                  showSearch
                  style="width:100%"
                  :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
                  :treeData="measureTree"
                  v-model="queryParam.measureId"
                  treeNodeFilterProp="title"
                  placeholder="请选择预警来源">
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="8">
              <a-form-item label="预警程度">
                <j-dict-select-tag v-model="queryParam.degree" placeholder="请选择预警程度" dictCode="result_degree"/>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="table-operator">
        <a-button @click="handleAdd" type="primary" icon="plus">新增风险危机评估</a-button>
      </div>

      <!-- table区域-begin -->
      <div>
        <a-table
          ref="table"
          size="middle"
          bordered
          rowKey="id"
          :scroll="{ x: 1300 }"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange">

          <span slot="degree" slot-scope="text">
            <a-badge :color="text | statusTypeFilter" :text="text | statusFilter"/>
          </span>

          <span slot="action" slot-scope="text, record" v-if="record.status == 2">
            <a @click="handleView(record)">查看报告</a>
            <a-divider type="vertical"/>
            <a @click="handleExportDoc(record)">下载报告</a>
            <a-divider type="vertical"/>
            <a @click="handleClick(record)">专家鉴别</a>
          </span>
        </a-table>
      </div>
      <!-- table区域-end -->
      <!-- 表单区域 -->
      <RiskAssessmentModal ref="modalForm" :measureTree="measureTree" @ok="modalFormOk"/>
    </a-card>
    <!-- 打印表单页 -->
    <report-common-form ref="ReportForm"></report-common-form>
  </div>
</template>
<script>
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import RiskAssessmentModal from './modules/RiskAssessmentModal'
  import { getAction, postAction } from '@/api/manage'
  import ReportCommonForm from '@/components/report/ReportCommonForm'

  const degreeMap = {
    0: {
      color: '#D9D9D9',
      text: '未答完'
    },
    1: {
      color: 'green',
      text: '正常'
    },
    2: {
      color: 'yellow',
      text: '轻度'
    },
    3: {
      color: 'red',
      text: '中度'
    },
    4: {
      color: 'purple',
      text: '重度'
    },
    5: {
      color: '#D9D9D9',
      text: '未知'
    }
  }

  export default {
    name: 'RiskAssessment',
    mixins: [JeecgListMixin],
    props: {
      isOnly: {
        type: Boolean,
        default: false
      },

      isShow: {
        type: Boolean,
        default: true
      }
    },
    components: {
      RiskAssessmentModal,
      ReportCommonForm
    },
    data() {
      return {
        superFieldList:[],
        description: '风险评估',
        measureTree: [],
        // 表头 用户名、预警来源（展示量表名称）、性别、年龄、学校、班级、年级、预警程度、预警时间
        columns: [
          {
            title: '用户名',
            align: 'center',
            dataIndex: 'userName',
            width: 120,
          },
          {
            title: '预警来源',
            align: 'center',
            dataIndex: 'measureName',
          },
          {
            title: '性别',
            align: 'center',
            dataIndex: 'sex_dictText',
            width: 100
          },
          {
            title: '年龄',
            align: 'center',
            dataIndex: 'age',
            width: 100
          },
          {
            title: '学校',
            align: 'center',
            dataIndex: 'schoolName',
            width: 120
          },
          {
            title: '班级',
            align: 'center',
            dataIndex: 'className',
            width: 120
          },
          {
            title: '年级',
            align: 'center',
            dataIndex: 'gradeName',
            width: 120
          },
          {
            title: '预警程度',
            align: 'center',
            dataIndex: 'degree',
            scopedSlots: { customRender: 'degree' },
            width: 120
          },
          {
            title: '预警时间',
            align: 'center',
            dataIndex: 'waringTime',
            width: 180
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' },
            width: 220,
          }
        ],
        // 请求参数
        url: {
          list: '/crisis/assessment/list',
          treeList: '/psychology/psCategory/queryTreeSelectList',
          exportDocUrl: '/diagnosis/dxResult/exportDoc',
          update: '/crisis/assessment/update'
        }
      }
    },
    filters: {
      statusFilter(type) {
        return degreeMap[type].text
      },
      statusTypeFilter(type) {
        return degreeMap[type].color
      }
    },
    created() {
      this.loadTreeData()
      // this.loadData()
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!')
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams()//查询条件
        if (params.measureId) {
          params.measureId = params.measureId.split(',')[0]
        }
        params.source = 'risk'
        this.loading = true
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records
            this.ipagination.total = res.result.total
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false
        })
      },
      loadTreeData() {
        getAction(this.url.treeList, null).then((res) => {
          if (res.success) {
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              this.measureTree.push(temp)
            }
            this.loading = false
          }
        })
      },
      initDictConfig() {
      },
      handleAdd: function() {
        this.$refs.modalForm.add(this.$route.params.id)
        this.$refs.modalForm.title = '新增'
      },
      handleExportDoc(record) {
        if (record.status != '2') {
          this.$message.error('未答完题目不能查看报告！')
          return
        }
        let url = `${window._CONFIG['domianURL']}/${this.url.exportDocUrl}?ids=` + record.id
        window.location.href = url
      },
      handleView(record){
        this.$refs.ReportForm.edit(record)
        this.$refs.ReportForm.title = '内容预览'
      },
      handleClick(record) { // 专家鉴别
        postAction(this.url.update, {id: record.id, source: 'expertReview'}).then((res) => {
          if (res.success) {
            this.$router.push({ path: '/crisisCenter/ExpertAssessment' })
          }
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }
</style>