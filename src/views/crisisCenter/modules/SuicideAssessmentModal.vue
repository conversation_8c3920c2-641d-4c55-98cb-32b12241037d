<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <!-- 主表单区域 -->
      <a-form :form="form">
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="组织类型">
              <a-radio-group v-decorator="['type', {initialValue: '1'}]">
                <a-radio value="1">机构</a-radio>
                <a-radio value="2">个人</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="form.getFieldValue('type')!='2'">
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="组织架构">
              <j-select-depart v-decorator="['departIds', {}]" :multi="true" :backDepart="true"></j-select-depart>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-else>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="测试对象">
              <j-select-patient v-decorator="['patientName']" @back="backPatientInfo" :backInfo="true"></j-select-patient>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="选择量表">
              <a-select mode="multiple" placeholder="请选择量表" style="width: 100%" v-decorator="['measureIds', {}]">
                <a-select-option v-for="measure in measures" :value="measure.measureId" :key="measure.measureId">
                  {{ measure.measureName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import Vue from 'vue'
  import { USER_ROLE } from '@/store/mutation-types'
  import pick from 'lodash.pick'
  import moment from 'moment'
  import { getAction, httpAction } from '@/api/manage'

  export default {
    name: 'RiskAssessmentModal',

    props: {
      measureTree: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        title: '操作',
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        disableSubmit: false,
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        resultMainModel: { source: 'suicide' },
        url: {
          add: '/crisis/assessment/add',
          edit: '/diagnosis/dxResult/edit',
          listAll: '/crisis/assessment/listMeasure',
          dxResultOption: {
            list: '/diagnosis/dxResult/queryDxResultOptionByMainId'
          }
        },
        measures: []
      }
    },
    methods: {
      moment,
      add() {
        this.edit({})
      },
      handleCancel() {
        this.close()
      },
      getList() {
        getAction(this.url.listAll, { pageNo: 1, pageSize: 10000, source: 'suicide' }).then((res) => { 
          this.measures = res.result.records
        })
      },
      backPatientInfo(info) {
        this.form.setFieldsValue({
          patientName: info.map(item => item.text).join(","),
        })
        this.resultMainModel.userIds = info.map(item => item.value)
      },
      edit(record) {
        this.getList()
        this.form.resetFields()
        this.resultMainModel = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.resultMainModel, 'departIds', 'smeasureIds'))
          //时间格式化
          this.form.setFieldsValue({
            type: !this.resultMainModel.departIds || this.resultMainModel.departIds.length ? '1' : '2',
          })
        })
      },
      // 确定
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.resultMainModel.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let quesionData = Object.assign(this.resultMainModel, values)
            let formData = {
              ...quesionData,
              source: 'suicide'
            }
            //时间格式化
            if (formData.departIds) formData.departIds = formData.departIds.split(',');
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      close() {
        this.$emit('close')
        this.disableSubmit = false
        this.visible = false
      }
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>