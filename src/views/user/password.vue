<template>
  <div class="bg">
    <div class="back" @click="back">
      < 返回登录页 </div>
        <div class="info">
          <div class="leftInfo">
            <div class="title">
              修改密码
            </div>
            <a-form ref="form" :model="model" layout="horizontal" :rules="validatorRules" labelAlign="left"
              :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-form-item prop="username" required label="账号">
                <a-input v-model="model.username" placeholder="请输入账号">
                </a-input>
              </a-form-item>
              <a-form-item prop="username" required label="新密码">
                <a-input v-model="model.username" placeholder="请输入新密码">
                </a-input>
              </a-form-item>
              <a-form-item prop="username" required label="再次输入密码">
                <a-input v-model="model.username" placeholder="请输入新密码">
                </a-input>
              </a-form-item>
            </a-form>
          </div>
        </div>
        <div class="next">
          提交
        </div>
    </div>
</template>

<script>
export default {
  components: {
  },
  data() {
    return {
      type: 'parent',
      num: 'last',
      model: {
        username: '',
        password: '',
        inputCode: ''
      },
      validatorRules: {
        username: [
          { required: true, message: '请输入用户名!' },
          { validator: this.handleUsernameOrEmail }
        ],
        password: [{
          required: true, message: '请输入密码!', validator: 'click'
        }],

      },
    }
  },
  created() {

  },
  methods: {
    getChoose(e) {
      this.type = e.target.value
    },
    getNext() {
      this.num = 'last'
    },
    back() {
      this.$router.go(-1);
    }
  }

}
</script>
<style lang="less">
.bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFFFFF 0%, #ECF7FF 100%);
}

.choose {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 380px;
  left: 0;
  right: 0;

  .student {
    width: 316px;
    height: 130px;
    background: url(../../assets/image/choose.png) no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: left;
  }

  .parent {
    width: 316px;
    height: 130px;
    background: url(../../assets/image/choose1.png) no-repeat;
    background-size: 100% 100%;
    margin-left: 46px;
    display: flex;
    align-items: center;
    justify-content: left;
  }
}

.back {
  position: fixed;
  left: 60px;
  top: 58px;
  cursor: pointer;
}

.next {
  width: 345px;
  height: 38px;
  background: #009AF5;
  border-radius: 19px;
  text-align: center;
  line-height: 38px;
  font-size: 14px;
  color: #FFFFFF;
  position: fixed;
  bottom: 127px;
  left: 0;
  right: 0;
  cursor: pointer;
  margin: 0 auto
}

.info {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: fixed;
  bottom: 312px;
  left: 0;
  right: 0;

  .title {
    font-size: 16px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 36px;
  }

  .leftInfo,
  .rightInfo {
    width: 347px;
  }

  .line {
    width: 1px;
    height: 337px;
    border: 1px solid rgba(177, 177, 177, 0.28);
    margin: 0 112px;
    margin-top: 20px;
  }
}
</style>