<template>
  <div class="bg">
    <div class="back" @click="back">
      < 返回登录页 </div>
        <div v-if="num == 'first'" class="choose">
          <div class="student">
            <a-radio :checked="type == 'student'" @change="getChoose" value="student">我是学生</a-radio>
          </div>
          <div class="parent">
            <a-radio :checked="type == 'parent'" @change="getChoose" value="parent">我是家长</a-radio>
          </div>
        </div>
        <div v-else class="info">
          <div class="leftInfo">
            <div class="title">
              个人信息
            </div>
            <a-form ref="form" :model="model" layout="horizontal" :rules="validatorRules" labelAlign="left"
              :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-form-item prop="username" required label="姓名">
                <a-input v-model="model.username" placeholder="请输入姓名">
                </a-input>
              </a-form-item>
              <a-form-item prop="password" required label="性别">
                <a-radio-group name="radioGroup" :default-value="1">
                  <a-radio :value="1">
                    男
                  </a-radio>
                  <a-radio :value="2">
                    女
                  </a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item prop="password" required label="出生年月">
                <a-date-picker @change="onChange" style="width: 100%;" />
              </a-form-item>
              <a-form-item prop="username" required label="手机号">
                <a-input v-model="model.username" placeholder="请输入手机号">
                </a-input>
              </a-form-item>
              <a-form-item prop="username" required label="密码">
                <a-input v-model="model.username" placeholder="请输入密码">
                </a-input>
              </a-form-item>
              <a-form-item prop="password" required label="与子女关系" v-if="type == 'parent'">
                <a-radio-group name="radioGroup" :default-value="1">
                  <a-radio :value="1">
                    父亲
                  </a-radio>
                  <a-radio :value="2">
                    母亲
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-form>
          </div>
          <div class="line"></div>
          <div class="rightInfo" v-if="type == 'student'">
            <div class="title">
              学校信息
            </div>
            <a-form ref="form" :model="model" layout="horizontal" :rules="validatorRules" labelAlign="left"
              :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
              <a-form-item prop="username" required label="学校">
                <a-select style="width: 100%" placeholder="请选择">
                  <a-select-option value="lucy">
                    Lucy
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item prop="username" required label="学院">
                <a-select style="width: 100%" placeholder="请选择">
                  <a-select-option value="lucy">
                    Lucy
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item prop="username" required label="系（部）">
                <a-select style="width: 100%" placeholder="请选择">
                  <a-select-option value="lucy">
                    Lucy
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item prop="username" required label="年级">
                <a-select style="width: 100%" placeholder="请选择">
                  <a-select-option value="lucy">
                    Lucy
                  </a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item prop="username" required label="班级">
                <a-select style="width: 100%" placeholder="请选择">
                  <a-select-option value="lucy">
                    Lucy
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item prop="username" required label="学籍号">
                <a-input v-model="model.username" placeholder="请输入学籍号，并作为登录账号">
                </a-input>
              </a-form-item>
            </a-form>
          </div>
          <div class="rightInfo" v-else>
            <div class="title">
              子女信息
            </div>
            <a-form ref="form" :model="model" layout="horizontal" :rules="validatorRules" labelAlign="left"
              :label-col="{ span: 12 }" :wrapper-col="{ span: 12 }">
              <a-form-item prop="username" required label="上传二维码图片">

                <a-upload name="file" :multiple="true" action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                  :headers="headers" @change="handleChange">
                  <a-button type="primary">
                    上传图片
                  </a-button>
                </a-upload>
              </a-form-item>

              <div class="shuoming">
                说明：二维码由学生本人提供或者由该学生的
              </div>
            </a-form>
          </div>
        </div>
        <div v-if="num == 'first'" class="next" @click="getNext">
          下一步
        </div>
        <div v-else class="next">
          提交
        </div>
    </div>
</template>

<script>
export default {
  components: {
  },
  data() {
    return {
      type: 'parent',
      num: 'last',
      model: {
        username: '',
        password: '',
        inputCode: ''
      },
      validatorRules: {
        username: [
          { required: true, message: '请输入用户名!' },
          { validator: this.handleUsernameOrEmail }
        ],
        password: [{
          required: true, message: '请输入密码!', validator: 'click'
        }],

      },
    }
  },
  created() {

  },
  methods: {
    getChoose(e) {
      this.type = e.target.value
    },
    getNext() {
      this.num = 'last'
    },
    back() {
      this.$router.go(-1);
    }
  }

}
</script>
<style lang="less">
.bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFFFFF 0%, #ECF7FF 100%);
}

.choose {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 380px;
  left: 0;
  right: 0;

  .student {
    width: 316px;
    height: 130px;
    background: url(../../assets/image/choose.png) no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: left;
  }

  .parent {
    width: 316px;
    height: 130px;
    background: url(../../assets/image/choose1.png) no-repeat;
    background-size: 100% 100%;
    margin-left: 46px;
    display: flex;
    align-items: center;
    justify-content: left;
  }
}

.back {
  position: fixed;
  left: 60px;
  top: 58px;
  cursor: pointer;
}

.next {
  width: 345px;
  height: 38px;
  background: #009AF5;
  border-radius: 19px;
  text-align: center;
  line-height: 38px;
  font-size: 14px;
  color: #FFFFFF;
  position: fixed;
  bottom: 127px;
  left: 0;
  right: 0;
  cursor: pointer;
  margin: 0 auto
}

.info {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: fixed;
  bottom: 262px;
  left: 0;
  right: 0;

  .title {
    font-size: 16px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 36px;
  }

  .leftInfo,
  .rightInfo {
    width: 347px;
  }

  .line {
    width: 1px;
    height: 337px;
    border: 1px solid rgba(177, 177, 177, 0.28);
    margin: 0 112px;
    margin-top: 20px;
  }
}
</style>