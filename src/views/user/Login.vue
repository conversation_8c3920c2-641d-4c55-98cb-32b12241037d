<template>
  <div class="login-main">
    <div class="login-bg"></div>

    <!-- <div class="login-top">
      <img class="top-icon" src="../../assets/img/icon.png" />
    </div> -->
    <div class="logn-logo">
      <!-- <img src="../../assets/image/logo.png" alt=""> -->
      <!-- 青岛澄明科技有限学校 -->
    </div>
    <div class="login-cotent">

      <div class="content-right">
        <div class="content-title">
          <!-- <a-typography-title>登录</a-typography-title> -->
          <div>登录</div>
          <div>心理健康智慧服务云平台</div>
        </div>
        <a-form-model a-form-model ref="form" :model="model" :rules="validatorRules" class="user-layout-login">
          <a-form-model-item prop="username">
            <a-input size="large" v-model="model.username" placeholder="请输入账号">
              <template #prefix>
                <a-icon type="user" style="color: rgba(0, 0, 0, 0.45)" />
              </template>
            </a-input>
          </a-form-model-item>

          <a-form-model-item prop="password">
            <a-input v-model="model.password" size="large" type="password" autocomplete="false" placeholder="请输入密码">
              <template #prefix>
                <a-icon type="lock" style="color: rgba(0, 0, 0, 0.45)" />
              </template>
            </a-input>
          </a-form-model-item>
          <a-form-model-item>
            <a-checkbox-group v-model="model.type">
              <a-checkbox value="1" name="type">记住密码</a-checkbox>
            </a-checkbox-group>
          </a-form-model-item>
          <a-form-model-item>
            <a-button style="background: #009AF5;height:50px;margin-top: 40px" size="large" type="primary" htmlType="submit" class="login-button" :loading="loginBtn" @click.stop.prevent="handleSubmit" :disabled="loginBtn">登录
            </a-button>
          </a-form-model-item>
          <a-form-model-item>
            <div class="content-password">
              <div @click="getRouter('/user/register')">注册</div>
              <div @click="getRouter('/user/password')">忘记密码？</div>
            </div>
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
    <login-select-tenant ref="loginSelect" @success="loginSelectOk"></login-select-tenant>
  </div>
</template>

<script>
import { postAction, getAction } from '@/api/manage'
import Vue from 'vue'
import { ACCESS_TOKEN, ENCRYPTED_STRING } from "@/store/mutation-types"
import { mapActions } from "vuex"
import { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt'
import { timeFix } from "@/utils/util"
import LoginSelectTenant from "./LoginSelectTenant"

export default {
  components: {
    LoginSelectTenant,
  },
  data () {
    return {
      hide: true, //眼睛显示隐藏
      password: 'password', //input 密码隐藏
      model: {
        username: '',
        password: '',
        inputCode: ''
      },
      loginType: 0,
      validatorRules: {
        username: [
          { required: true, message: '请输入用户名!' },
          { validator: this.handleUsernameOrEmail }
        ],
        password: [{
          required: true, message: '请输入密码!', validator: 'click'
        }],

      },
      customActiveKey: 'tab1',
      requestCodeSuccess: false,
      randCodeImage: '',
      currdatetime: '',
      loginBtn: false,
      requiredTwoStepCaptcha: false,
      stepCaptchaVisible: false,
      //手机号登录用
      state: {
        time: 60,
        smsSendBtn: false,
      },
      encryptedString: {
        key: "",
        iv: "",
      },
    }
  },
  created () {
    this.currdatetime = new Date().getTime();
    this.model.rememberMe = true
    Vue.ls.remove(ACCESS_TOKEN)
    this.getRouterData();
    this.handleChangeCheckCode();
  },
  methods: {
    // 路由跳转
    getRouter (path) {
      this.$router.push({ path: path });
    },
    ...mapActions(['Login', 'Logout', 'PhoneLogin']),
    handleTabClick (key) {
      this.customActiveKey = key
    },
    changeType () {
      if (this.hide) {
        this.hide = false
        this.password = 'text'
      } else {
        this.hide = true
        this.password = 'password'
      }
    },
    handleUsernameOrEmail (rule, value, callback) {
      const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/;
      if (regex.test(value)) {
        this.loginType = 0
      } else {
        this.loginType = 1
      }
      callback()
    },
    /**刷新验证码*/
    handleChangeCheckCode () {
      this.currdatetime = new Date().getTime();
      this.model.inputCode = ''
      getAction(`/sys/randomImage/${this.currdatetime}`).then(res => {
        if (res.success) {
          this.randCodeImage = res.result
          this.requestCodeSuccess = true
        } else {
          this.$message.error(res.message)
          this.requestCodeSuccess = false
        }
      }).catch(() => {
        this.requestCodeSuccess = false
      })
    },
    /**跳转到登录页面的参数-账号获取*/
    getRouterData () {
      this.$nextTick(() => {
        let temp = this.$route.params.username || this.$route.query.username || ''
        if (temp) {
          this.model['username'] = temp
        }
      })
    },
    handleRememberMeChange (e) {
      this.model.rememberMe = e.target.checked
    },
    //登录
    handleSubmit () {
      let that = this
      let loginParams = {};
      that.loginBtn = true;
      // 使用账户密码登录
      if (that.customActiveKey === 'tab1') {
        this.loginByUsername();
      } else {
        this.loginByPhone()
      }
    },
    /**
     * 验证字段
     * @param arr
     * @param callback
     */
    validateFields (arr, callback) {
      let promiseArray = []
      for (let item of arr) {
        let p = new Promise((resolve, reject) => {
          this.$refs['form'].validateField(item, (err) => {
            if (!err) {
              resolve();
            } else {
              reject(err);
            }
          })
        });
        promiseArray.push(p)
      }
      Promise.all(promiseArray).then(() => {
        callback()
      }).catch(err => {
        callback(err)
      })
    },
    //账号密码登录
    loginByUsername () {
      this.validateFields(['username', 'password'], (err) => {
        if (!err) {
          let loginParams = {
            username: this.model.username,
            password: this.model.password,
            remember_me: this.model.rememberMe,
            checkKey: this.currdatetime
          }
          this.Login(loginParams).then((res) => {
            this.$refs.loginSelect.show(res.result)
          }).catch((err) => {
            this.requestFailed(err);
          });
        } else {
          this.loginBtn = false;
        }
      })
    },
    //手机号码登录
    loginByPhone () {
      this.validateFields(['mobile', 'captcha'], (err) => {
        if (!err) {
          let loginParams = {
            mobile: this.model.mobile,
            captcha: this.model.captcha,
            remember_me: this.model.rememberMe
          }
          console.log("登录参数", loginParams)
          this.PhoneLogin(loginParams).then((res) => {
            console.log(res.result);
            this.$refs.loginSelect.show(res.result)
          }).catch((err) => {
            this.requestFailed(err);
          })
        } else {
          this.loginBtn = false;
        }
      })
    },
    //登录后台失败
    requestFailed (err) {
      let description = ((err.response || {}).data || {}).message || err.message || "请求出现错误，请稍后再试"
      this.$notification['error']({
        message: '登录失败',
        description: description,
        duration: 4,
      });
      //密码错误后更新验证码
      if (description.indexOf('密码错误') > 0) {
        this.handleChangeCheckCode();
      }
      this.loginBtn = false;
    },
    loginSelectOk () {
      this.loginSuccess()
    },
    //登录成功
    loginSuccess () {
      this.$router.push({ path: "/dashboard/analysis" }).catch(() => {
        console.log('登录跳转首页出错,这个错误从哪里来的')
      })
      this.$notification.success({
        message: '欢迎',
        description: `${timeFix()}，欢迎回来`,
      });
    },
    validateMobile (rule, value, callback) {
      if (!value || new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/).test(value)) {
        callback();
      } else {
        callback("您的手机号码格式不正确!");
      }
    },
    //获取验证码
    getCaptcha (e) {
      e.preventDefault();
      let that = this;
      that.validateFields(['mobile'], (err) => {
        if (!err) {
          that.state.smsSendBtn = true;
          let interval = window.setInterval(() => {
            if (that.state.time-- <= 0) {
              that.state.time = 60;
              that.state.smsSendBtn = false;
              window.clearInterval(interval);
            }
          }, 1000);

          const hide = that.$message.loading('验证码发送中..', 0);
          let smsParams = {};
          smsParams.mobile = that.model.mobile;
          smsParams.smsmode = "0";
          postAction("/sys/sms", smsParams)
            .then(res => {
              if (!res.success) {
                setTimeout(hide, 0);
                that.cmsFailed(res.message);
              }
              console.log(res);
              setTimeout(hide, 500);
            })
            .catch(err => {
              setTimeout(hide, 1);
              clearInterval(interval);
              that.state.time = 60;
              that.state.smsSendBtn = false;
              that.requestFailed(err);
            });
        }
      }
      );
    },
    cmsFailed (err) {
      this.$notification['error']({
        message: '登录失败',
        description: err,
        duration: 4,
      });
    },
    stepCaptchaSuccess () {
      this.loginSuccess()
    },
    stepCaptchaCancel () {
      this.Logout().then(() => {
        this.loginBtn = false
        this.stepCaptchaVisible = false
      })
    },
    //获取密码加密规则
    getEncrypte () {
      var encryptedString = Vue.ls.get(ENCRYPTED_STRING);
      if (encryptedString == null) {
        getEncryptedString().then((data) => {
          this.encryptedString = data
        });
      } else {
        this.encryptedString = encryptedString;
      }
    }

  }

}
</script>
<style lang="less">
.login-main {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 62px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;

  .login-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('../../assets/image/loginbg.png');
  }

  .login-cotent {
    position: fixed;
    right: 100px;
    width: 407px;
    height: 404px;
    display: flex;
    justify-content: space-between;
    margin-top: 50px;
    padding: 0 38px;

    .content-right {
      display: flex;
      flex-direction: column;
      width: 407px;
      padding: 43px 0;
    }

    .user-layout-login {
      padding-top: 23px;
      height: 284px;

      .ant-form-item-label {
        height: 38px;

        label {
          padding-bottom: 10px;
          font-size: 16px;
          line-height: 22px;

          &::after {
            content: '';
          }
        }
      }

      .ant-input {
        height: 40px;
      }

      .getCaptcha {
        display: block;
        width: 100%;
        height: 27px;
      }

      .forge-password {
        font-size: 12px;
      }

      .user-login-other {
        text-align: left;
        margin-top: 16px;
        line-height: 15px;

        .item-icon {
          font-size: 16px;
          color: rgba(0, 0, 0, 0.2);
          margin-left: 10px;
          vertical-align: middle;
          cursor: pointer;
          transition: color 0.3s;

          &:hover {
            color: #1890ff;
          }
        }

        .register {
          float: right;
        }
      }

      .login-button {
        height: 40px;
        width: 100%;
        margin-top: 13px;
        background: #0066ff;
        border-radius: 4px;
        font-size: 16px;
        line-height: 40px;
      }
    }

    .link-btn {
      padding-top: 16px;
      font-size: 13px;
      line-height: 3px;
    }
  }
}

.content-title {
  div:first-child {
    font-size: 28px;
    font-weight: bold;
    color: #009af5;
    margin-bottom: 16px;
  }

  div:last-child {
    font-size: 20px;
    color: #999999;
  }
}

.content-password {
  display: flex;
  align-items: center;
  justify-content: space-between;
  div {
    cursor: pointer;
  }
}

.logn-logo {
  position: fixed;
  right: 15px;
  top: 15px;
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #666666;

  img {
    width: 35px;
    height: 22px;
    margin-right: 10px;
  }
}
</style>
<style>
.valid-error .ant-select-selection__placeholder {
  color: #f5222d;
}
</style>