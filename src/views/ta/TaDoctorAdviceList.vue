<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="任务名称">
              <j-input placeholder="请输入任务名称" v-model="queryParam.title" :allowClear="true"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="状态">
              <j-dict-select-tag v-model="queryParam.status" placeholder="请选择状态" dictCode="completeStatus" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery" style="margin-left: 8px"></j-super-query>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" v-has="'advice:add'" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" class="j-table-force-nowrap" @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a @click="getDetail(record)">详情</a>
          <a-divider type="vertical" v-has="'advice:edit'" />
          <a @click="handleEdit(record)" v-has="'advice:edit'">编辑</a>
          <a-divider type="vertical" v-has="'advice:del'" />
          <a-popconfirm v-has="'advice:del'" title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
          <!--          <a-divider type="vertical"/>-->
          <!--          <a-dropdown>-->
          <!--            <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>-->
          <!--            <a-menu slot="overlay">-->
          <!--              <a-menu-item>-->
          <!--                <a @click="handleEdit(record)">编辑</a>-->
          <!--              </a-menu-item>-->
          <!--              <a-menu-item>-->
          <!--                -->
          <!--              </a-menu-item>-->
          <!--            </a-menu>-->
          <!--          </a-dropdown>-->
        </span>

      </a-table>
    </div>

    <ta-doctor-advice-modal ref="modalForm" @ok="modalFormOk"></ta-doctor-advice-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { colAuthFilter } from "@/utils/authFilter"
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import TaDoctorAdviceModal from './modules/TaDoctorAdviceModal'

export default {
  name: 'TaDoctorAdviceList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    TaDoctorAdviceModal
  },
  data () {
    return {
      isorter: {
        column: 'limitDate',
        order: 'desc'
      },
      description: '医嘱任务管理页面',
      // 表头
      columns: [
        {
          title: '任务名称',
          align: 'center',
          dataIndex: 'title'
        },
        {
          title: '学生姓名',
          align: 'center',
          dataIndex: 'patientName'
        },
        {
          title: '老师名称',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '完成时限',
          align: 'center',
          dataIndex: 'limitDate'
          // ,
          // customRender: function(text) {
          //   return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
          // }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/ta/taDoctorAdvice/list',
        delete: '/ta/taDoctorAdvice/delete',
        deleteBatch: '/ta/taDoctorAdvice/deleteBatch',
        exportXlsUrl: '/ta/taDoctorAdvice/exportXls',
        importExcelUrl: 'ta/taDoctorAdvice/importExcel'

      },
      dictOptions: {},
      superFieldList: []
    }
  },
  created () {
    this.disableMixinCreated = true;
    this.columns = colAuthFilter(this.columns, 'doctorAdvice:');
    // this.loadData();
    // this.initDictConfig();
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    getDetail (record) {
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title = "详情";
      this.$refs.modalForm.disableSubmit = true;
      if (record.status > 0) {
        this.$refs.modalForm.finishHidden = true;
      }
    },
    initDictConfig () {
    },
    getSuperFieldList () {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'doctorName', text: '老师名称' })
      fieldList.push({ type: 'string', value: 'patientName', text: '学生姓名' })
      fieldList.push({ type: 'string', value: 'content', text: '留言内容' })
      fieldList.push({ type: 'date', value: 'limitDate', text: '完成时限' })
      this.superFieldList = fieldList
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>