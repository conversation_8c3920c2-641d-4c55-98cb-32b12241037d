<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <!--          <a-col :md="6" :sm="8">-->
          <!--            <a-form-item label="学生名称">-->
          <!--              <j-input placeholder="请输入学生名称" v-model="queryParam.patientName"></j-input>-->
          <!--            </a-form-item>-->
          <!--          </a-col>-->
          <a-col :md="6" :sm="8">
            <a-form-item label="量表">
              <a-select placeholder="请选择量表" v-model="queryParam.measureId" allowClear>
                <a-select-option v-for="measure in measures" :value="measure.id" :key="measure.id">
                  {{ measure.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="状态">
              <j-dict-select-tag v-model="queryParam.status" placeholder="请选择状态" dictCode="measure_task_status" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery" style="margin-left: 8px"></j-super-query>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" v-has="'measureTask:add'" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-has="'measureTask:del'" v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length
        }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" class="j-table-force-nowrap" @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a v-has="'measureTask:edit'" @click="handleEdit(record)">编辑</a>
          <a-divider v-has="'measureTask:edit'" type="vertical" />
          <a v-if="record.status == '0'" @click="startQuestions(record)">开始答题</a>
          <a-divider v-if="record.status == '0'" type="vertical" />
          <a v-if="record.status == '1'" @click="continueQuestions(record)">继续答题</a>
          <a-divider v-if="record.status == '1'" type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'measureTask:del'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <ta-measure-task-modal ref="modalForm" @ok="modalFormOk"></ta-measure-task-modal>
  </a-card>
</template>

<script>

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import TaMeasureTaskModal from './modules/TaMeasureTaskModal'
import { getAction, httpAction } from '@api/manage'
import { colAuthFilter } from "@/utils/authFilter";

export default {
  name: 'TaMeasureTaskList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    TaMeasureTaskModal
  },
  data () {
    return {
      isorter: {
        column: 'limitDate',
        order: 'desc'
      },
      measures: [],
      description: '量表任务管理页面',
      // 表头
      columns: [
        {
          title: '量表名称',
          align: 'center',
          dataIndex: 'measureName',
        },
        {
          title: '老师名称',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '学生姓名',
          align: 'center',
          dataIndex: 'patientName'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '完成时限',
          align: 'center',
          dataIndex: 'limitDate'
          // ,
          // customRender: function(text) {
          //   return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
          // }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        // /psychology/psMeasure/listAllWithTenants
        listAll: '/psychology/psMeasure/listAllWithTenants',
        list: '/ta/taMeasureTask/list',
        delete: '/ta/taMeasureTask/delete',
        deleteBatch: '/ta/taMeasureTask/deleteBatch',
        exportXlsUrl: '/ta/taMeasureTask/exportXls',
        importExcelUrl: 'ta/taMeasureTask/importExcel',
        startQuestions: '/ta/taMeasureTask/startQuestions',
        goContinueQuestionPage: '/ta/taMeasureTask/goContinueQuestionPage',
      },
      dictOptions: {},
      superFieldList: []
    }
  },
  created () {
    this.disableMixinCreated = true;
    this.columns = colAuthFilter(this.columns, 'measureTask:');
    this.loadPsTemplate()
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    initDictConfig () {
    },
    getSuperFieldList () {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'doctorName', text: '老师名称' })
      fieldList.push({ type: 'string', value: 'patientName', text: '学生姓名' })
      fieldList.push({ type: 'date', value: 'limitDate', text: '完成时限' })
      fieldList.push({ type: 'int', value: 'remark', text: '备注' })
      this.superFieldList = fieldList
    },
    startQuestions (record) {
      if (record.measureId) {
        let that = this
        let httpurl = that.url.startQuestions
        getAction(httpurl, { 'id': record.id }).then((res) => {
          if (res.success) {
            this.$notification['success']({
              message: '添加成功',
              duration: 3,
              description: '已在选择的终端推送答题信息'
            })
            that.confirmLoading = false
            that.close()
          } else {
            that.$message.error(res.message)
          }
        })
      } else {
        this.$message.warning('量表套餐中未包含任何量表!')
      }
    },
    /**
     * 初始化量表
     */
    loadPsTemplate () {
      httpAction(this.url.listAll, {}, 'get').then((res) => {
        if (res.success) {
          this.measures = res.result
        }
      })
    },
    getMeasureName (id) {
      let result = ''
      this.measures.forEach(item => {
        if (id === item.id) {
          result = item.name
        }
      })
      return result
    },
    continueQuestions (record) {
      let that = this
      let httpurl = that.url.goContinueQuestionPage
      let formData = {}
      formData.id = record.id
      getAction(httpurl, formData).then((res) => {
        if (res.success) {
          this.$notification['success']({
            message: '添加成功',
            duration: 3,
            description: '已在选择的终端推送答题信息'
          })
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
      })
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>