<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okText="disableSubmit?'完成':'确定'"
    :okButtonProps="{ class:{'jee-hidden': finishHidden} }"
    @cancel="handleCancel"
    cancelText="关闭">
<!--    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"-->
    <ta-doctor-advice-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></ta-doctor-advice-form>
  </j-modal>
</template>

<script>

  import TaDoctorAdviceForm from './TaDoctorAdviceForm'
  export default {
    name: 'TaDoctorAdviceModal',
    components: {
      TaDoctorAdviceForm
    },
    data () {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: false,
        finishHidden:false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        if(!this.disableSubmit){
          this.$refs.realForm.submitForm();
        }else{
          this.$refs.realForm.finishTask();
        }
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>