<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="老师名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorName">
              <a-input v-model="model.doctorName" disabled :defaultValue="nickname()" placeholder="请输入老师名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="!isEdit">
            <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sendByGroup">
              <span slot="label">
                是否按标签下发&nbsp;
                <a-tooltip title="按照学生的标签批量下发医嘱任务">
                  <a-icon type="question-circle-o" />
                </a-tooltip>
              </span>
              <a-radio-group v-model="model.sendByGroup">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="0">否</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.sendByGroup === 0">
            <a-form-model-item label="学生姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
              <j-select-patient v-model="model.patientName" @back="backPatientInfo" :backInfo="true" :disabled="disabledEdit" :isRadio="true"></j-select-patient>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.sendByGroup === 1">
            <a-form-model-item label="学生标签" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="selectedGroups">
              <j-label-select v-model="model.selectedGroups" :multiple="false" @change="changeSelectedGroups"></j-label-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="完成时限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitDate">
              <!--              <j-date placeholder="请选择完成时限" v-model="model.limitDate" style="width: 100%"/>-->
              <a-date-picker format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" :show-time="{ format: 'HH:mm' }" v-model="model.limitDate" placeholder="请选择完成时限" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
              <a-input v-model="model.title" placeholder="请输入标题"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="任务内容" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content">
              <a-textarea :rows="5" v-model="model.content" placeholder="请输入任务内容"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction } from '@/api/manage'
import { mapGetters } from 'vuex'
import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'

export default {
  name: 'TaDoctorAdviceForm',
  components: { JLabelSelect },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      model: {
        sendByGroup: 0,
        selectedGroups: null
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      disabledEdit: true,
      confirmLoading: false,
      validatorRules: {
        sendByGroup: [{ required: true, message: '请选择是否按标签下发!' }],
        patientName: [{ required: true, message: '请选择学生!' }],
        selectedGroups: [{ required: true, message: '请选择学生标签!' }],
        limitDate: [{ required: true, message: '请选择完成时限!' }],
        title: [{ required: true, message: '请输入标题!' }],
        content: [{ required: true, message: '请输入任务内容!' }]
      },
      url: {
        add: '/ta/taDoctorAdvice/add',
        edit: '/ta/taDoctorAdvice/edit',
        finish: '/ta/taDoctorAdvice/finish',
        queryById: '/ta/taDoctorAdvice/queryById'
      },
      isEdit: false
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    changeSelectedGroups (value) {
      this.model.selectedGroups = value
    },
    add () {
      this.modelDefault.doctorId = this.userInfo().id
      this.modelDefault.doctorName = this.userInfo().realname
      this.disabledEdit = false
      this.edit(this.modelDefault)
      this.isEdit = false
    },
    edit (record) {
      this.model = Object.assign({}, record)
      this.model.sendByGroup = 0
      this.visible = true
      this.isEdit = true
    },
    submitForm () {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
            this.model.doctorId = this.userInfo().id;
          } else {
            httpurl += this.url.edit
            method = 'put'
            delete this.model.doctorId
          }
          let params = Object.assign({}, this.model)
          httpAction(httpurl, params, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }

      })
    },
    finishTask () {
      let that = this
      that.confirmLoading = true
      let httpurl = this.url.finish
      let method = 'put'
      delete this.model.doctorId
      let value = { id: this.model.id }
      httpAction(httpurl, value, method).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.$emit('ok')
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
        that.confirmLoading = false
      })
    },
    backPatientInfo (info) {
      this.model.patientId = info[0].value
      this.model.patientName = info[0].text
    }
  }
}
</script>