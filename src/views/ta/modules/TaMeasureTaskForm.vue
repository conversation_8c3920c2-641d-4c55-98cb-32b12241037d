<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="老师名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorName">
              <a-input v-model="model.doctorName" disabled :defaultValue="nickname()" placeholder="请输入老师名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="!isEdit">
            <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sendByGroup">
              <span slot="label">
                是否按标签下发&nbsp;
                <a-tooltip title="按照学生的标签批量下发医嘱任务">
                  <a-icon type="question-circle-o" />
                </a-tooltip>
              </span>
              <a-radio-group v-model="model.sendByGroup">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="0">否</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.sendByGroup === 0">
            <a-form-model-item label="学生" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
              <j-select-patient v-model="model.patientName" @back="backPatientInfo" :backInfo="true" :disabled="disabledEdit" :isRadio="true"></j-select-patient>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.sendByGroup === 1">
            <a-form-model-item label="学生标签" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="selectedGroups">
              <j-label-select v-model="model.selectedGroups" :multiple="false" @change="changeSelectedGroups"></j-label-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item v-if="disabledEdit" label="量表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="measureId">
              <a-select show-search placeholder="请选择量表" option-filter-prop="children" style="width: 100%" :filter-option="filterOption" v-model="model.measureId" @change="handleChange">
                <a-select-option v-for="measure in measures" :value="measure.id" :key="measure.id">
                  {{ measure.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="量表" v-if="!disabledEdit" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="taskMeasures">
              <a-select mode="multiple" show-search placeholder="请选择量表" option-filter-prop="children" style="width: 100%" :filter-option="filterOption" v-model="model.taskMeasures" @change="handleChange">
                <a-select-option v-for="measure in measures" :value="measure.id" :key="measure.id">
                  {{ measure.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="完成时限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitDate">
              <a-date-picker format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" :show-time="{ format: 'HH:mm' }" v-model="model.limitDate" placeholder="请选择完成时限" style="width: 100%" />
              <!--              <j-date placeholder="请选择完成时限" v-model="model.limitDate" style="width: 100%"/>-->
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
              <a-textarea :rows="5" v-model="model.remark" placeholder="请输入备注" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction } from '@/api/manage'
import { mapGetters } from 'vuex'
import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'

export default {
  name: 'TaMeasureTaskForm',
  components: { JLabelSelect },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      model: {
        taskMeasures: [],
        sendByGroup: 0,
        selectedGroups: null
      },
      disabledEdit: true,
      measures: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        sendByGroup: [{ required: true, message: '请选择是否按标签下发!' }],
        patientName: [{ required: true, message: '请选择学生!' }],
        selectedGroups: [{ required: true, message: '请选择学生标签!' }],
        measureId: [{ required: true, message: '请选择量表!' }],
        taskMeasures: [{ required: true, message: '请选择量表!' }],
        limitDate: [{ required: true, message: '请选择完成时限!' }]
      },
      url: {
        add: '/ta/taMeasureTask/add',
        edit: '/ta/taMeasureTask/edit',
        queryById: '/ta/taMeasureTask/queryById',
        listAll: '/psychology/psMeasure/listAllWithTenants'
      },
      isEdit: false
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    changeSelectedGroups (value) {
      this.model.selectedGroups = value
    },
    add () {
      this.modelDefault.sendByGroup = 0
      this.modelDefault.doctorId = this.userInfo().id
      this.modelDefault.doctorName = this.userInfo().realname
      this.disabledEdit = false
      this.edit(this.modelDefault)
      this.isEdit = false
    },
    edit (record) {
      this.model = Object.assign({}, record)
      this.loadPsTemplate()
      this.model.taskMeasures = []
      // if (this.model.measureId) {
      //   this.model.measureId = this.model.measureId.split(',')
      // }
      if (record.measureId) {
        this.model.measureId = record.measureId
        this.model.taskMeasures.push(record.measureId)
      }
      this.model.sendByGroup = 0
      this.visible = true
      this.isEdit = true
    },
    submitForm () {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
            this.model.doctorId = this.userInfo().id;
            let taskMeasures = this.model.taskMeasures
            let measures = []
            taskMeasures.forEach(measureId => {
              if (measureId) {
                let data = { id: measureId, name: this.measures.filter(item => item.id === measureId)[0].name }
                measures.push(data)
              }
            })
            this.model.measures = measures
          } else {
            httpurl += this.url.edit
            method = 'put'
            delete this.model.doctorId
          }
          this.model.taskMeasures = null
          let params = Object.assign({}, this.model)
          if (method == 'put') {
            params.measureName = this.measures.filter(item => item.id === this.model.measureId)[0].name
          }
          httpAction(httpurl, params, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    },
    backPatientInfo (info) {
      this.model.patientId = info[0].value
      this.model.patientName = info[0].text
    },
    /**
     * 初始化量表下拉选
     */
    loadPsTemplate () {
      httpAction(this.url.listAll, {}, 'get').then((res) => {
        if (res.success) {
          this.measures = res.result
        }
      })
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    handleChange (value) {
      console.log(this.model.taskMeasures);
      // this.model.taskMeasures.push(value)
    },
  }
}
</script>