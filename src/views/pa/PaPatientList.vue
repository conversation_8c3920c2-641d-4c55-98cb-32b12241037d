<template>
  <a-row :gutter="10">
    <a-col :md="leftColMd" :sm="24" style="margin-bottom: 20px">
      <a-card :bordered="false">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :lg="6" :sm="8">
                <a-form-item label="真实姓名">
                  <a-input placeholder="请输入真实姓名" v-model="queryParam.name"></a-input>
                </a-form-item>
              </a-col>
              <a-col :lg="6" :sm="8">
                <a-form-item label="手机号">
                  <a-input placeholder="请输入手机号" v-model="queryParam.telphone"></a-input>
                </a-form-item>
              </a-col>
              <a-col :lg="6" :sm="8">
                <a-form-item label="标签">
                  <a-tree-select v-model="queryParam.labelId" style="width: 100%" :tree-data="treeData" treeDefaultExpandAll allow-clear :replace-fields="{children:'children', label:'name', value: 'id'}" placeholder="请选择标签" tree-node-filter-prop="label" />
                </a-form-item>
              </a-col>
              <a-col :lg="6" :sm="8">
                <a-form-item label="编号">
                  <a-input placeholder="请输入编号" v-model="queryParam.number"></a-input>
                </a-form-item>
              </a-col>
              <a-col :lg="6" :sm="8">
                <a-form-item label="组织机构名称">
                  <a-input placeholder="请输入组织机构名称" v-model="queryParam.doctorName"></a-input>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <div>
            <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
            <a-popover v-model="visible" trigger="click" placement="bottom">
              <div slot="content">
                <vue-qr :size="200" :margin="10" :auto-color="true" :dot-scale="1" :text="qrUrl" />
              </div>
              <!-- <a-button type="primary">老师二维码</a-button> -->
            </a-popover>
            <a-button @click="handleSend" type="primary">批量发送</a-button>
          </div>
          <div class="table-page-search-submitButtons">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            <a-button type="primary" icon="upload" @click="handleExportXls('学生')">导出</a-button>
            <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
              <a-button type="primary" icon="import">导入</a-button>
            </a-upload>
            <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery" style="margin-left: 8px"></j-super-query>
          </div>
        </div>

        <!-- table区域-begin -->
        <div>
          <!--          <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">-->
          <!--            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{-->
          <!--            selectedRowKeys.length }}</a>项-->
          <!--            <a style="margin-left: 24px" @click="onClearSelected">清空</a>-->
          <!--          </div>-->

          <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange">

            <template slot="doctorName" slot-scope="record">{{ record.doctorName }}</template>

            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="imgSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
              <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;" />
            </template>
            <template slot="fileSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
              <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
                下载
              </a-button>
            </template>

            <template slot="label" slot-scope="scope">
              <div class="label-item">
                <template v-for="(item, index) in scope.labels">
                  <a-tag color="#FFAA00" v-if="index < 4" :key="item.labelId">
                    {{ item.labelName }}
                  </a-tag>
                </template>

                <a-popover title="标签" v-if="scope.labels.length > 4">
                  <template slot="content">
                    <a-tag color="cyan" v-for="item in scope.labels" :key="item.labelId + 'tag'">
                      {{ item.labelName }}
                    </a-tag>
                  </template>

                  <div>...</div>
                </a-popover>
              </div>
            </template>

            <span slot="action" slot-scope="text, record">
              <a @click="handleDetail(record)">详情</a>
              <a-divider type="vertical" />
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <!-- <a @click="handleWeekDetail(record)">睡眠周报</a> -->
              <!-- <a-divider type="vertical" /> -->
              <a @click="showConfirm(record.id)">删除</a>

            </span>

          </a-table>
        </div>

        <pa-patient-modal ref="modalForm" @ok="modalFormOk"></pa-patient-modal>
        <pa-patient-daily-modal ref="modalDaily" @ok="modalFormOk"></pa-patient-daily-modal>
        <pa-patient-week-modal ref="modalWeek" @ok="modalFormOk"></pa-patient-week-modal>
        <pa-patient-prescription-modal ref="modalPrescription" @ok="loadData1"></pa-patient-prescription-modal>
      </a-card>
    </a-col>

    <a-col :md="rightColMd" :sm="24" v-if="rightcolval == 1">
      <a-card :bordered="false">
        <div class="table-operator">
          <a-button @click="editPrescription" type="primary" icon="plus">修改睡眠处方</a-button>
          <a-button @click="rightcolval = 0,paPatientSchemeId=''">关闭</a-button>
        </div>
        <a-table ref="table" size="middle" bordered rowKey="id" class="j-table-force-nowrap" :columns="columns1" :dataSource="dataSource1" :pagination="ipagination1" @change="handleTableChange" :loading="loading1">
        </a-table>
      </a-card>
    </a-col>

    <pa-patient-batch-modal ref="modalScheme" @ok="modalFormOk" />
  </a-row>
</template>

<script>

import '@/assets/less/TableExpand.less'
import { mapGetters } from 'vuex'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@/api/manage'
import PaPatientModal from './modules/PaPatientModal'
import PaPatientDailyModal from './modules/PaPatientDailyModal'
import PaPatientWeekModal from './modules/PaPatientWeekModal'
import PaPatientPrescriptionModal from './modules/PaPatientPrescriptionModal.vue'
import PaPatientBatchModal from './modules/PaPatientBatchModal'
import VueQr from 'vue-qr'
import Vue from 'vue'
import { TENANT_ID } from "@/store/mutation-types"
import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'
import store from '@/store'
import { Modal } from 'ant-design-vue';
import { TreeSelect } from 'ant-design-vue';
const SHOW_PARENT = TreeSelect.SHOW_PARENT;


export default {
  name: 'PaPatientList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    JLabelSelect,
    PaPatientModal,
    PaPatientDailyModal,
    PaPatientWeekModal,
    PaPatientPrescriptionModal,
    PaPatientBatchModal,
    VueQr
  },
  data () {
    return {
      description: '学生管理页面',
      // 表头
      columns: [
        {
          title: '组织机构名称',
          align: 'center',
          scopedSlots: { customRender: 'doctorName' }
        },
        {
          title: '真实姓名',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '手机号',
          align: 'center',
          dataIndex: 'telphone'
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex_dictText'
        },
        {
          title: '标签',
          align: 'center',
          scopedSlots: { customRender: 'label' }
        },
        {
          title: '编号',
          align: 'center',
          dataIndex: 'number'
        },
        {
          title: '是否开启填写提醒',
          align: 'center',
          dataIndex: 'isSleepDiaryRemind_dictText'
        },
        // {
        //   title: '老师名称',
        //   align: 'center',
        //   dataIndex: 'doctorName'
        // },
        // {
        //   title: '昵称',
        //   align: 'center',
        //   dataIndex: 'nickname'
        // },
        // {
        //   title: '用户头像',
        //   align: 'center',
        //   dataIndex: 'avatarUrl',
        //   scopedSlots: { customRender: 'imgSlot' }
        // },
        // {
        //   title: '主诉',
        //   align: 'center',
        //   dataIndex: 'principleAction'
        // },
        // {
        //   title: '身份证号',
        //   align: 'center',
        //   dataIndex: 'idCard'
        // },
        // {
        //   title: '是否用药',
        //   align: 'center',
        //   dataIndex: 'isMedicine_dictText'
        // },
        // {
        //   title: '状态',
        //   align: 'center',
        //   dataIndex: 'status_dictText'
        // },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      columns1: [
        {
          title: '起床时间',
          align: 'center',
          dataIndex: 'getUpTime'
        },
        {
          title: '睡觉时间',
          align: 'center',
          dataIndex: 'sleepTime'
        },
        {
          title: '处方时长',
          align: 'center',
          dataIndex: 'sleepTimeLong'
        },
        {
          title: '系统推荐起床时间',
          align: 'center',
          dataIndex: 'getUpTimeSys'
        },
        {
          title: '系统推荐睡觉时间',
          align: 'center',
          dataIndex: 'sleepTimeSys'
        },
        {
          title: '更新时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
      ],
      dataSource1: [],
      url: {
        list: '/pa/paPatient/list',
        delete: '/pa/paPatient/delete',
        deleteBatch: '/pa/paPatient/deleteBatch',
        exportXlsUrl: '/pa/paPatient/exportXls',
        importExcelUrl: '/pa/paPatient/importExcel',
        sublist: '/ta/taDoctorAdviceSleep/list'
      },
      dictOptions: {},
      superFieldList: [],
      rightcolval: 0,
      paPatientSchemeId: '',
      ipagination1: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      treeData: [],
      visible: false,
      qrUrl: ''
    }
  },
  created () {
    var userInfo = JSON.parse(localStorage.getItem("pro__Login_Userinfo"));
    // if (userInfo.value.roleId != '1435523044358402049'){
    //   let doctor = {
    //     title: '老师名称',
    //     align: 'center',
    //     dataIndex: 'doctorName'
    //   }
    //   this.columns.unshift(doctor)
    // }
    console.log(userInfo)
    this.getList()
    this.getSuperFieldList()
    if (process.env.NODE_ENV === 'production') {
      this.qrUrl = 'https://cbti.zhisongkeji.com/patient/?id=' + this.userInfo().id + '&name=' + this.userInfo().realname + '&tenantId=' + Vue.ls.get(TENANT_ID)
    } else {
      this.qrUrl = 'http://cbtitest.zhisongkeji.com/patient/?id=' + this.userInfo().id + '&name=' + this.userInfo().realname + '&tenantId=' + Vue.ls.get(TENANT_ID)
    }
    console.log('qrUrl=======', this.qrUrl)
  },
  computed: {
    store () {
      return store
    },
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    leftColMd () {
      return this.paPatientSchemeId == '' ? 24 : 14
    },
    rightColMd () {
      return this.paPatientSchemeId == '' ? 0 : 10
    }
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    initDictConfig () {
    },
    handleSend () {
      this.$refs.modalScheme.add();
      this.$refs.modalScheme.title = "批量发送";
      this.$refs.modalScheme.disableSubmit = false;
    },
    getList () {
      getAction('/label/tree').then((res) => {
        if (res.success) {
          let data = res.result
          data.forEach(item => {
            item.disabled = true
          })
          this.treeData = data
        }
      })
    },
    showConfirm (id) {
      let that = this
      Modal.confirm({
        title: '确认删除',
        content: '删除后不可恢复，是否确认删除？',
        onOk () {
          that.handleDelete(id)
        },
        onCancel () {
        },
        class: 'test',
      });
    },
    changeSelectedGroups (value) {
      this.queryParam.labels = value
    },
    getSuperFieldList () {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'doctorName', text: '老师名称' })
      fieldList.push({ type: 'string', value: 'name', text: '真实姓名' })
      fieldList.push({ type: 'string', value: 'nickname', text: '昵称' })
      fieldList.push({ type: 'string', value: 'number', text: '编号' })
      fieldList.push({ type: 'date', value: 'birthday', text: '生日' })
      fieldList.push({ type: 'int', value: 'sex', text: '性别' })
      fieldList.push({ type: 'string', value: 'stature', text: '身高' })
      fieldList.push({ type: 'string', value: 'weight', text: '体重' })
      fieldList.push({ type: 'string', value: 'telphone', text: '手机号' })
      fieldList.push({ type: 'string', value: 'profession', text: '职业' })
      fieldList.push({ type: 'int', value: 'maritalStatus', text: '婚否' })
      fieldList.push({ type: 'string', value: 'cultural', text: '文化程度' })
      fieldList.push({ type: 'string', value: 'nationality', text: '民族' })
      fieldList.push({ type: 'string', value: 'principleAction', text: '主诉' })
      fieldList.push({ type: 'string', value: 'idCard', text: '身份证号' })
      fieldList.push({ type: 'date', value: 'goBedTime', text: '上床时间' })
      fieldList.push({ type: 'date', value: 'wakeUpTime', text: '起床时间' })
      fieldList.push({ type: 'string', value: 'status', text: '状态' })
      fieldList.push({ type: 'int', value: 'isMedicine', text: '是否服用促进睡眠药物' })
      fieldList.push({ type: 'int', value: 'isSleepDiaryRemind', text: '是否开启睡眠日志填写提醒' })
      this.superFieldList = fieldList
    },
    handleDailyDetail (record) {
      this.$refs.modalDaily.loadData(record);
      this.$refs.modalDaily.title = "睡眠日记";
      this.$refs.modalDaily.disableSubmit = true;
    },
    handleWeekDetail (record) {
      this.$refs.modalWeek.loadData(record);
      this.$refs.modalWeek.title = "睡眠周报";
      this.$refs.modalWeek.disableSubmit = true;
    },
    loadData1 (arg) {
      if (arg === 1) {
        this.ipagination1.current = 1
      }
      if (this.paPatientSchemeId === '') return
      let params = {}//查询条件
      params.patientId = this.paPatientSchemeId
      params.pageNo = this.ipagination1.current;
      params.pageSize = this.ipagination1.pageSize;
      this.loading1 = true
      getAction(this.url.sublist, params).then((res) => {
        if (res.success) {
          this.dataSource1 = res.result.records
          this.ipagination1.total = res.result.total
        }
        this.loading1 = false
      })
    },
    handlePrescription (record) {
      this.rightcolval = 1
      this.paPatientSchemeId = record.id
      this.loadData1()
    },
    editPrescription () {
      this.$refs.modalPrescription.title = "修改睡眠处方"
      this.$refs.modalPrescription.loadData(this.paPatientSchemeId);
    },
    handleDetail (record) {
      localStorage.setItem('PatientDetail', JSON.stringify(record))
      this.$store.dispatch('PatientDetail', record)
      this.$router.push('/pa/PaPatientDetail')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@assets/less/common.less';

.label-item {
  display: flex;
}

.ant-tag {
  margin-bottom: 8px !important;
}

.table-operator {
  display: flex;
  justify-content: space-between;
}
</style>

<style lang="scss">
.ant-modal-confirm-title {
  color: red !important;
}
</style>