<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="5" :sm="7">
            <a-form-item>
              <a-input placeholder="请输入学生姓名/手机号/昵称" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="6">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <!-- <a-button type="primary" @click="newLeaveMessage" icon="add" style="margin-left:8px;">新增留言</a-button> -->
            </span>
          </a-col>
          <a-col :md="6" :sm="6">
            <div v-if="patientName != ''" style="font-size:15px; font-weight: 500; margin-left:10px;">
              姓名：{{patientName}}
            </div>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <!-- <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
    </div> -->

    <!-- table区域-begin -->
    <div class="message-list-content">

      <div class="left-table">
        <div class="table-content">
          <div class="table-item" v-for="(item,index) of dataSource" :key=index @click="onSelectChange(item)">
            <div style="display: flex; flex-direction:row; position:relative;">
              <div class="unread-num" v-if="item.noRead && item.noRead != 0">{{item.noRead}}</div>
              <div class="user-avatar">
                <img class="img" v-if="!item.avatarUrl" src="../../assets/img/user_icon_default.png" alt="" />
                <img class="img" v-else :src="getImgView(item.avatarUrl)" alt="" />
              </div>
              <div class="user-content">
                <div class="user-name">
                  {{item.name}}/{{item.telphone}}
                </div>
                <div class="list-message-view">{{item.lastContent}}</div>
              </div>
            </div>
            <div class="content-time">{{timeChange(item.lastCreateTime)}}</div>
          </div>
        </div>
      </div>
      <div class="message-view">
        <div class="list-content" ref="messagescroll" id="messagescroll">
          <div class="item" :class="item.type != 1 ? 'item-right' : 'item-left'" v-for="item in messageList" :key="item.id">
            <div class="time">{{item.createTime.substring(5,16)}}</div>
            <div class="replayContent replayContent-right" v-if="item.type != 1">
              <div class="chatBox chatBox-right">
                <div class="detail">{{item.content}}</div>
              </div>
              <div class="user">
                <div class="avatar">
                  <img src="../../assets/img/user_icon_default.png" style="width: 40px; height: 40px" alt="" />
                </div>
                <div class="name">{{item.doctorName}}</div>
              </div>
            </div>
            <div class="replayContent replayContent-left" v-else>
              <div class="user">
                <div class="avatar">
                  <img v-if="!patientAvatar" src="../../assets/img/user_icon_default.png" style="width: 40px; height: 40px" alt="" />
                  <img v-else :src="getImgView(patientAvatar)" alt="" style="width: 40px; height: 40px" />
                </div>
                <div class="name">{{item.patientName}}</div>
              </div>
              <div class="chatBox chatBox-left">
                <div class="detail">{{item.content}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="send-bottom">
          <a-textarea class="bottom-text" row="5" v-model="messageContent" placeholder="请输入留言内容"></a-textarea>
          <a-button class="send-button" @click="sendClick" type="primary">发送</a-button>
        </div>
      </div>
    </div>

    <SelectPatientModal ref="SelectPatientModal" :isRadio="true" @selectFinished="handleOK"></SelectPatientModal>
    <!-- <paLeaveMessage-modal ref="modalForm" @ok="modalFormOk"></paLeaveMessage-modal> -->
  </a-card>
</template>

<script>

import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PaLeaveMessageModal from './modules/PaLeaveMessageModal'
import { getAction, getFileAccessHttpUrl } from '@/api/manage'
import PaLeaveMessageReplyList from './PaLeaveMessageReplyList'
import SelectPatientModal from '../../components/jeecgbiz/modal/SelectPatientModal'
import '@/assets/less/TableExpand.less'
import { formatDate } from '@/utils/util'
import { postAction } from '../../api/manage'

export default {
  name: 'PaLeaveMessageList',
  mixins: [JeecgListMixin],
  components: {
    PaLeaveMessageReplyList,
    PaLeaveMessageModal,
    SelectPatientModal
  },
  data () {
    return {
      description: '留言问题管理页面',
      messageContent: '',
      messageList: [],
      patientId: '',
      patientAvatar: '',
      patientName: '',
      queryParam: {
        name: ''
      },
      // 表头
      columns: [
        {
          title: '用户头像',
          align: 'center',
          dataIndex: 'avatarUrl',
          width: 60,
          scopedSlots: { customRender: 'imgSlot' }
        },
        {
          title: '真实姓名',
          align: 'left',
          dataIndex: 'name',
          scopedSlots: { customRender: 'name' }
        },
        {
          title: '留言时间',
          dataIndex: 'birthday',
          align: 'center',
        }
      ],
      url: {
        list: '/pa/paPatient/listAll',
        delete: '/pa/paLeaveMessage/delete',
        deleteBatch: '/pa/paLeaveMessage/deleteBatch',
        exportXlsUrl: '/pa/paLeaveMessage/exportXls',
        importExcelUrl: 'pa/paLeaveMessage/importExcel',
        messageList: 'pa/paDialogue/listByPaId',
        sendToPatient: 'pa/paDialogue/sendToPatient',
        refresh: '/pa/paPatient/selected'
      },
      dictOptions: {},
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 5,
        pageSizeOptions: ['5', '10', '50'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      selectedMainId: '',
      superFieldList: [],
      needSelect: true
    }
  },
  created () {
    this.getSuperFieldList()
    this.$bus.$on('selectPatient', res => {
      const item = this.dataSource.filter(it => it.id === res)[0]
      this.onSelectChange(item)
      this.needSelect = false
    })
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    getImgView (text) {
      if (text && text.indexOf(",") > 0) {
        text = text.substring(0, text.indexOf(","))
      }
      return getFileAccessHttpUrl(text)
    },
    initDictConfig () {
    },
    onClearSelected () {
      this.selectedRowKeys = []
      this.selectionRows = []
      this.selectedMainId = ''
    },
    onSelectChange (selectionRow) {
      this.refreshPatientList(selectionRow.id)
      this.loadMessageData(selectionRow.id);
      this.patientAvatar = selectionRow.avatarUrl;
      this.patientName = selectionRow.name;
    },
    // 学生列表(左侧聊天列表)
    loadData (arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }

      this.onClearSelected()
      var params = this.queryParam//查询条件
      console.log(params)
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result
          if (this.$route.query.id && this.needSelect) {
            const item = this.dataSource.filter(it => it.id === this.$route.query.id)[0]
            this.onSelectChange(item)
            this.needSelect = false
          }
          // this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    // 根据学生id获取聊天记录
    loadMessageData (patientId) {
      this.patientId = patientId
      // console.log(record);
      // return;
      this.loading = true
      let url = this.url.messageList + '/' + patientId
      getAction(url).then((res) => {
        if (res.success) {
          // console.log(res)
          this.messageList = res.result.records.reverse();
          let messagescroll = document.getElementById('messagescroll')
          this.$nextTick(() => {
            messagescroll.scrollTop = messagescroll.scrollHeight;
            this.loading = false
          })
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    // 发送消息
    sendClick () {
      var params = {
        content: this.messageContent,
        patientId: this.patientId
      }
      this.loading = true
      postAction(this.url.sendToPatient, params).then((res) => {
        if (res.success) {
          this.loadMessageData(this.patientId)
          this.messageContent = ''
          // this.messageList = res.result.records;
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    getSuperFieldList () {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'doctorName', text: '老师名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'patientName', text: '学生姓名', dictCode: '' })
      fieldList.push({ type: 'string', value: 'content', text: '留言内容', dictCode: '' })
      this.superFieldList = fieldList
    },
    newLeaveMessage () {
      this.$refs.SelectPatientModal.visible = true
    },
    handleOK (rows) {
      let value = ''
      let patientNames = ''
      let patientIds = ''
      if (!rows && rows.length <= 0) {
        patientNames = ''
        patientIds = ''
      } else {
        patientNames = rows.map(row => row['name']).join(',')
        patientIds = rows.map(row => row['id']).join(',')
      }

      this.refreshPatientList(patientIds)

      console.log(patientNames, patientIds)
      this.patientId = patientIds;
      this.patientName = patientNames
      this.loadMessageData(patientIds)
    },
    refreshPatientList (patientId) {
      var params = {
        id: patientId
      }
      this.loading = true
      getAction(this.url.refresh, params).then((res) => {
        console.log(res);
        if (res.success) {
          this.loadData();
          // this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    timeChange (time) {
      if (!time) {
        return
      }
      var timeValue = Date.parse(time); // 转换为时间戳
      console.log(time, timeValue);
      // timeValue = timeValue * 1000
      var timeNew = Date.parse(new Date()); //当前时间
      var timeDiffer = timeNew - timeValue; //与当前时间误差
      var returnTime = '';
      if (timeDiffer <= 60000) { //一分钟内
        var returnTime = '刚刚';
      } else if (timeDiffer > 60000 && timeDiffer < 3600000) { //1小时内
        var returnTime = Math.floor(timeDiffer / 60000) + '分钟前';
      } else if (timeDiffer >= 3600000 && timeDiffer < 86400000 && this.isYestday(timeValue) === false) { //今日
        var returnTime = formatDate(timeValue, 'yyyy-MM-dd hh:mm:ss').substr(11, 5);
      } else if (timeDiffer > 3600000 && this.isYestday(timeValue) === true) { //昨天
        var returnTime = '昨天' + formatDate(timeValue, 'yyyy-MM-dd hh:mm:ss').substr(11, 5);
      } else if (timeDiffer > 86400000 && this.isYestday(timeValue) === false && this.isYear(timeValue) === true) { //今年
        var returnTime = formatDate(timeValue, 'yyyy-MM-dd hh:mm:ss').substr(5, 6);
      } else if (timeDiffer > 86400000 && this.isYestday(timeValue) === false && this.isYear(timeValue) === false) { //不属于今年
        var returnTime = formatDate(timeValue, 'yyyy-MM-dd hh:mm:ss').substr(0, 10);
      }
      return returnTime;
    },
    isYestday (timeValue) { // 是否为昨天
      const date = new Date(timeValue)
      const today = new Date()
      if (date.getFullYear() === today.getFullYear() && date.getMonth() === today.getMonth()) {
        if (date.getDate() - today.getDate() === 1) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    },
    isYear (timeValue) { // 是否为今年
      const dateyear = new Date(timeValue).getFullYear()
      const toyear = new Date().getFullYear()
      // console.log(dateyear, toyear)
      if (dateyear === toyear) {
        return true
      } else {
        return false
      }
    }
  }
}
</script>
<style scoped>
.message-list-content {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 70vh;
}

.message-list-content .left-table {
  width: 40%;
  overflow: hidden;
}

.left-table .table-content {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
}

.left-table .table-content .table-item {
  display: flex;
  flex-direction: row;
  height: 65px;
  padding: 5px 0;
  border-bottom: 1px solid #f1f1f1;
  justify-content: space-between;
  padding-right: 10px;
}

.left-table .table-content .table-item .unread-num {
  position: absolute;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  background-color: rgb(233, 41, 40);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2px;
  left: 25px;
  font-size: 12px;
}

.left-table .table-content .table-item .user-avatar {
  width: 40px;
  min-width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
  margin-top: 5px;
}

.left-table .table-content .table-item .user-avatar .img {
  min-width: 40px;
  height: 40px;
}

.left-table .table-content .table-item .user-content {
  margin-left: 10px;
  width: 100%;
}

.left-table .table-content .table-item .user-content .list-message-view {
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  height: 20px;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.left-table .table-content .table-item .content-time {
  min-width: 100px;
  height: 30px;
  display: flex;
  justify-content: end;
}

.message-list-content .message-view {
  width: 60%;
  margin-left: 20px;
  height: 70vh;
  /* background-color: #fafafa; */
  border-radius: 6px;
  padding: 10px;
  position: relative;
}

.message-list-content .message-view .send-bottom {
  position: absolute;
  bottom: 10px;
  width: calc(100% - 20px);
  display: flex;
  flex-direction: row;
}

.send-bottom .bottom-text {
  width: calc(100% - 90px);
}

.send-bottom .send-button {
  margin: auto;
}

.message-list-content .message-view .list-content {
  display: flex;
  width: 100%;
  height: calc(100% - 82px);
  /* max-height: calc(100% - 62px); */
  flex-direction: column;
  background-color: #fbfbfb;
  overflow: auto;
  box-sizing: border-box;
}

.item {
  display: flex;
  width: 100%;
  flex-direction: column;
  padding: 0 15px;
}
.item-left {
  align-items: flex-start;
}
.item-right {
  align-items: flex-end;
}

.part {
  color: #8f9399;
  height: 10px;
}
.replayContent {
  display: flex;
  flex-direction: column;
  padding: 0 5px;
  flex-direction: row;
}

.replayContent .user {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.replayContent .user .avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
}

.replayContent .user .name {
  font-size: 12px;
}

.right {
  text-align: right;
  flex-direction: row-reverse;
}

.item .time {
  display: flex;
  width: 100%;
  height: 33px;
  font-size: 10px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #8f9399;
  line-height: 17px;
  align-items: center;
  justify-content: center;
}

.detail {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #606266;
  line-height: 20px;
  margin: 10px 0;
}

.label-item {
  text-align: left;
}

.ant-tag {
  margin-bottom: 8px !important;
}

.user-content {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 15px;
  font-weight: 500;
}

.list-message-view {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.chatBox {
  position: relative;
  padding: 0 15px;
  word-break: break-all;
  border-radius: 5px;
  max-width: 280px;
  height: fit-content;
}

.chatBox-left {
  margin-left: 20px;
  background: #ffffff;
}
.chatBox-left::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  left: -20px;
  top: 7px;
  border: 10px solid;
  border-color: transparent #fff transparent transparent;
}

.chatBox-right {
  margin-right: 20px;
  background: #1890ff;
  color: #fff;
}

.chatBox-right .detail {
  color: #fff;
}
.chatBox-right::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  right: -14px;
  top: 7px;
  border: 7px solid;
  border-color: transparent transparent transparent #1890ff;
}

@import '~@assets/less/common.less';
</style>