<template>
  <a-card :bordered="false" :class="'cust-erp-sub-tab'">
    <!-- 操作按钮区域 -->
    <div class="table-operator" v-if="mainId">
      <a-button @click="handleAdd" type="primary" icon="plus">回复</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange">
        <span slot="type" slot-scope="text, record">
          <a-tag v-if="record.type=='1'" color="green">问</a-tag>
          <a-tag v-if="record.type=='2'" color="blue">答</a-tag>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <paLeaveMessageReply-modal ref="modalForm" @ok="modalFormOk" :mainId="mainId"></paLeaveMessageReply-modal>
  </a-card>
</template>

<script>

import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PaLeaveMessageReplyModal from './modules/PaLeaveMessageReplyModal'

export default {
  name: 'PaLeaveMessageReplyList',
  mixins: [JeecgListMixin],
  components: { PaLeaveMessageReplyModal },
  props: {
    mainId: {
      type: String,
      default: '',
      required: false
    }
  },
  watch: {
    mainId: {
      immediate: true,
      handler (val) {
        console.log(this.mainId)
        if (!this.mainId) {
          this.clearList()
        } else {
          this.queryParam['leaveMessageId'] = val
          this.loadData(1)
        }
      }
    }
  },
  data () {
    return {
      description: '留言问题管理页面',
      disableMixinCreated: true,
      // 表头
      columns: [
        {
          title: '类型',
          align: 'center',
          dataIndex: 'type',
          scopedSlots: { customRender: 'type' }
        },
        {
          title: '内容',
          align: 'center',
          dataIndex: 'content'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/pa/paLeaveMessage/listPaLeaveMessageReplyByMainId',
        delete: '/pa/paLeaveMessage/deletePaLeaveMessageReply',
        deleteBatch: '/pa/paLeaveMessage/deleteBatchPaLeaveMessageReply',
        exportXlsUrl: '/pa/paLeaveMessage/exportPaLeaveMessageReply',
        importUrl: '/pa/paLeaveMessage/importPaLeaveMessageReply'
      },
      dictOptions: {},
      superFieldList: []
    }
  },
  created () {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl () {
      return `${window._CONFIG['domianURL']}/${this.url.importUrl}/${this.mainId}`
    }
  },
  methods: {
    clearList () {
      this.dataSource = []
      this.selectedRowKeys = []
      this.ipagination.current = 1
    },
    getSuperFieldList () {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'doctorName', text: '老师名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'patientName', text: '学生姓名', dictCode: '' })
      fieldList.push({ type: 'string', value: 'content', text: '留言内容', dictCode: '' })
      this.superFieldList = fieldList
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
