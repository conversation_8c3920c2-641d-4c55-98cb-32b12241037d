<template>
  <div class="patient-detail">
    <div class="patient-detail-info">
      <p class="title">
        <span>详情信息</span>
        <a-button type="primary" @click="goBack">返回</a-button>
      </p>
      <div class="content">
        <img class="content-img" v-if="patientDetail.avatarUrl" :src="getImgView(patientDetail.avatarUrl)" />
        <img class="content-img" v-else src="../../assets/img/avatar.png" />
        <div class="content-main">
          <div class="main-name">
            <span class="name">{{ patientDetail.name }}</span>
            <div class="label">
              <a-tag color="#FFAA00" v-for="item in patientDetail.labels" :key="item.labelId">
                {{ item.labelName }}
              </a-tag>
            </div>
          </div>

          <div class="main-info">
            <a-row>
              <a-col :span="6">性别：{{ patientDetail.sex_dictText }}</a-col>
              <a-col :span="6">年龄：{{ patientDetail.age }}</a-col>
              <a-col :span="6">联系方式：{{ patientDetail.telphone }}</a-col>
              <a-col :span="6">身高：{{ patientDetail.stature }}cm</a-col>
            </a-row>
            <a-row>
              <a-col :span="6">体重：{{ patientDetail.weight }}kg</a-col>
              <a-col :span="6">生日：{{ patientDetail.birthday }}</a-col>
              <a-col :span="6">编号：{{ patientDetail.number }}</a-col>
              <!-- <a-col :span="6">文化程度：{{ patientDetail.cultural_dictText }}</a-col> -->
              <!-- <a-col :span="6">职业：{{ patientDetail.profession }}</a-col> -->
            </a-row>
            <!-- <a-row>
              <a-col :span="6">婚否：{{ patientDetail.maritalStatus_dictText }}</a-col>
              <a-col :span="6">身份证号：{{ patientDetail.idCard }}</a-col>
              <a-col :span="6">民族：{{ patientDetail.nationality_dictText }}</a-col>
              <a-col :span="6">昵称：{{ patientDetail.nickname }}</a-col>
            </a-row> -->
            <a-row>
              <!-- <a-col :span="6">编号：{{ patientDetail.number }}</a-col> -->
              <!-- <a-col :span="6">是否服用药物：{{ patientDetail.isMedicine_dictText }}</a-col> -->
              <!-- <a-col :span="6">是否开启日志提醒：{{ patientDetail.isSleepDiaryRemind_dictText }}</a-col> -->
            </a-row>
            <!-- <a-row>
              <a-col :span="23">主诉：{{ patientDetail.principleAction }}</a-col>
            </a-row>
            <a-row>
              <a-col :span="6">上床时间：{{ patientDetail.goBedTime }}</a-col>
              <a-col :span="6">起床时间：{{ patientDetail.wakeUpTime }}</a-col>
            </a-row> -->
          </div>
        </div>
      </div>
    </div>

    <div class="patient-detail-content">
      <a-tabs v-model="chooseTab">
        <a-tab-pane :key="1" tab="心理日记">
          <pa-sleep-diary :id="patientDetail.id"></pa-sleep-diary>
        </a-tab-pane>
        <a-tab-pane :key="2" tab="干预记录">
          <pa-report-management></pa-report-management>
        </a-tab-pane>
        <a-tab-pane :key="5" tab="预约记录">
          <pa-reserve-record :id="patientDetail.id"></pa-reserve-record>
        </a-tab-pane>
        <a-tab-pane :key="3" tab="筛查记录">
          <dx-result-list :isOnly="true" :isShow="false" />
        </a-tab-pane>
        <!-- <a-tab-pane :key="7" tab="睡眠处方">
          <pa-report-prescription :id="patientDetail.id"></pa-report-prescription>
        </a-tab-pane> -->
        <a-tab-pane :key="4" tab="学生记录">
          <pa-report-parient-report :id="patientDetail.id"></pa-report-parient-report>
        </a-tab-pane>
        <a-tab-pane :key="9" tab="整体报告">
          <pa-program-report></pa-program-report>
        </a-tab-pane>
        <!-- <a-tab-pane :key="6" tab="测评计划">
          <pa-follow-plan :patientDetail="patientDetail"></pa-follow-plan>
        </a-tab-pane> -->
        <!-- <a-tab-pane :key="8" tab="训练记录">
          <pa-doctor-record :id="patientDetail.id"></pa-doctor-record>
        </a-tab-pane> -->
      </a-tabs>
    </div>
  </div>
</template>

<script>
import PaSleepDiary from './modules/PaSleepDiary.vue'
import PaReportPrescription from './modules/PaReportPrescription.vue'
import PaProgramManagement from './modules/PaProgramManagement.vue'
import PaReportPatientReport from './modules/PaReportPatientReport.vue'
import DxResultList from '../diagnosis/DxResultList.vue'
import PaReserveRecord from './modules/PaReserveRecord.vue'
import PaFollowPlan from './modules/PaFollowPlan.vue'
import PaDoctorRecord from './modules/PaDoctorRecord.vue'
import PaProgramReport from './modules/PaProgramReport.vue'
import { getFileAccessHttpUrl } from '@/api/manage'
export default {
  name: 'pa-patient-detail',

  components: {
    PaProgramReport,
    [PaSleepDiary.name]: PaSleepDiary,
    [PaReportPrescription.name]: PaReportPrescription,
    [PaProgramManagement.name]: PaProgramManagement,
    [PaReportPatientReport.name]: PaReportPatientReport,
    DxResultList,
    PaReserveRecord,
    PaFollowPlan,
    PaDoctorRecord
  },

  data () {
    return {
      patientDetail: JSON.parse(localStorage.getItem("PatientDetail")),
      chooseTab: 1,
    }
  },

  created () {
    this.$store.dispatch('PatientDetail', JSON.parse(localStorage.getItem("PatientDetail")))
    this.chooseTab = Number(this.$route.query.chooseTab) || 1
  },

  methods: {
    goBack () {
      this.$router.go(-1)
    },

    getImgView (text) {
      if (text && text.indexOf(",") > 0) {
        text = text.substring(0, text.indexOf(","))
      }
      return getFileAccessHttpUrl(text)
    }
  }
}
</script>

<style lang="less">
.patient-detail {
  width: 100%;

  .patient-detail-info {
    width: 100%;
    padding: 20px;
    background: #fff;
    border-radius: 12px;

    .title {
      display: flex;
      justify-content: space-between;
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }

    .content {
      display: flex;

      .content-img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }

      .content-main {
        padding-left: 20px;
        flex: 1;

        .main-name {
          display: flex;
          align-items: center;

          .name {
            font-size: 16px;
            font-weight: bold;
            padding-right: 15px;
          }
        }

        .main-info {
          .ant-row {
            padding-top: 10px;
          }
        }
      }
    }
  }

  .patient-detail-content {
    min-height: 400px;
    margin-top: 10px;
    background: #fff;
    border-radius: 12px;
  }

  .ant-card-body {
    padding-top: 0;
  }
}
</style>
