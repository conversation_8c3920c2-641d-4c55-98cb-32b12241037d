<template>
  <a-card :bordered="false">
    <a-tabs type="card" v-model="chooseTab" @change="handleChange">
      <a-tab-pane :key="1" tab="预约总览">
        <PaAppointmentOverviewNew ref="pane1" @setTab="setTab"></PaAppointmentOverviewNew>
      </a-tab-pane>

      <a-tab-pane :key="2" tab="预约列表">
        <PaConsultAppointmentList2 ref="pane2"></PaConsultAppointmentList2>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import PaConsultAppointmentList2 from './modules/PaConsultAppointmentList2'
import PaAppointmentOverviewNew from './modules/PaAppointmentOverviewNew'

export default {
  name: "PaConsultAppointmentList",
  components: {
    PaConsultAppointmentList2,
    PaAppointmentOverviewNew
  },
  data () {
    return {
      chooseTab: 1,
    }
  },
  methods: {
    handleChange() {
      if (this.chooseTab === 1) {        
        this.$nextTick(() => {
          this.$refs.pane1.loadData()
        })
      }
      if (this.chooseTab === 2) {        
        this.$nextTick(() => {
          this.$refs.pane2.queryParam.id = ''
          this.$refs.pane2.loadData()
        })
      }
    },
    setTab(item) {
      this.chooseTab = 2
      this.$nextTick(() => {
        this.$refs.pane2.loadData(1, item.id)
      })
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>