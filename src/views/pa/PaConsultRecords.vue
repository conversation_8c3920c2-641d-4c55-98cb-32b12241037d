<template>
  <a-card :bordered="false">

    <!-- 查询区域 姓名、电话、咨询时间（范围查询）、关注等级、结案状态 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学生姓名">
              <j-input placeholder="请输入学生姓名" v-model="queryParam.patientName"></j-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="电话">
              <a-input placeholder="请输入电话" v-model="queryParam.phone"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="咨询时间">
              <a-range-picker
                style="width: 100%"
                v-model="queryParam.createTimeRange"
                format="YYYY-MM-DD"
                :placeholder="['开始时间', '结束时间']"
                @change="onDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="关注等级">
              <a-input placeholder="请输入关注等级" v-model="queryParam.level"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="结案状态">
              <a-input placeholder="请输入结案状态" v-model="queryParam.status"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange">
        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <PaConsultRecordsModal ref="modalForm" @ok="modalFormOk"></PaConsultRecordsModal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import PaConsultRecordsModal from './modules/PaConsultRecordsModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: "PaConsultAppointmentList",
  mixins: [JeecgListMixin],
  components: {
    PaConsultRecordsModal
  },
  data () {
    return {
      description: '咨询记录',
      // 表头 来访者基本信息（姓名、性别、电话、年龄），咨询时间，咨询主诉，本次咨询要点，诊断评估，关注等级，问题类型，结案状态
      // 姓名：patientName、性别:1-男，2-女：sex、电话:phone、年龄:age），咨询时间:consultTime，咨询主诉:question，本次咨询要点：consultPoint，
      // 诊断评估：assessment，关注等级:level，问题类型:questionType，结案状态：status
      columns: [
        {
          title: '学生姓名',
          align: "center",
          dataIndex: 'patientName'
        },
        {
          title: '性别',
          align: "center",
          dataIndex: 'sex',
          customRender: function (text) {
            if (text == 1) {
              return '男'
            } else {
              return '女'
            }
          }
        },
        {
          title: '电话',
          align: "center",
          dataIndex: 'phone'
        },
        {
          title: '年龄',
          align: "center",
          dataIndex: 'age'
        },
        {
          title: '咨询时间',
          align: "center",
          dataIndex: 'consultTime'
        },
        {
          title: '咨询主诉',
          align: "center",
          dataIndex: 'question'
        },
        {
          title: '本次咨询要点',
          align: "center",
          dataIndex: 'consultPoint'
        },
        {
          title: '诊断评估',
          align: "center",
          dataIndex: 'assessment'
        },
        {
          title: '关注等级',
          align: "center",
          dataIndex: 'level'
        },
        {
          title: '问题类型',
          align: "center",
          dataIndex: 'questionType'
        },
        {
          title: '结案状态',
          align: "center",
          dataIndex: 'status'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' },
        }
      ],
      url: {
        list: "/pa/consultRecord/list",
        delete: "/pa/consultRecord/delete",
        deleteBatch: "/pa/consultRecord/deleteBatch",
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  methods: {
    onDateChange: function(value, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
      // let that = this
      // let httpurl = that.url.getMeasureNumStatistics
      // getAction(httpurl, { startDate: dateString[0], endDate: dateString[1] }).then((res) => {
      //   if (res.success) {
      //     that.measureList = res.result
      //   } else {
      //     that.$message.warning(res.message)
      //   }
      // }).finally(() => {
      // })
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>