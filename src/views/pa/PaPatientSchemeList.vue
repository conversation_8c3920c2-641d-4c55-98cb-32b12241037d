<template>
  <a-row :gutter="10">
    <a-col :md="leftColMd" :sm="24" style="margin-bottom: 20px">
      <a-card :bordered="false">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :lg="6" :sm="8">
                <a-form-item label="方案名称">
                  <a-input placeholder="请输入方案名称" v-model="queryParam.name"></a-input>
                </a-form-item>
              </a-col>
              <a-col :lg="6" :sm="8">
                <a-form-item label="学生名称">
                  <a-input placeholder="请输入学生名称" v-model="queryParam.patientName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="8">
                <a-form-item label="日期">
                  <a-range-picker valueFormat="YYYY-MM-DD" v-model="queryParam.timeArray" style="width: 100%" @change='changeDateQuery'></a-range-picker>
                </a-form-item>
              </a-col>
              <a-col :md="4" :sm="8">
                <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
          <!--          <a-button type="primary" icon="download" @click="handleExportXls('学生方案')">导出</a-button>-->
          <!--          <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader"-->
          <!--                    :action="importExcelUrl"-->
          <!--                    @change="handleImportExcel">-->
          <!--            <a-button type="primary" icon="import">导入</a-button>-->
          <!--          </a-upload>-->
          <!-- 高级查询区域 -->
          <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel">
                <a-icon type="delete" />
                删除
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <a-table ref="table" size="middle" bordered rowKey="id" class="j-table-force-nowrap" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange">
            <span slot="action" slot-scope="text, record">
              <a @click="handleEdit(record)">查看</a>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
                <a-menu slot="overlay">
                  <!-- <a-menu-item>
                    <a @click="handleOpen(record)">每日方案</a>
                  </a-menu-item> -->
                  <a-menu-item>
                    <a @click="handleDetail(record)">详情</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                      <a>删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </div>
        <pa-patient-scheme-modal ref="modalForm" @ok="modalFormOk" />
      </a-card>
    </a-col>
    <a-col :md="rightColMd" :sm="24" v-if="rightcolval == 1">
      <a-card :bordered="false">
        <div class="table-operator" style="display: flex;justify-content: flex-end;">
          <a-button @click="rightcolval = 0,paPatientSchemeId=''">关闭</a-button>
        </div>

        <a-tabs :default-active-key="1" @change="handleTabChange">
          <a-tab-pane :key="1" tab="课程">
            <a-table ref="table" size="middle" bordered rowKey="id" class="j-table-force-nowrap" :columns="columns1" :dataSource="dataSource1" :pagination="ipagination1" @change="handleTableChange1" :loading="loading1">
              <template slot="status" slot-scope="text">
                {{ text ===1 ? '已完成' : text === 0 ? '未完成' : text === 2 ?'进行中' : '已超期' }}
              </template>
              <template slot="treatmentCondition" slot-scope="text, record">
                <editable-cell :text="text" @change="onCellChange(record.id, 'treatmentCondition', $event)" />
              </template>
            </a-table>
          </a-tab-pane>

          <a-tab-pane :key="2" tab="放松训练">
            <a-table ref="table" size="middle" bordered rowKey="id" class="j-table-force-nowrap" :columns="columns2" :dataSource="dataSource2" :pagination="ipagination2" @change="handleTableChange2" :loading="loading2">
              <template slot="status" slot-scope="text">
                {{ text ===1 ? '已完成' : text === 0 ? '未完成' : text === 2 ?'进行中' : '已超期' }}
              </template>
              <template slot="treatmentCondition" slot-scope="text, record">
                <editable-cell :text="text" @change="onCellChange(record.id, 'treatmentCondition', $event)" />
              </template>
            </a-table>
          </a-tab-pane>

          <a-tab-pane :key="3" tab="量表评估">
            <a-table ref="table" size="middle" bordered rowKey="id" class="j-table-force-nowrap" :columns="columns3" :dataSource="dataSource3" :pagination="ipagination3" @change="handleTableChange3" :loading="loading3">
              <template slot="status" slot-scope="text">
                {{ text ===1 ? '已完成' : text === 0 ? '未完成' : text === 2 ?'进行中' : '已超期' }}
              </template>
              <template slot="treatmentCondition" slot-scope="text, record">
                <editable-cell :text="text" @change="onCellChange(record.id, 'treatmentCondition', $event)" />
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>

const EditableCell = {
  template: `
      <div class="editable-cell">
        <div v-if="editable">
          <j-dict-select-tag style="width: 150px;" @change="handleChange" @pressEnter="check" v-model="value" placeholder="请选择治疗情况"
                                 dictCode="treatment_condition"/>
          <a-icon
            type="check"
            @click="check"
          />
        </div>
        <div v-else class="editable-cell-text-wrapper">
          {{ transform(value) || ' ' }}
          <a-icon type="edit" class="editable-cell-icon" @click="edit" />
        </div>
      </div>
    `,
  props: {
    text: Number
  },
  data () {
    return {
      value: this.text,
      editable: false
    }
  },
  methods: {
    handleChange (value) {
      this.value = value
    },
    check () {
      this.editable = false
      this.$emit('change', this.value)
    },
    edit () {
      this.editable = true
    },
    transform (value) {
      if (value == 1) {
        return '很好'
      } else if (value == 2) {
        return '好'
      } else if (value == 3) {
        return '一般'
      } else if (value == 4) {
        return '差'
      } else if (value == 5) {
        return '很差'
      }
    }
  }
}

import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PaPatientSchemeModal from './modules/PaPatientSchemeModal'
import '@/assets/less/TableExpand.less'
import { getAction, putAction } from '@/api/manage'

export default {
  name: 'PaPatientSchemeList',
  mixins: [JeecgListMixin],
  components: {
    PaPatientSchemeModal,
    EditableCell
  },
  data () {
    return {
      description: '学生方案管理页面',
      // 表头
      columns: [
        {
          title: '方案名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '老师名称',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '学生名称',
          align: 'center',
          dataIndex: 'patientName'
        },
        {
          title: '治疗天数',
          align: 'center',
          dataIndex: 'dayNum'
        },
        {
          title: '开始时间',
          align: 'center',
          dataIndex: 'startDate',
          customRender: function (text) {
            return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
          }
        },
        {
          title: '结束时间',
          align: 'center',
          dataIndex: 'endDate',
          customRender: function (text) {
            return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],

      columns1: [
        {
          title: '日期',
          align: 'center',
          dataIndex: 'recordTime'
        },
        {
          title: '分类',
          align: 'center',
          dataIndex: 'materialCategoryName'
        },
        {
          title: '治疗内容',
          align: 'center',
          dataIndex: 'materialTitle'
        },
        {
          title: '完成状态',
          align: 'center',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '治疗情况',
          align: 'center',
          dataIndex: 'treatmentCondition',
          width: '40%',
          scopedSlots: { customRender: 'treatmentCondition' }
        }
      ],
      loading1: false,
      dataSource1: [],
      ipagination1: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },

      columns2: [
        {
          title: '日期',
          align: 'center',
          dataIndex: 'recordTime'
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'trainTitle'
        },
        {
          title: '完成状态',
          align: 'center',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '治疗情况',
          align: 'center',
          dataIndex: 'treatmentCondition',
          width: '40%',
          scopedSlots: { customRender: 'treatmentCondition' }
        }
      ],
      loading2: false,
      dataSource2: [],
      ipagination2: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },

      columns3: [
        {
          title: '日期',
          align: 'center',
          dataIndex: 'limitDate'
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'measureName'
        },
        {
          title: '完成状态',
          align: 'center',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '治疗情况',
          align: 'center',
          dataIndex: 'treatmentCondition',
          width: '40%',
          scopedSlots: { customRender: 'treatmentCondition' }
        }
      ],
      loading3: false,
      dataSource3: [],
      ipagination3: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },

      paPatientSchemeId: '',
      url: {
        list: '/pa/paPatientSchemeGroup/list',
        sublist: '/pa/paPatientScheme/sublist',
        measurelist: '/pa/paPatientScheme/measurelist',
        trainlist: '/pa/paPatientScheme/trainlist',
        editSub: '/pa/paPatientScheme/editSub',
        delete: '/pa/paPatientSchemeGroup/delete',
        deleteBatch: '/pa/paPatientSchemeGroup/deleteBatch',
        exportXlsUrl: '/pa/paPatientScheme/exportXls',
        importExcelUrl: 'pa/paPatientScheme/importExcel',
        patientDetail: '/api/doctor/patient/queryById'
      },
      dictOptions: {},
      superFieldList: [],
      rightcolval: 0,
      activeKey: 1
    }
  },
  created () {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    leftColMd () {
      return this.paPatientSchemeId == '' ? 24 : 14
    },
    rightColMd () {
      return this.paPatientSchemeId == '' ? 0 : 10
    }
  },
  methods: {
    initDictConfig () {
    },
    changeDateQuery () {
      if (this.queryParam.timeArray) {
        this.queryParam.startDate = this.queryParam.timeArray[0]
        this.queryParam.endDate = this.queryParam.timeArray[1]
      } else {
        this.queryParam.startDate = ''
        this.queryParam.endDate = ''
      }
    },
    onCellChange (key, dataIndex, value) {
      this.updateSchemeSub(key, value)
      if (this.activeKey === 1) {
        const dataSource = [...this.dataSource1]
        const target = dataSource.find(item => item.key === key)
        if (target) {
          target[dataIndex] = value
          this.dataSource1 = dataSource
        }
      } else if (this.activeKey === 2) {
        const dataSource = [...this.dataSource2]
        const target = dataSource.find(item => item.key === key)
        if (target) {
          target[dataIndex] = value
          this.dataSource2 = dataSource
        }
      } else if (this.activeKey === 3) {
        const dataSource = [...this.dataSource3]
        const target = dataSource.find(item => item.key === key)
        if (target) {
          target[dataIndex] = value
          this.dataSource3 = dataSource
        }
      }
    },
    getSuperFieldList () {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'name', text: '方案名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'doctorName', text: '老师名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'patientName', text: '学生名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'schemeName', text: '方案名称', dictCode: '' })
      fieldList.push({ type: 'int', value: 'dayNum', text: '治疗天数', dictCode: '' })
      fieldList.push({ type: 'date', value: 'startDate', text: '开始时间' })
      fieldList.push({ type: 'date', value: 'endDate', text: '结束时间' })
      this.superFieldList = fieldList
    },
    handleOpen (record) {
      this.rightcolval = 1
      this.paPatientSchemeId = record.id
      this.loadData1()
    },
    handleDetail (record) {
      getAction(this.url.patientDetail, { id: record.patientId }).then(res => {
        localStorage.setItem('PatientDetail', JSON.stringify(res.result))
        this.$store.dispatch('PatientDetail', res.result)
        this.$router.push('/pa/PaPatientDetail?chooseTab=2&schemeId=' + record.id)
      })
    },
    loadData1 () {
      let param = {}
      let url = ''
      if (this.activeKey === 1) {
        this.loading1 = true
        url = this.url.sublist
      } else if (this.activeKey === 2) {
        this.loading2 = true
        url = this.url.trainlist
      } else if (this.activeKey === 3) {
        this.loading3 = true
        url = this.url.measurelist
      }

      if (this.paPatientSchemeId === '') return
      let params = {}//查询条件
      params.paPatientSchemeId = this.paPatientSchemeId
      params.pageNo = this.ipagination1.current;
      params.pageSize = this.ipagination1.pageSize;
      getAction(url, params).then((res) => {
        if (res.success) {
          if (this.activeKey === 1) {
            this.dataSource1 = res.result.records
            this.ipagination1.total = res.result.total || 0
          } else if (this.activeKey === 2) {
            this.dataSource2 = res.result.records
            this.ipagination2.total = res.result.total || 0
          } else if (this.activeKey === 3) {
            this.dataSource3 = res.result.records
            this.ipagination3.total = res.result.total || 0
          }
        }
      }).finally(() => {
        this.loading1 = false
        this.loading2 = false
        this.loading3 = false
      })
    },
    updateSchemeSub (id, status) {
      let params = {}
      params.id = id
      params.treatmentCondition = status
      putAction(this.url.editSub, params).then((res) => {

      })
    },
    handleTabChange (key) {
      this.activeKey = Number(key)
      this.loadData1()
    },
    handleTableChange1 (pagination, filters, sorter) {
      this.ipagination1 = pagination;
      this.loadData1();
    },
    handleTableChange2 (pagination, filters, sorter) {
      this.ipagination2 = pagination;
      this.loadData1();
    },
    handleTableChange3 (pagination, filters, sorter) {
      this.ipagination3 = pagination;
      this.loadData1();
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>