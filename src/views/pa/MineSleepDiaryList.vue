<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button v-has="'mine:sleepDiary:add'" @click="handleAdd" type="primary" icon="plus">新增</a-button>
<!--      <a-button type="primary" icon="download" @click="handleExportXls('睡眠日记主表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      &lt;!&ndash; 高级查询区域 &ndash;&gt;
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>-->
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <pa-sleep-diary-modal ref="modalForm" @ok="modalFormOk"></pa-sleep-diary-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import PaSleepDiaryModal from './modules/PaSleepDiaryModal'
  import { USER_INFO } from "@/store/mutation-types"
  import { getAction } from '@/api/manage'
  import Vue from 'vue'

  export default {
    name: 'MineSleepDiaryList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      PaSleepDiaryModal
    },
    data () {
      return {
        description: '睡眠日记主表管理页面',
        // 表头
        columns: [
          {
            title:'日期',
            align:"center",
            dataIndex: 'recordTime',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },
          {
            title:'学生名称',
            align:"center",
            dataIndex: 'patientName'
          },
          {
            title:'昨晚准备睡觉时间',
            align:"center",
            dataIndex: 'nightPrepareSleepDate'
          },
          {
            title:'昨晚睡觉时间',
            align:"center",
            dataIndex: 'nightAsleepDate'
          },
          {
            title:'醒来时间',
            align:"center",
            dataIndex: 'wakeDate'
          },
          {
            title:'起床时间',
            align:"center",
            dataIndex: 'getUpDate'
          },
          {
            title:'夜醒次数',
            align:"center",
            dataIndex: 'nightWakeTimes'
          },
          {
            title:'夜醒总时长（分钟）',
            align:"center",
            dataIndex: 'nightWakeTotalTime'
          },
          {
            title:'是否小睡',
            align:"center",
            dataIndex: 'isNap_dictText'
          },
          {
            title:'早晨感觉如何',
            align:"center",
            dataIndex: 'mornFeelType_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/pa/paSleepDiary/list",
          delete: "/pa/paSleepDiary/delete",
          deleteBatch: "/pa/paSleepDiary/deleteBatch",
          exportXlsUrl: "/pa/paSleepDiary/exportXls",
          importExcelUrl: "pa/paSleepDiary/importExcel",

        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      loadData(arg) {
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1;
        }
        var params = this.getQueryParams();//查询条件
        params.patientId = Vue.ls.get(USER_INFO).patientId
        this.loading = true;
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
            this.dataSource = res.result.records||res.result;
            if(res.result.total)
            {
              this.ipagination.total = res.result.total;
            }else{
              this.ipagination.total = 0;
            }
            //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          }
          if(res.code===510){
            this.$message.warning(res.message)
          }
          this.loading = false;
        })
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'date',value:'recordTime',text:'睡眠日记日期'})
        fieldList.push({type:'string',value:'patientId',text:'学生id'})
        fieldList.push({type:'string',value:'patientName',text:'学生名称'})
        fieldList.push({type:'date',value:'nightPrepareSleepDate',text:'昨晚准备睡觉时间'})
        fieldList.push({type:'date',value:'nightAsleepDate',text:'昨晚睡觉时间'})
        fieldList.push({type:'date',value:'wakeDate',text:'醒来时间'})
        fieldList.push({type:'date',value:'getUpDate',text:'起床时间'})
        fieldList.push({type:'int',value:'nightWakeTimes',text:'夜醒次数'})
        fieldList.push({type:'int',value:'nightWakeTotalTime',text:'夜醒总时长（分钟）'})
        fieldList.push({type:'int',value:'isNap',text:'是否小睡'})
        fieldList.push({type:'int',value:'mornFeelType',text:'早晨感觉如何（1_十分差，2_较差，3_正常，4_较好，5_超级棒）'})
        fieldList.push({type:'string',value:'remark',text:'备注'})
        fieldList.push({type:'int',value:'delFlag',text:'删除状态（0正常，1已删除）'})
        fieldList.push({type:'string',value:'tenantId',text:'租户id'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>