<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="老师名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorName">
              <a-input v-model="model.doctorName" disabled :defaultValue="nickname()" placeholder="请输入老师名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学生名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
              <j-select-patient v-model="model.patientName" @back="backPatientInfo" :backInfo="true" :isRadio="true" disabled></j-select-patient>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="完成时限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitDate">
              <a-date-picker format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" :show-time="{ format: 'HH:mm' }" v-model="model.limitDate" placeholder="请选择完成时限" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
              <a-input v-model="model.title" placeholder="请输入标题"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="任务内容" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content">
              <a-textarea :rows="5" v-model="model.content" placeholder="请输入任务内容"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction } from '@/api/manage'
import { mapGetters } from 'vuex'

export default {
  name: 'BaseMaterialForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        limitDate: [{ required: true, message: '请选择完成时限!' }],
        title: [{ required: true, message: '请输入标题!' }],
        content: [{ required: true, message: '请输入任务内容!' }]
      },
      url: {
        add: '/ta/taDoctorAdvice/add',
        edit: '/ta/taDoctorAdvice/edit',
        queryById: '/base/baseMaterial/queryById'
      },
      patientDetail: JSON.parse(localStorage.getItem("PatientDetail")),
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    add () {
      this.edit(this.modelDefault)
    },
    edit (record) {
      this.model = Object.assign({}, record)
      this.model.patientId = this.patientDetail.id
      this.model.patientName = this.patientDetail.name
      this.visible = true
    },
    backPatientInfo (info) {
      this.model.patientId = info[0].value
      this.model.patientName = info[0].text
    },
    submitForm () {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    }
  }
}
</script>