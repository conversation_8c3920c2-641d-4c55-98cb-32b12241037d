<template>
  <j-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" switchFullscreen @ok="handleOk" @cancel="handleCancel" cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">

        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientId" label="学生id">
          <a-input placeholder="请输入学生id" v-model="model.patientId" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName" label="学生名称">
          <a-input placeholder="请输入学生名称" v-model="model.patientName" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientPhone" label="学生联系方式">
          <a-input placeholder="请输入学生联系方式" v-model="model.patientPhone" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="parentName" label="家长名称">
          <a-input placeholder="请输入家长名称" v-model="model.parentName" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="parentPhone" label="家长联系方式">
          <a-input placeholder="请输入家长联系方式" v-model="model.parentPhone" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="relation" label="家长与学生之间关系">
          <a-input placeholder="请输入家长与学生之间关系" v-model="model.relation" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="startTime" label="回访开始日期">
          <a-date-picker showTime valueFormat='YYYY-MM-DD HH:mm:ss' v-model="model.startTime" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="endTime" label="回访结束日期">
          <a-date-picker showTime valueFormat='YYYY-MM-DD HH:mm:ss' v-model="model.endTime" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type" label="类型（初访、回访）">
          <a-input placeholder="请输入类型（初访、回访）" v-model="model.type" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title" label="回访标题">
          <a-input placeholder="请输入回访标题" v-model="model.title" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reasion" label="回访原因">
          <a-input placeholder="请输入回访原因" v-model="model.reasion" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="note" label="回访备注与建议">
          <a-input placeholder="请输入回访备注与建议" v-model="model.note" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorId" label="老师id">
          <a-input placeholder="请输入老师id" v-model="model.doctorId" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorName" label="老师名称">
          <a-input placeholder="请输入老师名称" v-model="model.doctorName" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="delFlag" label="删除状态（0正常，1已删除）">
          <a-input placeholder="请输入删除状态（0正常，1已删除）" v-model="model.delFlag" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tenantId" label="租户id">
          <a-input placeholder="请输入租户id" v-model="model.tenantId" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sysOrgCode" label="组织机构编码">
          <a-input placeholder="请输入组织机构编码" v-model="model.sysOrgCode" />
        </a-form-model-item>

      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import moment from "moment"

export default {
  name: "PaReturnVisitModal",
  data () {
    return {
      title: "操作",
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      validatorRules: {
      },
      url: {
        add: "/pa/paReturnVisit/add",
        edit: "/pa/paReturnVisit/edit",
      },
    }
  },
  created () {
  },
  methods: {
    add () {
      //初始化默认值
      this.edit({});
    },
    edit (record) {
      this.model = Object.assign({}, record);
      this.visible = true;
    },
    close () {
      this.$emit('close');
      this.visible = false;
      this.$refs.form.clearValidate();
    },
    handleOk () {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true;
          let httpurl = '';
          let method = '';
          if (!this.model.id) {
            httpurl += this.url.add;
            method = 'post';
          } else {
            httpurl += this.url.edit;
            method = 'put';
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          })
        } else {
          return false;
        }
      })
    },
    handleCancel () {
      this.close()
    },


  }
}
</script>

<style lang="less" scoped>
</style>