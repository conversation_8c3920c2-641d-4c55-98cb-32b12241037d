<template>
  <div class="pa-sleep-diary-detail">
    <div class="chart-legend">
      <div class="item">
        <div class="item-legend legend1"></div>
        <div class="item-text">睡眠</div>
      </div>
      <div class="item">
        <div class="item-legend legend5"></div>
        <div class="item-text">小睡</div>
      </div>
      <div class="item">
        <div class="item-legend legend2"></div>
        <div class="item-text">清醒</div>
      </div>
      <div class="item">
        <div class="item-legend legend3">—</div>
        <div class="item-text">卧床</div>
      </div>
      <div class="item">
        <div class="item-legend legend4"></div>
        <div class="item-text">睡眠处方</div>
      </div>
      <div class="item">
        <div class="item-info">M</div>
        <div class="item-text">服用药物</div>
      </div>
    </div>

    <a-table
      ref="table"
      :columns="columns"
      :data-source="dataSource"
      bordered
      size="middle"
      rowKey="id"
      :scroll="{ x: 2200 }"
      :pagination="ipagination"
      @change="handleTableChange"
      :loading="loading">

      <div class="date" slot="date" slot-scope="scope">
        <p class="text">{{ scope.recordTime }}</p>
        <p class="text">{{ getWeek(scope.recordTime) }}</p>

        <span class="sleep-item" v-for="(item, index) in scope.sleepList" :key="index + 'sleep'" :style="{'left': item.left + 120 + 'px', 'width': item.width + 'px'}" @click="handleDetail(scope)"></span>
        <span class="nap-item" v-for="(item, index) in scope.napList" :key="index + 'nap'" :style="{'left': item.left + 120 + 'px', 'width': item.width + 'px'}" @click="handleNapDetail(scope)"></span>
        <span class="wake-item" v-for="(item, index) in scope.wakeList" :key="index + 'wake'" :style="{'left': item.left + 120 + 'px', 'width': item.width + 'px'}" @click="handleDetail(scope)"></span>
        <span class="medicine-item" v-for="(item, index) in scope.medicineList" :key="index + 'medicine'" :style="{'left': item.left + 120 + 'px'}" @click="handleDetail(scope)">M</span>
        <span class="bedridden-item" :style="{'left': scope.bedridden.left + 120 + 'px', 'width': scope.bedridden.width + 'px'}" @click="handleDetail(scope)"></span>
        <span class="sleepPrescription-item" :style="{'left': scope.sleepPrescription.left + 120 + 'px', 'width': scope.sleepPrescription.width + 'px'}" @click="handleDetail(scope)"></span>
      </div>

      <div class="cell" slot="cell">
        <div class="cell1"></div>
        <div class="cell1"></div>
        <div class="cell2"></div>
      </div>

      <div class="cell cell-right" slot="cellRight">
        <div class="cell1"></div>
        <div class="cell1"></div>
        <div class="cell2"></div>
      </div>

      <div slot="nightWake" slot-scope="scope">
        <p class="text">{{ scope.nightWakeTotalTime }}分</p>
        <p class="text">{{ scope.nightWakeTimes }}次</p>
      </div>

      <template slot="sleepEfficiency" slot-scope="scope">{{ scope.sleepEfficiency }}%</template>

      <template slot="mornFeelType" slot-scope="scope">{{ getMornFeelType(scope.mornFeelType) }}</template>
      <template slot="napsNum" slot-scope="scope">{{ scope.naps.length }}</template>
    </a-table>
    <PaSleepDiaryDetailModal ref="modalForm" />
    <PaNaoDetailModal ref="napFrom" />
  </div>
</template>

<script>
import PaSleepDiaryDetailModal from './PaSleepDiaryDetailModal.vue'
import PaNaoDetailModal from './PaNaoDetailModal.vue'

export default {
  name: 'pa-sleep-diary-detail',

  components: {
    PaSleepDiaryDetailModal,
    PaNaoDetailModal
  },

  props: {
    dataSource: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      columns: [
        {
          title:'日期(星期)',
          align:"center",
          width: 120,
          scopedSlots: { customRender: 'date' }
        },
        {
          title:'昨天上午',
          align:"center",
          width: 120,
          children: [
            {
              title: '10',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '11',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '12',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            }
          ]
        },
        {
          title:'昨天下午',
          align:"center",
          width: 440,
          children: [
            {
              title: '13',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '14',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '15',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '16',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '17',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '18',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '19',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '20',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '21',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '22',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '23',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '24',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            }
          ]
        },
        {
          title:'今天上午',
          align:"center",
          width: 400,
          children: [
            {
              title: '1',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '2',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '3',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '4',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '5',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '6',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '7',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '8',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '9',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            
            {
              title: '10',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cellRight' }
            },

            {
              title: '11',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cellRight' }
            },

            {
              title: '12',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cellRight' }
            }
          ]
        },
        {
          title:'今天下午',
          align:"center",
          width: 440,
          children: [
            {
              title: '13',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '14',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            },
            {
              title: '15',
              width: 40,
              align:"center",
              scopedSlots: { customRender: 'cell' }
            }
          ]
        },
        {
          title:'睡眠统计',
          align:"center",
          children: [
            {
              title: '睡眠时长',
              align:"center",
              dataIndex: 'sleepLength'
            },
            {
              title: '夜醒',
              align:"center",
              scopedSlots: { customRender: 'nightWake' }
            },
            {
              title: '睡眠效率',
              align:"center",
              scopedSlots: { customRender: 'sleepEfficiency' }
            },
            {
              title: '起床时感觉',
              align:"center",
              scopedSlots: { customRender: 'mornFeelType' }
            },
            {
              title: '小睡次数',
              align:"center",
              scopedSlots: { customRender: 'napsNum' }
            },
            {
              title: '小睡总时长',
              align:"center",
              dataIndex: 'napLength'
            }
          ]
        }
      ],
      data:[
        {
          id: 1,
          date: '2023-03-09',
          week: '星期四',
        },
        {
          id: 2,
          date: '2023-03-09',
          week: '星期四',
        }
      ],

      loading: false,
      ipagination:{
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      isorter:{
        column: 'createTime',
        order: 'desc',
      },
    }
  },

  methods: {
    getWeek(date) {
      const datelist = ['星期日','星期一','星期二','星期三','星期四','星期五','星期六']
      return datelist[new Date(date).getDay()]
    },

    getMornFeelType(type) {
      let res = ''
      switch(type) {
        case 1:
          res = '十分差'
          break
        case 2:
          res = '较差'
          break
        case 3:
          res = '正常'
          break
        case 4:
          res = '较好'
          break
        case 5:
          res = '超级棒'
          break
        default:
          res = ''
      }

      return res
    },

    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      this.ipagination = pagination;
      this.$emit('handleTableChange', pagination)
    },

    handleDetail:function(record){
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title="数据详情";
      this.$refs.modalForm.disableSubmit = true;
    },

    handleNapDetail:function(record){
      this.$refs.napFrom.edit(record);
      this.$refs.napFrom.title="小睡详情";
      this.$refs.napFrom.disableSubmit = true;
    },
  }
}
</script>

<style lang="less">
.pa-sleep-diary-detail {
  padding-top: 20px;

  .chart-legend {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 25px;

    .item {
      display: inline-flex;
      align-items: center;
      padding-right: 50px;

      .item-legend {
        width: 16px;
        height: 16px;
      }

      .item-text {
        padding-left: 10px;
      }

      .legend1 {
        background: #6595F4;
      }

      .legend5 {
        background: #35cd3d;
      }

      .legend2 {
        background: #FFCE7B;
      }

      .legend3 {
        border-left: 1px solid #35cd3d;
        border-right: 1px solid #35cd3d;
        color: #35cd3d;
        padding-left: 1px;
      }

      .legend4 {
        border: 1px solid red;
        border-top: none;
      }
    }
  }

  .ant-table {
    .ant-table-row > td {
      position: relative;
    }

    .sleep-item {
      position: absolute;
      top: 0;
      height: 22px;
      background: #6595F4;
      z-index: 1;
      cursor: pointer;
    }

    .nap-item {
      position: absolute;
      top: 0;
      height: 22px;
      background: #35cd3d;
      z-index: 1;
      cursor: pointer;
    }

    .wake-item {
      position: absolute;
      top: 0;
      height: 22px;
      background: #FFCE7B;
      z-index: 1;
      cursor: pointer;
    }

    .medicine-item {
      position: absolute;
      top: 44px;
      z-index: 1;
      cursor: pointer;
    }

    .bedridden-item {
      display: block;
      position: absolute;
      top: 33px;
      height: 1px;
      background: #35cd3d;
      z-index: 1;
      cursor: pointer;


      &::before {
        position: absolute;
        left: 0;
        content: '';
        top: -9px;
        height: 18px;
        width: 1px;
        background: #35cd3d;
      }

      &::after {
        position: absolute;
        right: 0;
        content: '';
        top: -9px;
        height: 18px;
        width: 1px;
        background: #35cd3d;
      }
    }

    .sleepPrescription-item {
      position: absolute;
      top: 0;
      height: 22px;
      border: 1px solid red;
      border-top: none;
      z-index: 2;
      cursor: pointer;
    }

    .ant-table-row-cell-break-word {
      padding: 0 !important;
    }

    .text {
      margin: 0;
    }

    .cell {
      width: 40px;
      height: 100%;
      .cell1 {
        width: 39px;
        height: 22px;
        background-color: #fff;
        border-bottom: 1px solid #f4f4f4;
      }

      .cell2 {
        width: 40px;
        height: 22px;
        background-color: #fff;
        border-bottom: none;
      }
    }

    .cell-right {
      .cell2 {
        width: 39px;
      }
    }
  }
}
</style>
