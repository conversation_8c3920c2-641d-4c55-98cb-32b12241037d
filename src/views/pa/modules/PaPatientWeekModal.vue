<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <pa-patient-week-chart ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></pa-patient-week-chart>
  </j-modal>
</template>

<script>

  import PaPatientWeekChart from './PaPatientWeekChart'
  export default {
    name: 'PaPatientWeekModal',
    components: {
      PaPatientWeekChart
    },
    data () {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      loadData (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.loadDetail(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>