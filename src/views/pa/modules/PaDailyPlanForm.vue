<template>
  <a-spin :spinning="false">
    <j-form-container :disabled="false">
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        class="j-table-force-nowrap"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        :loading="loading">
        <template slot="status" slot-scope="text">
          <span class="error-text" v-if="text.type === 'measureTask' && text.status === 0">未开始</span>
          <span class="default-text" v-if="text.type === 'measureTask' && text.status === 1">进行中</span>
          <span class="success-text" v-if="text.type === 'measureTask' && text.status === 2">已完成</span>
          <span class="error-text" v-if="text.type !== 'measureTask' && text.status === 0">待完成</span>
          <span class="success-text" v-if="text.type !== 'measureTask' && text.status === 1">已完成</span>
        </template>
      </a-table>
    </j-form-container>
  </a-spin>
</template>

<script>
const EditableCell = {
  template: `
    <div class="editable-cell">
      <div v-if="editable">
        <j-dict-select-tag style="width: 150px;" @change="handleChange" @pressEnter="check" v-model="value" placeholder="请选择治疗情况"
                                dictCode="treatment_condition"/>
        <a-icon
          type="check"
          @click="check"
        />
      </div>
      <div v-else class="editable-cell-text-wrapper">
        {{ transform(value) || ' ' }}
        <a-icon type="edit" class="editable-cell-icon" @click="edit" />
      </div>
    </div>
  `,
  props: {
    text: Number
  },
  data() {
    return {
      value: this.text,
      editable: false
    }
  },
  methods: {
    handleChange(value) {
      this.value = value
    },
    check() {
      this.editable = false
      this.$emit('change', this.value)
    },
    edit() {
      this.editable = true
    },
    transform(value) {
      if (value == 1) {
        return '很好'
      } else if (value == 2) {
        return '好'
      } else if (value == 3) {
        return '一般'
      } else if (value == 4) {
        return '差'
      } else if (value == 5) {
        return '很差'
      }
    }
  }
}

import { getAction } from '@/api/manage'

export default {
  name: 'PaPatientSchemeForm',
  components: {
    EditableCell
  },
  data() {
    return {
      columns: [
        {
          title: '计划内容名称',
          align: 'center',
          dataIndex: 'title'
        },
        {
          title: '完成状态',
          align: 'center',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '计划开始时间',
          align: 'center',
          dataIndex: 'startTime',
          customRender: function(text) {
            return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
          }
        },
        {
          title: '计划完成时间',
          align: 'center',
          dataIndex: 'endTime',
          customRender: function(text) {
            return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
          }
        },
      ],
      loading: false,
      dataSource: [],

      url: {
        queryById: '/pa/paPatientScheme/queryById'
      },
      record: {}
    }
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  methods: {
    loadData(record) {
      if (record) this.record = record
      let params = {}//查询条件
      params.id = this.record.id
      
      getAction(this.url.queryById, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.detailMapList
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>
.success-text {
  color: green;
}

.default-text {
  color: blue;
}

.error-text {
  color: red;
}

.warn-text {
  color: orange;
}
</style>