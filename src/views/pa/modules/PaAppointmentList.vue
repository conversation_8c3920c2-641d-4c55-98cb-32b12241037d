<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="学生姓名">
              <j-input placeholder="请输入学生姓名" v-model="queryParam.patientName"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="预约日期">
              <a-date-picker valueFormat="YYYY-MM-DD" placeholder="请选择预约日期" v-model="queryParam.visitDate" style="width: 100%"></a-date-picker>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8">
            <a-form-item label="状态">
              <a-select placeholder="请选择预约状态" style="width: 100%" v-model="queryParam.status">
                <a-select-option v-for="status in statusList" :value="status.id" :key="status.id">
                  {{ status.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery" style="margin-left: 8px"></j-super-query>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- table区域-begin -->
    <div>
      <!-- <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{selectedRowKeys.length
        }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div> -->

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a v-has="'pa.appointment.verify'" v-if="record.status == '1'" @click="handleEdit(record)">预约确认</a>
          <a-divider v-has="'pa.appointment.verify'" v-if="record.status == '1'" type="vertical" />
          <a v-if="record.status == '2'" @click="handleFinish(record)">确认完成</a>
          <a-divider v-if="record.status == '2'" type="vertical" />
          <a v-if="record.status == '1' || record.status == '2'" @click="handleCancle(record)">取消预约</a>
          <a-divider v-if="record.status == '1' || record.status == '2'" type="vertical" />
          <a v-if="record.status == '2'" @click="handleOverdue(record)">逾约</a>
          <a-divider type="vertical" v-if="record.status == '2'" />
          <a @click="handleRemark(record)">编辑备注</a>
        </span>
      </a-table>
    </div>

    <pa-appointment-modal ref="modalForm" @ok="modalFormOk"></pa-appointment-modal>
    <a-modal v-model:visible="visible" :title="modelTitle" @ok="handleOk" @cancel="handleCancel">
      <a-textarea v-model:value="textContent" :placeholder="textPlaceholder" :autosize="{minRows: 4, maxRows: 6}" />
    </a-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { httpAction } from '@/api/manage'
import { getAction } from '@/api/manage'
import PaAppointmentModal from './PaAppointmentModal'
import { Modal } from 'ant-design-vue'
import { filterObj } from '@/utils/util';

export default {
  name: 'PaAppointmentList',
  components: {
    PaAppointmentModal
  },
  data () {
    return {
      description: '学生预约管理页面',
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: {},
      /* 数据源 */
      dataSource: [],
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      /* 排序参数 */
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      // 表头
      columns: [
        {
          title: '学生名称',
          align: 'center',
          dataIndex: 'patientName'
        },
        {
          title: '学生电话',
          align: 'center',
          dataIndex: 'patientPhone'
        },
        {
          title: '老师名称',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '就诊日期',
          align: 'center',
          dataIndex: 'visitDate',
          customRender: function (text) {
            return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
          }
        },
        {
          title: '就诊时间',
          align: 'center',
          dataIndex: 'visitTime',
          customRender: function (text) {
            return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
          }
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '学生备注',
          align: "center",
          dataIndex: 'patientNote'
        },
        {
          title: '老师备注',
          align: "center",
          dataIndex: 'doctorNote'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      statusList: [
        {
          id: 1,
          name: '待确认'
        },
        {
          id: 2,
          name: '已确认'
        },
        {
          id: 4,
          name: '已取消'
        },
        {
          id: 5,
          name: '已逾约'
        },
        {
          id: 3,
          name: '已完成'
        }
      ],
      url: {
        list: '/pa/paAppointment/list',
        delete: '/pa/paAppointment/delete',
        edit: '/pa/paAppointment/edit',
        deleteBatch: '/pa/paAppointment/deleteBatch',
        exportXlsUrl: '/pa/paAppointment/exportXls',
        importExcelUrl: 'pa/paAppointment/importExcel'

      },
      dictOptions: {},
      superFieldList: [],
      visible: false,
      modelTitle: '',
      textPlaceholder: '',
      textContent: '',
      loading: false
    }
  },
  created () {
    // this.loadData()
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handleEdit: function (record) {
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title = "确认预约";
      this.$refs.modalForm.disableSubmit = false;
    },
    loadData (arg, id) {
      if (!this.url.list) {
        this.$message.error("请设置url.list属性!")
        return
      }
      this.dataSource = []
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }

      var params = this.getQueryParams();//查询条件
      if (id) {
        params.id = id
      }
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result;
          if (res.result.total) {
            this.ipagination.total = res.result.total;
          } else {
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },
    getQueryParams () {
      //获取查询条件
      let sqp = {}
      if (this.superQueryParams) {
        sqp['superQueryParams'] = encodeURI(this.superQueryParams)
        sqp['superQueryMatchType'] = this.superQueryMatchType
      }
      var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters);
      param.field = this.getQueryField();
      param.pageNo = this.ipagination.current;
      param.pageSize = this.ipagination.pageSize;
      return filterObj(param);
    },
    getQueryField () {
      //TODO 字段权限控制
      var str = "id,";
      this.columns.forEach(function (value) {
        str += "," + value.dataIndex;
      });
      return str;
    },
    searchQuery () {
      this.loadData(1);
    },
    searchReset () {
      this.queryParam = {}
      this.loadData(1);
    },
    handleSuperQuery (params, matchType) {
      //高级查询方法
      if (!params) {
        this.superQueryParams = ''
        this.superQueryFlag = false
      } else {
        this.superQueryFlag = true
        this.superQueryParams = JSON.stringify(params)
        this.superQueryMatchType = matchType
      }
      this.loadData(1)
    },
    handleTableChange (pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      console.log(pagination)
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
      this.loadData();
    },
    getSuperFieldList () {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'patientName', text: '学生名称' })
      fieldList.push({ type: 'string', value: 'patientPhone', text: '学生联系电话' })
      fieldList.push({ type: 'string', value: 'doctorName', text: '老师名称' })
      fieldList.push({ type: 'date', value: 'visitDate', text: '就诊日期' })
      fieldList.push({ type: 'date', value: 'visitTime', text: '就诊时间' })
      fieldList.push({ type: 'string', value: 'status', text: '状态' })
      this.superFieldList = fieldList
    },
    handleUpdateStatus (id, status, doctorNote = '') {
      let that_ = this
      that_.loading = true
      let param = {
        id,
        status
      }
      if (doctorNote) param.doctorNote = doctorNote
      httpAction(that_.url.edit, param, 'put').then((res) => {
        if (res.success) {
          that_.$message.success(res.message)
          that_.modalFormOk()
        } else {
          that_.$message.warning(res.message)
        }
      }).finally(() => {
        that_.loading = false
      })
    },

    handleCancle (record) {
      Modal.confirm({
        title: '取消预约',
        content: '取消后不可恢复，是否确认删除？',
        onOk: () => {
          this.handleUpdateStatus(record.id, '4')
        },
        onCancel: () => {
        },
        class: 'test',
      });
    },

    handleOverdue (record) {
      Modal.confirm({
        title: '逾约',
        content: '学生已逾约，是否确认？',
        onOk: () => {
          this.handleUpdateStatus(record.id, '5')
        },
        onCancel: () => {
        },
        class: 'test',
      });
    },

    handleFinish (record) {
      this.chooseData = record
      this.chooseData.status = '3'
      this.modelTitle = '完成预约'
      this.textPlaceholder = '预约完成啦~补充一些对学生预约内容吧~'
      this.visible = true
    },

    handleRemark (record) {
      this.chooseData = record
      this.modelTitle = '备注'
      this.textPlaceholder = '请输入备注'
      this.visible = true
    },

    handleOk () {
      this.handleUpdateStatus(this.chooseData.id, this.chooseData.status, this.textContent)
      this.handleCancel()
    },

    handleCancel () {
      this.modelTitle = ''
      this.textPlaceholder = ''
      this.textContent = ''
      this.visible = false
    },

    modalFormOk () {
      // 新增/修改 成功时，重载列表
      this.loadData();
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>