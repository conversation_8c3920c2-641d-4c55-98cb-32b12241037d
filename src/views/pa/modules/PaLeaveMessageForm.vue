<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="老师名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorName">
              <a-input v-model="model.doctorName" placeholder="请输入老师名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学生姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
              <a-input v-model="model.patientName" placeholder="请输入学生姓名"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="留言内容" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content">
              <a-textarea row="5" v-model="model.content" placeholder="请输入留言内容" style="width: 100%;"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
    <!-- 子表单区域 -->
    <a-tabs v-model="activeKey" @change="handleChangeTabs">
      <a-tab-pane tab="留言回复" :key="refKeys[0]" :forceRender="true">
        <j-editable-table :ref="refKeys[0]" :loading="paLeaveMessageReplyTable.loading" :columns="paLeaveMessageReplyTable.columns" :dataSource="paLeaveMessageReplyTable.dataSource" :maxHeight="300" :disabled="formDisabled" :rowNumber="true" :rowSelection="true" :actionButton="true" />
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<script>

import { FormTypes, getRefPromise, VALIDATE_NO_PASSED } from '@/utils/JEditableTableUtil'
import { JEditableTableModelMixin } from '@/mixins/JEditableTableModelMixin'
import ATextarea from 'ant-design-vue/es/input/TextArea'

export default {
  name: 'PaLeaveMessageForm',
  mixins: [JEditableTableModelMixin],
  components: { ATextarea },
  data () {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      labelCol2: {
        xs: { span: 24 },
        sm: { span: 3 }
      },
      wrapperCol2: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      model: {},
      // 新增时子表默认添加几行空数据
      addDefaultRowNum: 1,
      validatorRules: {},
      refKeys: ['paLeaveMessageReply'],
      tableKeys: ['paLeaveMessageReply'],
      activeKey: 'paLeaveMessageReply',
      // 留言回复
      paLeaveMessageReplyTable: {
        loading: false,
        dataSource: [],
        columns: [
          {
            title: '老师名称',
            key: 'doctorName',
            type: FormTypes.input,
            width: '200px',
            placeholder: '请输入${title}',
            defaultValue: ''
          },
          {
            title: '学生姓名',
            key: 'patientName',
            type: FormTypes.input,
            width: '200px',
            placeholder: '请输入${title}',
            defaultValue: ''
          },
          {
            title: '回复内容',
            key: 'content',
            type: FormTypes.input,
            width: '200px',
            placeholder: '请输入${title}',
            defaultValue: ''
          }
        ]
      },
      url: {
        add: '/pa/paLeaveMessage/add',
        edit: '/pa/paLeaveMessage/edit',
        queryById: '/pa/paLeaveMessage/queryById',
        paLeaveMessageReply: {
          list: '/pa/paLeaveMessage/queryPaLeaveMessageReplyByMainId'
        }
      }
    }
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
  },
  methods: {
    addBefore () {
      this.paLeaveMessageReplyTable.dataSource = []
    },
    getAllTable () {
      let values = this.tableKeys.map(key => getRefPromise(this, key))
      return Promise.all(values)
    },
    /** 调用完edit()方法之后会自动调用此方法 */
    editAfter () {
      this.$nextTick(() => {
      })
      // 加载子表数据
      if (this.model.id) {
        let params = { id: this.model.id }
        this.requestSubTableData(this.url.paLeaveMessageReply.list, params, this.paLeaveMessageReplyTable)
      }
    },
    //校验所有一对一子表表单
    validateSubForm (allValues) {
      return new Promise((resolve, reject) => {
        Promise.all([]).then(() => {
          resolve(allValues)
        }).catch(e => {
          if (e.error === VALIDATE_NO_PASSED) {
            // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
            this.activeKey = e.index == null ? this.activeKey : this.refKeys[e.index]
          } else {
            console.error(e)
          }
        })
      })
    },
    /** 整理成formData */
    classifyIntoFormData (allValues) {
      let main = Object.assign(this.model, allValues.formValue)
      return {
        ...main, // 展开
        paLeaveMessageReplyList: allValues.tablesValue[0].values
      }
    },
    validateError (msg) {
      this.$message.error(msg)
    }

  }
}
</script>

<style scoped>
</style>