<template>
  <a-spin :spinning="false">
    <j-form-container :disabled="false">
      <!-- 主表单区域 -->
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="请选择您要生成报告的方案" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="paPatientSchemeGroupId">
              <a-select
                style="width: 100%"
                placeholder="请选择方案"
                v-model="model.paPatientSchemeGroupId">
                <a-select-option v-for="scheme in schemes" :key="scheme.id">
                  {{ scheme.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="请选择模板" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reportTemplateId">
              <a-select
                style="width: 100%"
                placeholder="请选择模板"
                v-model="model.reportTemplateId">
                <a-select-option v-for="report in reports" :key="report.id">
                  {{ report.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'

export default {
  name: 'PaCreateReportForm',
  data() {
    return {
      patientDetail: JSON.parse(localStorage.getItem("PatientDetail")),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      reports: [
        {
          id: 1,
          name: '标准模板'
        }
      ],
      model:{
        reportTemplateId: 1
      },
      schemes: [],
      validatorRules: {
        paPatientSchemeGroupId: [
          { required: true, message: '请选择方案' }
        ],
        reportTemplateId: [
          { required: true, message: '请选择模板' }
        ]
      },

      url: {
        add: '/pa/paPatientSchemeGroupReport/addReport',
        list: '/pa/paPatientSchemeGroup/listDoneByPatientId',
      },
    }
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    add() {
      this.edit(this.modelDefault)
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.loadPsTemplate()
      this.visible = true
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    },
    /**
     * 初始化量表下拉选
     */
    loadPsTemplate() {
      getAction(this.url.list, {patientId: this.patientDetail.id}).then((res) => {
        if (res.success) {
          this.schemes = res.result
        }
      })
    }
  }
}
</script>

<style scoped>
.success-text {
  color: green;
}

.default-text {
  color: blue;
}

.error-text {
  color: red;
}

.warn-text {
  color: orange;
}
</style>