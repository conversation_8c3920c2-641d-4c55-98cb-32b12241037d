<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <div class="mood-top">
        <img class="mood-img" :src="moodList[model.moodStatus].img" />
        <p class="mood-text">{{ moodList[model.moodStatus].text }}</p>
        <div class="mood-label">
          <span class="label-item" v-for="(item, index) in model.moodSubType" :key="index">{{ item }}</span>
        </div>
      </div>

      <div class="mood-content">
        <p class="content-title">
          是因为什么事情影响了你的情绪
        </p>
        <p class="content-text">
          {{ model.reason }}
        </p>
      </div>

      <div class="mood-content">
        <p class="content-title">
          老师备注
        </p>
        <p class="content-text">
          {{ model.doctorNote || '暂无' }}
        </p>
      </div>
    </j-form-container>
  </a-spin>
</template>

<script>
import { mapGetters } from 'vuex'
import PaPatientPlanModal from './PaPatientPlanModal.vue'
import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'
import moment from 'moment'

export default {
  name: 'PaPatientSchemeForm',
  components: {
    PaPatientPlanModal,
    JLabelSelect
  },
  data () {
    return {
      confirmLoading: false,
      model: {
        moodStatus: 1,
      },
      disabledEdit: true,
      url: {
        add: '/pa/paPatientScheme/add',
        edit: '/pa/paPatientScheme/edit',
        listAll: '/psychology/psMeasure/listAllWithTenants'
      },
      moodList: [
        {
          id: 0,
          text: '生气',
          img: require('../../../../public/static/common/<EMAIL>')
        },
        {
          id: 1,
          text: '不开心',
          img: require('../../../../public/static/common/<EMAIL>')
        },
        {
          id: 2,
          text: '平淡',
          img: require('../../../../public/static/common/<EMAIL>')
        },
        {
          id: 3,
          text: '开心',
          img: require('../../../../public/static/common/<EMAIL>')
        },
        {
          id: 4,
          text: '兴奋',
          img: require('../../../../public/static/common/<EMAIL>')
        },
      ],
      scSchemePage: {},
      isEdit: false,
    }
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    add () {
      this.modelDefault.doctorId = this.userInfo().id
      this.modelDefault.doctorName = this.userInfo().realname
      this.disabledEdit = false
      this.edit(this.modelDefault)
      this.isEdit = false
    },
    edit (record) {
      this.model = Object.assign({}, record)
      const index = this.moodList.findIndex(item => item.text === this.model.moodType)
      this.model.moodStatus = index
      this.model.moodSubType = this.model.moodSubType.split(',')
      this.visible = true
      this.isEdit = true
    },
  }
}
</script>

<style lang="scss" scoped>
.mood-top {
  display: flex;
  align-items: center;

  .mood-img {
    width: 50px;
  }

  .mood-text {
    font-size: 16px;
    font-weight: 600;
    padding-left: 20px;
    margin: 0;
  }

  .mood-label {
    display: flex;
    align-items: center;
    padding-left: 15px;

    .label-item {
      font-size: 12px;
      line-height: 24px;
      padding: 0 8px;
      margin-right: 12px;
      border-radius: 4px;
      color: #fff;
      background-color: cornflowerblue;
    }
  }
}

.mood-content {
  padding-top: 20px;

  .content-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 28px;
  }

  .content-text {
    padding-top: 8px;
    font-size: 12px;
    line-height: 20px;
  }
}
</style>