<template>
  <div class="reserve-record">
    <div class="table-operator">
      <div class="search">
        <div class="search-item">
          <div class="item-label">预约状态：</div>
          <div class="item-content">
            <a-button type="primary" v-for="item in statusList" :key="item.id" :ghost="queryParam.status !== item.id" @click="setStatus(item.id)">{{ item.value }}</a-button>
          </div>
        </div>
        <div class="search-item">
          <div class="item-label">预约日期：</div>
          <div class="item-content">
            <a-range-picker v-model="diaryDate" format="YYYY-MM-DD" @change='changeDateQuery' />
          </div>
        </div>
      </div>

      <a-button @click="loadData" type="primary">查询</a-button>
    </div>
    <a-table ref="table" size="middle" bordered rowKey="id" class="j-table-force-nowrap" :columns="columns" :dataSource="dataSource" :pagination="ipagination" @change="handleTableChange" :loading="loading">
      <template slot="patientNote" slot-scope="text, record">
        <a-tooltip placement="top">
          <template #title>
            <span>{{ record.patientNote }}</span>
          </template>
          <p class="remark-text">{{ record.patientNote }}</p>
        </a-tooltip>
      </template>
      <template slot="doctorNote" slot-scope="text, record">
        <a-tooltip placement="top">
          <template #title>
            <span>{{ record.doctorNote }}</span>
          </template>
          <p class="remark-text">{{ record.doctorNote }}</p>
        </a-tooltip>
      </template>
      <template slot="status" slot-scope="text, record">
        {{ statusList.filter(item => item.id == record.status)[0].value }}
      </template>
      <span slot="action" slot-scope="text, record">
        <template v-if="record.status == 1">
          <a @click="handleEdit(record)">预约确认</a>
          <a-divider type="vertical" />
        </template>
        <template v-if="record.status == 2">
          <a @click="handleFinish(record)">完成预约</a>
          <a-divider type="vertical" />
        </template>
        <template v-if="record.status == 1 || record.status == 2">
          <a @click="handleCancle(record)">取消预约</a>
          <a-divider type="vertical" />
        </template>
        <a @click="handleRemark(record)">备注</a>
        <template v-if="record.status == 2">
          <a-divider type="vertical" />
          <a @click="handleOverdue(record)">逾约</a>
        </template>
      </span>
    </a-table>

    <PaReserveConfirmModal ref="modalForm" @ok="modalFormOk"></PaReserveConfirmModal>
    <a-modal v-model:visible="visible" :title="modelTitle" @ok="handleOk" @cancel="handleCancel">
      <a-textarea v-model:value="textContent" :placeholder="textPlaceholder" :autosize="{minRows: 4, maxRows: 6}" />
    </a-modal>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { httpAction } from '@/api/manage'
import { getAction } from '@/api/manage'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PaReserveConfirmModal from './PaReserveConfirmModal.vue'
import { Modal } from 'ant-design-vue';

export default {
  name: 'pa-reserve-record',

  mixins: [JeecgListMixin, mixinDevice],

  components: {
    PaReserveConfirmModal
  },

  props: {
    id: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: {
        status: ''
      },
      /* 数据源 */
      dataSource: [],
      loading: false,
      /* 排序参数 */
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      columns: [
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '预约日期',
          align: 'center',
          dataIndex: 'visitDate'
        },
        {
          title: '预约时间',
          align: 'center',
          dataIndex: 'visitTime'
        },
        {
          title: '状态',
          align: 'center',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '学生备注',
          align: 'center',
          // dataIndex: 'patientNote'
          scopedSlots: { customRender: 'patientNote' }
        },
        {
          title: '老师备注',
          align: 'center',
          // dataIndex: 'doctorNote',
          scopedSlots: { customRender: 'doctorNote' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' }
        }
      ],
      statusList: [
        {
          id: 1,
          value: '待确认'
        },
        {
          id: 2,
          value: '已确认'
        },
        {
          id: 3,
          value: '已完成'
        },
        {
          id: 4,
          value: '已取消'
        },
        {
          id: 5,
          value: '已逾约'
        }
      ],
      url: {
        list: '/pa/paAppointment/list',
        delete: '/pa/paAppointment/delete',
        edit: '/pa/paAppointment/edit',
        deleteBatch: '/pa/paAppointment/deleteBatch',
        exportXlsUrl: '/pa/paAppointment/exportXls',
        importExcelUrl: 'pa/paAppointment/importExcel'
      },
      paPatientSchemeId: '',
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      visible: false,
      modelTitle: '',
      textPlaceholder: '',
      textContent: '',
      diaryDate: [],
      isFinish: false
    }
  },

  methods: {
    setStatus (id) {
      if (id === this.queryParam.status) {
        this.queryParam.status = ''
      } else {
        this.queryParam.status = id
      }

      this.loadData(1)
    },

    loadData (arg) {
      if (!this.url.list) {
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      params.patientId = this.id
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result;
          if (res.result.total) {
            this.ipagination.total = res.result.total;
          } else {
            this.ipagination.total = 0;
          }
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    changeDateQuery () {
      if (this.diaryDate.length) {
        this.queryParam.startDate = this.diaryDate[0].format('YYYY-MM-DD')
        this.queryParam.endDate = this.diaryDate[1].format('YYYY-MM-DD')
      } else {
        this.queryParam.startDate = ''
        this.queryParam.endDate = ''
      }
    },

    reservationConfirm (record) {
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title = "预约确认";
      this.$refs.modalForm.disableSubmit = false;
    },

    editPrescription () {
      this.$refs.modalPrescription.title = "修改睡眠处方"
      this.$refs.modalPrescription.loadData(this.id);
    },

    handleTableChange (pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
      this.loadData();
    },

    handleUpdateStatus (id, status, doctorNote = '') {
      let that_ = this
      that_.loading = true
      let param = {
        id,
        status
      }
      if (doctorNote) param.doctorNote = doctorNote
      httpAction(that_.url.edit, param, 'put').then((res) => {
        if (res.success) {
          that_.$message.success(res.message)
          that_.modalFormOk()
        } else {
          that_.$message.warning(res.message)
        }
      }).finally(() => {
        that_.loading = false
      })
    },

    handleCancle (record) {
      Modal.confirm({
        title: '取消预约',
        content: '取消后不可恢复，是否确认删除？',
        onOk: () => {
          this.handleUpdateStatus(record.id, '4')
        },
        onCancel () {
        },
        class: 'test',
      });
    },

    handleOverdue (record) {
      Modal.confirm({
        title: '逾约',
        content: '学生已逾约，是否确认？',
        onOk: () => {
          this.handleUpdateStatus(record.id, '5')
        },
        onCancel () {
        },
        class: 'test',
      });
    },

    handleFinish (record) {
      this.chooseData = record
      this.isFinish = true
      this.modelTitle = '完成预约'
      this.textPlaceholder = '预约完成啦~补充一些对学生预约的内容吧~'
      this.visible = true
    },

    handleRemark (record) {
      this.chooseData = record
      this.isFinish = false
      this.modelTitle = '备注'
      this.textPlaceholder = '请输入备注'
      this.visible = true
    },

    handleOk () {
      if (this.isFinish) {
        this.handleUpdateStatus(this.chooseData.id, '3', this.textContent)
      } else {
        this.handleUpdateStatus(this.chooseData.id, this.chooseData.status, this.textContent)
      }
      this.handleCancel()
    },

    handleCancel () {
      this.textContent = ''
      this.modelTitle = ''
      this.textPlaceholder = ''
      this.isFinish = false
      this.visible = false
    },

    modalFormOk () {
      // 新增/修改 成功时，重载列表
      this.loadData();
    },
  }
}
</script>

<style lang="less">
.reserve-record {
  padding: 0 10px 20px;

  .table-operator {
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;

    .search {
      display: flex;

      .search-item {
        display: flex;
        margin-right: 20px;

        .item-label {
          margin-bottom: 8px;
          line-height: 32px;
        }
      }
    }
  }

  .remark-text {
    width: 200px;
    margin: 0;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
  }
}
</style>