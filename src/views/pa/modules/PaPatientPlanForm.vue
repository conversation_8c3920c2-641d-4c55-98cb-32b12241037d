<template>
  <a-spin :spinning="confirmLoading">
    <a-table rowKey="id" :scroll="{ x: '600px' }" :columns="columns" :data-source="dataSource"
              size="small">
      <template slot="titles" slot-scope="scope">
        <div class="title-item" v-if="!scope.day">
          <span>第</span>
          <a-select v-model="scope.week" :default-value="1" disabled>
            <a-select-option v-for="item in 6" :key="item + 'week'" :value="item">{{ item }}</a-select-option>
          </a-select>
          <span>周</span>
        </div>
        <div class="title-item" v-else>
          <span>第</span>
          <a-select v-model="scope.week" :default-value="1" :disabled="disabled">
            <a-select-option v-for="item in 6" :key="item + 'week'" :value="item">{{ item }}</a-select-option>
          </a-select>
          <span>周的第</span>
          <a-select v-model="scope.day" :default-value="1" :disabled="disabled">
            <a-select-option v-for="item in 7" :key="item + 'day'" :value="item">{{ item }}</a-select-option>
          </a-select>
          <span>天</span>
        </div>
      </template>
      <template slot="sort" slot-scope="scope">
        <a-input v-model="scope.sort" disabled></a-input>
      </template>
    </a-table>
  </a-spin>
</template>

<script>

  import { getAction } from '@/api/manage'
  import { mapGetters } from 'vuex'

  export default {
    name: 'PaPatientPlanForm',
    data() {
      return {
        confirmLoading: false,
        url: {
          add: '/pa/paPatientScheme/add',
          edit: '/pa/paPatientScheme/edit',
          queryById: '/sc/scScheme/queryById'
        },

        columns: [
          {
            title: '标题',
            align: 'center',
            width: 300,
            scopedSlots: { customRender: 'titles' }
          },
          {
            title: '内容排序',
            align: 'center',
            width: 100,
            scopedSlots: { customRender: 'sort' }
          },
          {
            title: '素材',
            align: 'center',
            dataIndex: 'materialName'
          }
        ],
        dataSource: [],
        detail: {},
        disabled: false
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    methods: {
      ...mapGetters(['nickname', 'userInfo']),
      add(value) {
        this.loadSchemeSub(value)
      },
      edit(value) {
        this.disabled = true
        this.dataSource = value.paPatientSchemeSubList.concat(value.paPatientSchemeTrainList).concat(value.taMeasureTaskList)
        this.dataSource.forEach(item => {
          item.materialName = item.schemeSubTitle || item.schemeTrainTitle || item.measureName
        })
      },
      loadSchemeSub(id) {
        getAction(this.url.queryById, { id: id }).then(res => {
          let { result } = res
          this.detail = result
          this.dataSource = result.scSchemeSubList.concat(result.scSchemeTrainList).concat(result.scSchemeMeasureList)
        })
      },
      submitForm() {
        let week = 1
        this.dataSource.forEach(it => {
          let hasMaterial = this.detail.scSchemeSubList.filter(item => item.id === it.id)
          let hasTrain= this.detail.scSchemeTrainList.filter(item => item.trainId === it.id)
          let hasMeasure = this.detail.scSchemeMeasureList.filter(item => item.measureId === it.id)
          if (hasMaterial.length) {
            hasMaterial.week = it.week
          } else if (hasTrain.leng) {
            hasTrain.week = it.week
            hasTrain.day = it.day
          } else if (hasMeasure.length) {
            hasMeasure.week = it.week
            hasMeasure.day = it.day
          }
          if (it.week > week) week = it.week
        })

        this.$emit('ok', this.detail, week * 7)
      }
    }
  }
</script>

<style lang="scss" scoped>
.title-item {
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-select {
    width: 50px;
    margin: 0 8px;
  }
}
</style>