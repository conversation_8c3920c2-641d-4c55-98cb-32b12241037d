<template>
  <j-modal
    :title="title"
    :width="600"
    :visible="visible"
    :maskClosable="false"
    :footer="!isEdit"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel">
    <pa-patient-plan-form ref="realForm" @ok="submitCallback" />
  </j-modal>
</template>

<script>

  import PaPatientPlanForm from './PaPatientPlanForm.vue'

  export default {
    name: 'PaPatientPlanModal',
    components: {
      PaPatientPlanForm
    },
    data() {
      return {
        title:'',
        width:800,
        visible: false,
        isEdit: false
      }
    },
    methods:{
      add (value) {
        this.isEdit = false
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add(value);
        })
      },
      edit (value) {
        this.isEdit = true
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(value);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(params, days){
        this.$emit('ok', params, days);
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>

<style scoped>
</style>