<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24" v-if="!isEdit">
            <a-form-model-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <a-radio-group v-model="model.type" @change="handleChangeType">
                <a-radio :value="1">方案</a-radio>
                <a-radio :value="2">医嘱任务</a-radio>
                <a-radio :value="3">量表任务</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="老师名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorName">
              <a-input v-model="model.doctorName" disabled :defaultValue="nickname()" placeholder="请输入老师名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="!isEdit">
            <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sendByGroup">
              <span slot="label">
                是否按标签下发&nbsp;
                <a-tooltip title="按照学生的标签批量下发医嘱任务">
                  <a-icon type="question-circle-o" />
                </a-tooltip>
              </span>
              <a-radio-group v-model="model.sendByGroup">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="0">否</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.sendByGroup === 0">
            <a-form-model-item label="学生名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
              <j-select-patient v-model="model.patientName" @back="backPatientInfo" :backInfo="true" :disabled="disabledEdit" :isRadio="true"></j-select-patient>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.sendByGroup === 1">
            <a-form-model-item label="学生标签" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="selectedGroups">
              <j-label-select v-model="model.selectedGroups" :multiple="false" @change="changeSelectedGroups"></j-label-select>
            </a-form-model-item>
          </a-col>
          <template v-if="model.type === 1">
            <a-col :span="24">
              <a-form-model-item label="方案名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="schemeName">
                <j-dict-select-tag v-model="model.schemeGroupId" :disabled="isEdit" placeholder="请选择方案" @input="chengeScheme" dictCode="sc_scheme_group,title,id,del_flag=0" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="治疗天数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dayNum">
                <a-input-number v-model="model.dayNum" disabled placeholder="请输入治疗天数" style="width: 100%" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="startDate">
                <j-date placeholder="请选择开始时间" :disabled="isEdit" v-model="model.startDate" style="width: 100%" @change="changeEndDate" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="endDate">
                <j-date placeholder="请选择结束时间" disabled v-model="model.endDate" style="width: 100%" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
                <a-textarea :rows="5" v-model="model.remark" :disabled="isEdit" placeholder="请输入备注" style="width: 100%" />
              </a-form-model-item>
            </a-col>
          </template>

          <template v-if="model.type === 2">
            <a-col :span="24">
              <a-form-model-item label="完成时限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitDate">
                <a-date-picker format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" :show-time="{ format: 'HH:mm' }" v-model="model.limitDate" placeholder="请选择完成时限" style="width: 100%" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
                <a-input v-model="model.title" placeholder="请输入标题"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="任务内容" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content">
                <a-textarea :rows="5" v-model="model.content" placeholder="请输入任务内容"></a-textarea>
              </a-form-model-item>
            </a-col>
          </template>

          <template v-if="model.type === 3">
            <a-col :span="24">
              <a-form-model-item label="量表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="taskMeasures">
                <a-select mode="multiple" show-search placeholder="请选择量表" option-filter-prop="children" style="width: 100%" :filter-option="filterOption" v-model="model.taskMeasures" @change="handleChange">
                  <a-select-option v-for="measure in measures" :value="measure.id" :key="measure.id">
                    {{ measure.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="完成时限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitDate">
                <a-date-picker format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" :show-time="{ format: 'HH:mm' }" v-model="model.limitDate" placeholder="请选择完成时限" style="width: 100%" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
                <a-textarea :rows="5" v-model="model.remark" placeholder="请输入备注" style="width: 100%" />
              </a-form-model-item>
            </a-col>
          </template>

        </a-row>
      </a-form-model>
    </j-form-container>
    <pa-patient-plan-modal ref="planForm" @ok="submitCallback" />
  </a-spin>
</template>

<script>

import { httpAction, getAction } from '@/api/manage'
import { mapGetters } from 'vuex'
import PaPatientPlanModal from './PaPatientPlanModal.vue'
import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'
import moment from 'moment'

export default {
  name: 'PaPatientSchemeForm',
  components: {
    PaPatientPlanModal,
    JLabelSelect
  },
  data () {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      model: {
        type: 1,
        startDate: moment(new Date()).format(),
        dayNum: null,
        sendByGroup: 0,
        selectedGroups: null
      },
      measures: [],
      disabledEdit: true,
      validatorRules: {
        sendByGroup: [{ required: true, message: '请选择是否按标签下发!' }],
        patientName: [{ required: true, message: '请选择学生!' }],
        selectedGroups: [{ required: true, message: '请选择学生标签!' }],
        startDate: [{ required: true, message: '请选择开始时间!' }],
        measureId: [{ required: true, message: '请选择量表!' }],
        taskMeasures: [{ required: true, message: '请选择量表!' }],
        limitDate: [{ required: true, message: '请选择完成时限!' }],
        title: [{ required: true, message: '请输入标题!' }],
        content: [{ required: true, message: '请输入任务内容!' }]
      },
      url: {
        addScheme: '/pa/paPatientSchemeGroup/add',
        addAdvice: '/ta/taDoctorAdvice/add',
        addMeasure: '/ta/taMeasureTask/add',
        edit: '/pa/paPatientScheme/edit',
        listAll: '/psychology/psMeasure/listAllWithTenants',
        queryById: '/sc/scSchemeGroup/queryById'
      },
      scSchemePage: {},
      isEdit: false,
    }
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    handleChangeType (value) {
      this.$refs.form.resetFields()
      this.model = JSON.parse(JSON.stringify(this.modelDefault))
      this.model.type = value.target.value
    },
    /**
     * 初始化量表下拉选
     */
    loadPsTemplate () {
      httpAction(this.url.listAll, {}, 'get').then((res) => {
        if (res.success) {
          this.measures = res.result
        }
      })
    },
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    handleChange (value) {
      console.log(value);
      // this.model.measureId = value
    },
    changeSelectedGroups (value) {
      this.model.selectedGroups = value
    },
    chengeScheme (value) {
      this.model.schemeGroupId = value
      getAction(this.url.queryById, { id: value }).then(res => {
        this.model.dayNum = res.result.dayNum
        this.model.endDate = moment(new Date()).add(this.model.dayNum, 'days').format()
      })
    },
    clickScheme () {
      if (!this.isEdit) return
      this.$refs.planForm.edit(this.model);
      this.$refs.planForm.title = "编辑";
    },
    add () {
      this.modelDefault.doctorId = this.userInfo().id
      this.modelDefault.doctorName = this.userInfo().realname
      this.disabledEdit = false
      this.edit(this.modelDefault)
      this.isEdit = false
    },
    edit (record) {
      this.model = Object.assign({}, record)
      this.loadPsTemplate()
      this.model.sendByGroup = 0
      this.model.taskMeasures = []
      if (record.measureId) {
        this.model.measureId = record.measureId
        this.model.taskMeasures.push(record.measureId)
      }
      this.visible = true
      this.isEdit = true
    },
    submitForm () {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            if (this.model.type == 1) {
              httpurl += this.url.addScheme
            } else if (this.model.type == 2) {
              httpurl += this.url.addAdvice
            } else if (this.model.type == 3) {
              httpurl += this.url.addMeasure
            }

            method = 'post'
            this.model.doctorId = this.userInfo().id;
            let taskMeasures = this.model.taskMeasures
            let measures = []
            taskMeasures.forEach(measureId => {
              if (measureId) {
                let data = { id: measureId, name: this.measures.filter(item => item.id === measureId)[0].name }
                measures.push(data)
              }
            })
            this.model.measures = measures
          } else {
            httpurl += this.url.edit
            method = 'put'
            delete this.model.doctorId
          }
          this.model.taskMeasures = null
          let params = Object.assign({}, this.model)
          if (this.model.type == 3 && method == 'put') {
            params.measureName = this.measures.filter(item => item.id === this.model.measureId)[0].name
          }

          httpAction(httpurl, params, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    },
    backPatientInfo (info) {
      this.model.patientId = info[0].value
      this.model.patientName = info[0].text
    },
    submitCallback (params, days) {
      this.scSchemePage = params
      this.model.dayNum = days
      this.model.endDate = moment(new Date()).add(this.model.dayNum, 'days').format()
    },
    changeEndDate (dateStr, date) {
      this.model.endDate = moment(date.valueOf() + this.model.dayNum * 24 * 60 * 60 * 1000).format()
    }
  }
}
</script>

<style scoped>
</style>