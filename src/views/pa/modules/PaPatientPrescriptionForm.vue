<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="起床时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="getUpTime">
              <a-time-picker :open.sync="open1" v-model="model.getUpTime" format="HH:mm">
                <a-button slot="addon" size="small" type="primary" @click="handleClose(1)">
                  确定
                </a-button>
              </a-time-picker>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="睡觉时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sleepTime">
              <a-time-picker :open.sync="open2" v-model="model.sleepTime" format="HH:mm">
                <a-button slot="addon" size="small" type="primary" @click="handleClose(2)">
                  确定
                </a-button>
              </a-time-picker>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitDate">
              <j-date v-model="model.limitDate" placeholder="请输入结束日期" date-format='YYYY-MM-DD'></j-date>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="处方时长" :labelCol="labelCol" :wrapperCol="wrapperCol">
              {{ model.sleepTimeLong }}
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { mapGetters } from 'vuex'
  import moment from 'moment'

  export default {
    name: 'PaPatientSchemeForm',
    data() {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        model: {
          getUpTime: null,
          sleepTime: null,
          sleepTimeLong: ''
        },
        open1: false,
        open2: false,
        disabledEdit: true,
        validatorRules: {
          getUpTime: [{ required: true, message: '请选择起床时间!' }],
          sleepTime: [{ required: true, message: '请选择睡觉时间!' }],
        },
        url: {
          edit: '/ta/taDoctorAdviceSleep/edit',
          sublist: '/ta/taDoctorAdviceSleep/list'
        },
        scSchemePage: {},
      }
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
    },
    methods: {
      ...mapGetters(['nickname', 'userInfo']),
      loadData(id) {
        let params = {}//查询条件
        params.patientId = id
        params.pageNo = 1
        params.pageSize = 1
        getAction(this.url.sublist, params).then((res) => {
          if (res.success) {
            if (!res.result.records.length) return
            const result = res.result.records[0]
            this.model = result
            this.model.getUpTime = moment(result.getUpTime, 'HH:mm:ss')
            this.model.sleepTime =  moment(result.sleepTime, 'HH:mm:ss')
          }
        })
      },
      handleClose(type) {
        console.log(this.model.getUpTime)
        console.log(this.model.sleepTime)
        if (type === 1) this.open1 = false
        if (type === 2) this.open2 = false
        if (this.model.getUpTime && this.model.sleepTime) {
          const timeDeff = Date.parse(this.model.getUpTime) / 1000 + 24 * 60 * 60 - Date.parse(this.model.sleepTime) / 1000
          let hour = parseInt(timeDeff / 3600)
          if (hour >= 24) hour = hour - 24
          const minute = (timeDeff % 3600) / 60
          this.model.sleepTimeLong = (hour ? (hour + '小时') : '') + (minute ? (minute + '分钟') : '')
        } else {
          this.model.sleepTimeLong = ''
        }
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let params = JSON.parse(JSON.stringify(this.model))
            delete params.createTime
            delete params.updateTime
            console.log(this.model.limitDate)
            params.getUpTime = this.model.getUpTime.format('HH:mm:ss')
            params.sleepTime = this.model.sleepTime.format('HH:mm:ss')
            params.patientId = this.$store.getters.patientDetail.id
            httpAction(this.url.edit, params, 'put').then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }
        })
      }
    }
  }
</script>

<style scoped>
</style>