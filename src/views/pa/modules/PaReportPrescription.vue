<template>
  <div class="report-prescription">
    <div class="table-operator">
      <a-button @click="editPrescription" type="primary" icon="plus">修改睡眠处方</a-button>
<!--      <a-button @click="rightcolval = 0,paPatientSchemeId=''">关闭</a-button>-->
    </div>
    <a-table
      ref="table"
      size="middle"
      bordered
      rowKey="id"
      class="j-table-force-nowrap"
      :columns="columns1"
      :dataSource="dataSource"
      :pagination="ipagination"
      @change="handleTableChange"
      :loading="loading">
    </a-table>

    <pa-patient-prescription-modal ref="modalPrescription" @ok="loadData1"></pa-patient-prescription-modal>
  </div>
</template>

<script>
import PaPatientPrescriptionModal from './PaPatientPrescriptionModal.vue'
import { filterObj } from '@/utils/util'
import { getAction } from '@/api/manage'

export default {
  name: 'pa-report-prescription',

  components: {
    PaPatientPrescriptionModal,
  },

  props: {
    id: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: {},
      /* 数据源 */
      dataSource:[],
      loading:false,
      /* 排序参数 */
      isorter:{
        column: 'createTime',
        order: 'desc',
      },
      columns1: [
        {
          title: '起床时间',
          align: 'center',
          dataIndex: 'getUpTime'
        },
        {
          title: '睡觉时间',
          align: 'center',
          dataIndex: 'sleepTime'
        },
        {
          title: '处方时长',
          align: 'center',
          dataIndex: 'sleepTimeLong'
        },
        {
          title: '系统推荐起床时间',
          align: 'center',
          dataIndex: 'getUpTimeSys'
        },
        {
          title: '系统推荐睡觉时间',
          align: 'center',
          dataIndex: 'sleepTimeSys'
        },
        {
          title: '结束时间',
          align: 'center',
          dataIndex: 'limitDate'
        },
        {
          title: '更新时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
      ],
      url: {
        list: '/ta/taDoctorAdviceSleep/list',
      },
      paPatientSchemeId: '',
      ipagination:{
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
    }
  },

  mounted() {
    this.loadData1();
  },

  methods: {
    editPrescription() {
      this.$refs.modalPrescription.title="修改睡眠处方"
      this.$refs.modalPrescription.loadData(this.id);
    },

    loadData1(arg) {
      if(!this.url.list){
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams1();//查询条件
      params.patientId = this.$store.getters.patientDetail.id
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records||res.result;
          if(res.result.total)
          {
            this.ipagination.total = res.result.total;
          }else{
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    getQueryParams1() {
      //获取查询条件
      let sqp = {}
      if(this.superQueryParams){
        sqp['superQueryParams']=encodeURI(this.superQueryParams)
        sqp['superQueryMatchType'] = this.superQueryMatchType
      }
      var param = Object.assign(sqp, this.queryParam, this.isorter ,this.filters);
      param.field = this.getQueryField1();
      param.pageNo = this.ipagination.current;
      param.pageSize = this.ipagination.pageSize;
      return filterObj(param);
    },
    getQueryField1() {
      //TODO 字段权限控制
      var str = "id,";
      this.columns1.forEach(function (value) {
        str += "," + value.dataIndex;
      });
      return str;
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      console.log(pagination)
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
      this.loadData1();
    },
  }
}
</script>

<style lang="less">
.report-prescription {
  padding: 0 10px 20px;

  .table-operator {
    display: flex;
    justify-content: space-between;
  }
}
</style>