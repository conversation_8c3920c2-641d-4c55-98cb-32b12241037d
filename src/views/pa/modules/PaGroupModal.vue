<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <pa-group-form ref="realForm" :step="step" @ok="submitCallback" :disabled="disableSubmit"></pa-group-form>
  </j-modal>
</template>

<script>

  import PaGroupForm from './PaGroupForm'
  export default {
    name: 'PaGroupModal',
    props: {
      step: {
        type: Number,
        default: 1
      }
    },
    components: {
      PaGroupForm
    },
    data () {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add(record);
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>