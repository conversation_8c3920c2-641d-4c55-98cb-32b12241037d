<template>
  <div class="appointment-overview">
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :lg="6" :sm="8">
            <a-form-item
              label="月份"
            >
              <a-month-picker
                v-model="queryParam.visitDate"
                format="YYYY-MM"
                placeholder="请选择月份"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <!-- <a-col :lg="6" :sm="8">
            <a-form-item label="患者姓名">
              <a-input placeholder="请输入患者姓名" v-model="queryParam.patientName"></a-input>
            </a-form-item>
          </a-col> -->
          <a-col :lg="6" :sm="8">
            <a-button @click="loadData" type="primary">查询</a-button>
            <a-button style="margin-left: 12px;" @click="reset">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
      <div class="search-status">
        <div class="status-item" v-for="item in statusList" :key="item.id" @click="setStatus(item.id)">
          <div :class="['item-left', 'item-' + item.id]"></div>
          <div :class="['item-text', queryParam.status == item.id && 'item-text-' + item.id]">{{ item.text }}</div>
        </div>
      </div>
    </div>

    <div class="content">
      <a-calendar class="content-calendar" v-model="queryParam.visitDate">
        <template slot="dateCellRender" slot-scope="value">
          <a-popover trigger="click" placement="rightTop">
            <div slot="content" class="pop-content">
              <template v-if="getListData(value) && getListData(value).length">
                <span v-for="item in getListData(value)" :key="item.id + 'content'" :class="['content-item', 'content-item-' + item.status]">
                  {{ item.visitTime }} {{ item.patientName }} {{ statusList[Number(item.status) - 1].text }}
                </span>
              </template>
              <span v-else>无</span>
            </div>
            <div class="event-main">
              <div class="event-item-main">
                <template v-if="getListData(value) && getListData(value).length">
                  <span v-for="item in getListData(value)" :key="item.id + 'event'" :class="['event-item', 'event-item-' + item.status]">{{ item.visitTime }} {{ item.patientName }}</span>
                </template>
                <span v-else>无</span>
              </div>
            </div>
          </a-popover>
        </template>
      </a-calendar>

      <div class="content-right">
        <div class="overview-total">
          <div class="total-title">预约统计</div>
          <div class="total-content">
            <div class="total-item" v-for="item in statusList" :key="item.text">
              <div :class="['item-text', 'item-text-' + item.id]">{{ item.text }}</div>
              <div class="item-num">{{ dataSource.countMap && dataSource.countMap[item.id] || 0 }}</div>
            </div>
          </div>
        </div>

        <div class="overview-list">
          <div class="list-title">我的预约</div>
          <div class="list-content">
            <div class="content-top">
              <span class="top-left">{{ getDay() }}</span>
              <div class="top-right">
                <span>共{{ dataList.length }}场预约</span>
                <p>
                  <a-icon type="caret-down" v-if="!isDown" @click="isDown = true" />
                  <a-icon type="caret-up" v-else @click="isDown = false" />
                </p>
              </div>
            </div>
            <div class="content-bottom">
              <a-timeline>
                <a-timeline-item v-for="(item, index) in dataList" :key="item.id" v-show="isDown || index < 4">
                  <div class="timeline-item" @click="chooseItem(item)">
                    <div class="item-top">
                      <span class="top-date">{{ item.visitDate }}</span>
                      <span :class="['top-status', item.status == '1' ? 'top-status-1' : 'top-status-2']">{{ item.status == '1' ? '待确认' : '已确认' }}</span>
                    </div>
                    <p class="item-name">{{ item.patientName }}</p>
                    <div class="item-time">
                      <a-icon type="clock-circle" />
                      <span class="time-text">{{ item.visitTime }}</span>
                    </div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { getAction } from '@/api/manage'
import moment from 'moment'

export default {
  data() {
    return {
      statusList: [
        {
          id: '1',
          text: '待确认'
        },
        {
          id: '2',
          text: '已确认'
        },
        {
          id: '3',
          text: '已完成'
        },
        {
          id: '4',
          text: '已取消'
        },
        {
          id: '5',
          text: '已逾约'
        }
      ], 
      queryParam: {
        visitDate: moment(new Date(), 'YYYY-MM'),
        patientName: ''
      },
      dataList: [],
      dataSource: {},
      url: {
        list: '/pa/paAppointment/listAll'

      },
      selectPriceDate: '',
      offsetDays: 86400000 * 14, //最多选择7天
      isDown: false
    }
  },

  created() {
    this.loadData()
  },

  methods: {
    getDay(){
      var now = new Date();
      var strLink = "-";
      var year = now.getFullYear();
      var month = now.getMonth() + 1;
      var day = now.getDate();
      if (month >= 1 && month <= 9) {
          month = "0" + month;
      }
      if (day >= 1 && day <= 9) {
          day = "0" + day;
      }
      var firstDate = year + strLink + month + strLink + '01';
      // 取当年当月对应的下个月的前一天，即当前月的最后一天
      var lastDay = new Date(year, month, 0).getDate();
      var lastDate = year + strLink + month + strLink + lastDay;
      return firstDate + ' ~ ' + lastDate
    },

    setStatus(value) {
      if (value === this.queryParam.status) {
        this.queryParam.status = ''
      } else {
        this.queryParam.status = value
      }
      
      this.queryParam.pageNo = 1
      this.loadData()
    },

    reset() {
      this.queryParam.visitDate = null
      this.queryParam.patientName = ''
      this.queryParam.pageNo = 1
      this.loadData()
    },

    chooseItem(item) {
      this.$emit('setTab', item)
    },

    getListData(value) {
      if (!this.dataSource.paAppointmentList || !this.dataSource.paAppointmentList.length) return
      const dataStr = value.format('YYYY-MM-DD')
      const list = this.dataSource.paAppointmentList.filter(item => item.visitDate == dataStr)
      return list
    },

    //选择完时间 清空限制
    changePriceRangeDate() {
      this.selectPriceDate = ''
    },
    //选择开始时间/结束时间
    calendarPriceRangeChange(date){
      this.selectPriceDate = date[0]
    },
    //根据选择的开始时间/结束时间，动态渲染要禁用的日期
    disabledDate(current){
      if(this.selectPriceDate){
        let selectV = moment(this.selectPriceDate, 'YYYY-MM-DD').valueOf()
        return current > moment(new Date(selectV + this.offsetDays), 'YYYY-MM-DD') ||
          current < moment(new Date(selectV - this.offsetDays), 'YYYY-MM-DD')
      }else{
        return false
      }
    },

    getWeek(date) {
      const datelist = ['星期日','星期一','星期二','星期三','星期四','星期五','星期六']
      return datelist[new Date(date).getDay()]
    },

    loadData() {
      //加载数据 若传入参数1则加载第一页的内容
      var params = JSON.parse(JSON.stringify(this.queryParam));//查询条件
      if (params.visitDate) {
        params.visitDate = params.visitDate.slice(0, 10)
      }
      
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result;
          this.dataList = res.result.paAppointmentList.filter(item => item.status == '1' || item.status == '2')
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    handleDetail:function(record){
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title="数据详情";
      this.$refs.modalForm.disableSubmit = true;
    },

    handleNapDetail:function(record){
      this.$refs.napFrom.edit(record);
      this.$refs.napFrom.title="小睡详情";
      this.$refs.napFrom.disableSubmit = true;
    },
  }
}
</script>

<style lang="less">
.pop-content {
  display: flex;
  flex-direction: column;

  .content-item {
    position: relative;
    display: flex;
    padding: 4px 0 4px 8px;

    &::after {
      content: '';
      width: 4px;
      height: 14px;
      position: absolute;
      top: 8px;
      left: 0;
    }
  }

  .content-item-1 {
    &::after {
      background: rgba(15, 64, 245, 0.86);
    }
  }

  .content-item-2 {
    &::after {
      background: rgb(211, 170, 8);
    }
  }

  .content-item-3 {
    &::after {
      background: rgb(100, 163, 6);
    }
  }

  .content-item-4 {
    &::after {
      background: rgba(187, 187, 187, 0.71);
    }
  }

  .content-item-5 {
    &::after {
      background: rgb(189, 49, 36);
    }
  }
}

.appointment-overview {
  .ant-fullcalendar-header {
    display: none;
  }
  .search {
    display: flex;
    justify-content: space-between;
  }

  .search-status {
    display: flex;
    align-items: center;

    .status-item {
      display: flex;
      align-items: center;
      padding-right: 15px;
      cursor: pointer;

      .item-left {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

      .item-1 {
        background: rgba(15, 64, 245, 0.86);
      }

      .item-2 {
        background: rgb(211, 170, 8);
      }

      .item-3 {
        background: rgb(100, 163, 6);
      }

      .item-4 {
        background: rgba(187, 187, 187, 0.71);
      }

      .item-5 {
        background: rgb(189, 49, 36);
      }

      .item-text-1 {
        color: rgba(15, 64, 245, 0.86);
      }

      .item-text-2 {
        color: rgb(211, 170, 8);
      }

      .item-text-3 {
        color: rgb(100, 163, 6);
      }

      .item-text-4 {
        color: rgba(187, 187, 187, 0.71);
      }

      .item-text-5 {
        color: rgb(189, 49, 36);
      }
    }
  }

  .status {
    padding-top: 12px;
    display: flex;
    align-items: center;

    .item-content {
      display: flex;

      .ant-btn {
        margin-left: 12px;
      }
    }
  }

  .content {
    display: flex;
    margin-top: 20px;
    width: 100%;

    .content-calendar {
      flex: 1;
      border: 1px solid #ccc;
      border-bottom-color: transparent;

      .event-main {
        height: 88px;
        overflow: hidden;
      }

      .event-item-main {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        white-space: nowrap;
        overflow: auto;

        .event-item-1 {
          color: rgba(15, 64, 245, 0.86);
        }

        .event-item-2 {
          color: rgb(211, 170, 8);
        }

        .event-item-3 {
          color: rgb(100, 163, 6);
        }

        .event-item-4 {
          color: rgba(187, 187, 187, 0.71);
        }

        .event-item-5 {
          color: rgb(189, 49, 36);
        }
      }

      .content-item {
        display: flex;
        border-bottom: 1px solid #ccc;

        .item-left {
          padding: 18px 16px;
          border-right: 1px solid #ccc;
          background-color: #eee;

          .text {
            margin: 0;
            text-align: center;
            line-height: 28px;
          }
        }

        .item-right {
          display: flex;
          align-items: center;

          .item-card {
            width: 150px; 
            margin: 12px;
            border: 1px solid #ccc;

            .card-top {
              display: flex;
              justify-content: space-between;
              padding: 0 18px;
              margin: 0;
              line-height: 30px;
              border-bottom: 1px solid #ccc;
              color: #fff;
            }

            .card-default {
              background: #1890ff;
            }

            .card-cancel {
              background: #cecece;
            }

            .card-error {
              background: #cf6a61;
            }

            .card-warn {
              background: #e99d42;
            }

            .card-success {
              background: #98c158;
            }

            .card-bottom {
              text-align: center;
              line-height: 36px;
              margin: 0;
            }
          }
        }
      }
    }

    .content-right {
      padding-left: 12px;
      width: 340px;
      
      .overview-total {
        border: 1px solid #ccc;

        .total-title {
          padding: 0 12px;
          line-height: 40px;
          border-bottom: 1px solid #ccc;
        }

        .total-content {
          .total-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;

            .item-text-1 {
              color: rgba(15, 64, 245, 0.86);
            }

            .item-text-2 {
              color: rgb(211, 170, 8);
            }

            .item-text-3 {
              color: rgb(100, 163, 6);
            }

            .item-text-4 {
              color: rgba(187, 187, 187, 0.71);
            }

            .item-text-5 {
              color: rgb(189, 49, 36);
            }
          }
        }
      }

      .overview-list {
        margin-top: 30px;
        border: 1px solid #ccc;

        .list-title {
          padding: 0 12px;
          line-height: 40px;
          border-bottom: 1px solid #ccc;
        }

        .list-content {
          height: 420px;
          overflow: auto;

          .content-top {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            padding: 8px 12px;

            .top-right {
              display: flex;
              align-items: center;

              p {
                margin: 0;
              }
            }
          }

          .content-bottom {
            padding: 8px 12px;

            .timeline-item {
              padding: 12px;
              border: 1px solid #ccc;

              .item-top {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .top-date {
                  font-weight: 600;
                }

                .top-status {
                  padding: 0 12px;
                  line-height: 24px;
                  font-size: 12px;
                  color: #fff;
                  border-radius: 4px;
                  cursor: pointer;
                }

                .top-status-1 {
                  background: rgba(15, 64, 245, 0.86);
                }

                .top-status-2 {
                  background: rgb(211, 170, 8);
                }
              }

              .item-name {
                padding: 12px 0 8px 0;
                font-weight: 600;
                margin: 0;
              }

              .item-time {
                display: flex;
                align-items: center;

                .time-text {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>