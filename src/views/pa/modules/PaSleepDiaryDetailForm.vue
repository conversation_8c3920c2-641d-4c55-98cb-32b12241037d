<template>
  <a-spin :spinning="confirmLoading">
    <a-table rowKey="project" :columns="columns" :data-source="dataSource" bordered :pagination="false"
              size="small">
      <template slot="title">
        <span class="table-title">{{ title }}</span>
      </template>
    </a-table>
  </a-spin>
</template>

<script>
  import { mapGetters } from 'vuex'
  import {getAction} from '@/api/manage'

  export default {
    name: 'PaSleepDiaryDetailForm',
    data() {
      return {
        confirmLoading: false,
        columns: [
          {
            title: '项目',
            align: 'center',
            width: 200,
            dataIndex: 'project'
          },
          {
            title: '主观',
            align: 'center',
            width: 200,
            dataIndex: 'value'
          }
        ],
        dataSource: [],
        title: '',
        disabled: false,
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    methods: {
      ...mapGetters(['nickname', 'userInfo']),
      edit(value) {
        this.disabled = true
        this.title = value.recordTime
        this.dataSource = [
          {
            project: '卧床时长',
            value: value.bedriddenLength
          },
          {
            project: '夜间睡眠总时长',
            value: value.sleepLength
          },
          {
            project: '夜醒总时长',
            value: value.nightWakeTotalTime + '分钟'
          },
          {
            project: '夜醒次数',
            value: value.nightWakeTimes
          },
          {
            project: '睡眠效率',
            value: value.sleepEfficiency + '%'
          },
          {
            project: '日间小睡时长',
            value: value.napLength ? value.napLength : '0分钟'
          },
          {
            project: '日间小睡次数',
            value: value.naps.length
          },
          {
            project: '睡眠处方',
            value: value.adviceSleep ? value.adviceSleep.sleepTime + ' - ' + value.adviceSleep.getUpTime : ''
          },
          {
            project: '服用药物',
            value: value.medicines && value.medicines.length ? 'M' : ''
          }
        ]
      },
    }
  }
</script>

<style lang="scss" scoped>
.table-title {
  display: block;
  text-align: center;
}
.title-item {
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-select {
    width: 50px;
    margin: 0 8px;
  }
}
</style>