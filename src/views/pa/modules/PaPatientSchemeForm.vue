<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="老师名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorName">
              <a-input v-model="model.doctorName" disabled :defaultValue="nickname()" placeholder="请输入老师名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="!isEdit">
            <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sendByGroup">
              <span slot="label">
                是否按标签下发&nbsp;
                <a-tooltip title="按照学生的标签批量下发医嘱任务">
                  <a-icon type="question-circle-o" />
                </a-tooltip>
              </span>
              <a-radio-group v-model="model.sendByGroup">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="0">否</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.sendByGroup === 0">
            <a-form-model-item label="学生名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
              <j-select-patient v-model="model.patientName" @back="backPatientInfo" :backInfo="true" :disabled="disabledEdit" :isRadio="true"></j-select-patient>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.sendByGroup === 1">
            <a-form-model-item label="学生标签" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="selectedGroups">
              <j-label-select v-model="model.selectedGroups" :multiple="false" @change="changeSelectedGroups"></j-label-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="方案名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="schemeName">
              <j-dict-select-tag v-model="model.schemeGroupId" :disabled="isEdit" placeholder="请选择方案" @input="chengeScheme" dictCode="sc_scheme_group,title,id,del_flag=0" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="治疗天数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dayNum">
              <a-input-number v-model="model.dayNum" disabled placeholder="请输入治疗天数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="startDate">
              <j-date placeholder="请选择开始时间" :disabled="isEdit" v-model="model.startDate" style="width: 100%" @change="changeEndDate" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="endDate">
              <j-date placeholder="请选择结束时间" disabled v-model="model.endDate" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
              <a-textarea :rows="5" v-model="model.remark" :disabled="isEdit" placeholder="请输入备注" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction, getAction } from '@/api/manage'
import { mapGetters } from 'vuex'
import PaPatientPlanModal from './PaPatientPlanModal.vue'
import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'
import moment from 'moment'

export default {
  name: 'PaPatientSchemeForm',
  components: {
    PaPatientPlanModal,
    JLabelSelect
  },
  data () {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      model: {
        startDate: moment(new Date()).format(),
        dayNum: null,
        sendByGroup: 0,
        selectedGroups: null
      },
      disabledEdit: true,
      validatorRules: {},
      url: {
        add: '/pa/paPatientSchemeGroup/add',
        edit: '/pa/paPatientScheme/edit',
        queryById: '/sc/scSchemeGroup/queryById'
      },
      isEdit: false,
    }
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    changeSelectedGroups (value) {
      this.model.selectedGroups = value
    },
    chengeScheme (value) {
      this.model.schemeGroupId = value
      getAction(this.url.queryById, { id: value }).then(res => {
        this.model.dayNum = res.result.dayNum
        this.model.endDate = moment(new Date()).add(this.model.dayNum, 'days').format()
      })
    },
    add () {
      this.modelDefault.doctorId = this.userInfo().id
      this.modelDefault.doctorName = this.userInfo().realname
      this.disabledEdit = false
      this.edit(this.modelDefault)
      this.isEdit = false
    },
    edit (record) {
      this.model = Object.assign({}, record)
      this.model.sendByGroup = 0
      this.visible = true
      this.isEdit = true
    },
    submitForm () {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
            this.model.doctorId = this.userInfo().id;
          } else {
            httpurl += this.url.edit
            method = 'put'
            delete this.model.doctorId
          }

          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    },
    backPatientInfo (info) {
      this.model.patientId = info[0].value
      this.model.patientName = info[0].text
    },
    changeEndDate (dateStr, date) {
      this.model.endDate = moment(date.valueOf() + this.model.dayNum * 24 * 60 * 60 * 1000).format()
    }
  }
}
</script>

<style scoped>
</style>