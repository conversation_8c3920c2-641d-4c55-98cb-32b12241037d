<template>
  <j-modal
    :title="title"
    :width="600"
    :visible="visible"
    :maskClosable="false"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel">
    <pa-patient-prescription-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"/>
  </j-modal>
</template>

<script>
  import PaPatientPrescriptionForm from './PaPatientPrescriptionForm'

  export default {
    name: 'PaPatientPrescriptionModal',
    components: {
      PaPatientPrescriptionForm
    },
    data() {
      return {
        title: '',
        visible: false,
        disableSubmit: false,
      }
    },
    methods:{
      loadData (id) {
        this.isEdit = true
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.loadData(id);

        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>

<style scoped>
</style>