<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <div class="mood-content">
        <p class="content-title">
          忧虑
        </p>
        <p class="content-text">
          {{ model.content }}
        </p>
      </div>

      <div class="mood-content">
        <p class="content-title">
          你是否认为这件事情是难题
        </p>
        <p class="content-text">
          {{ model.isProblem ? '是' : '否' }}
        </p>
      </div>

      <div class="mood-content">
        <p class="content-title">
          是否已有详细计划解决忧虑
        </p>
        <p class="content-text">
          {{ model.isPlan ? '是' : '否' }}
        </p>
      </div>

      <div class="mood-content">
        <p class="content-title">
          需要求助
        </p>
        <p class="content-text">
          {{ model.detailPlan }}
        </p>
      </div>

      <div class="mood-content">
        <p class="content-title">
          提醒时间
        </p>
        <p class="content-text">
          {{ model.reminderTime }}
        </p>
      </div>

      <div class="mood-content">
        <p class="content-title">
          老师备注
        </p>
        <p class="content-text">
          {{ model.doctorNote || '暂无' }}
        </p>
      </div>
    </j-form-container>
  </a-spin>
</template>

<script>
import { mapGetters } from 'vuex'
import PaPatientPlanModal from './PaPatientPlanModal.vue'
import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'
import moment from 'moment'

export default {
  name: 'PaPatientSchemeForm',
  components: {
    PaPatientPlanModal,
    JLabelSelect
  },
  data () {
    return {
      confirmLoading: false,
      model: {
      },
      disabledEdit: true,
      url: {
        add: '/pa/paPatientScheme/add',
        edit: '/pa/paPatientScheme/edit',
        listAll: '/psychology/psMeasure/listAllWithTenants'
      },
      scSchemePage: {},
      isEdit: false,
    }
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    add () {
      this.modelDefault.doctorId = this.userInfo().id
      this.modelDefault.doctorName = this.userInfo().realname
      this.disabledEdit = false
      this.edit(this.modelDefault)
      this.isEdit = false
    },
    edit (record) {
      this.model = Object.assign({}, record)
      this.visible = true
      this.isEdit = true
    },
  }
}
</script>

<style lang="scss" scoped>
.mood-top {
  display: flex;
  align-items: center;

  .mood-img {
    width: 50px;
  }

  .mood-text {
    font-size: 16px;
    font-weight: 600;
    padding-left: 20px;
    margin: 0;
  }

  .mood-label {
    display: flex;
    align-items: center;
    padding-left: 15px;

    .label-item {
      font-size: 12px;
      line-height: 24px;
      padding: 0 8px;
      margin-right: 12px;
      border-radius: 4px;
      color: #fff;
      background-color: cornflowerblue;
    }
  }
}

.mood-content {
  padding-top: 20px;

  .content-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 28px;
  }

  .content-text {
    padding-top: 8px;
    font-size: 12px;
    line-height: 20px;
  }
}
</style>