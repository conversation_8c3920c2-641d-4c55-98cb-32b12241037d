<template>
  <j-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" switchFullscreen @ok="handleOk" @cancel="handleCancel" cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <!-- 预约方式（下拉选择，单选：自己预约、代预约），学生姓名（自己预约是展示登陆人姓名，代预约选择登录人所属下级），电话，咨询师姓名、咨询方式（下拉选择，单选：面对面咨询、线上咨询）、预约日期、预约时间 -->
      <a-form-model ref="form" :model="model" :rules="validatorRules">
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="appointmentMethod" label="预约方式">
          <a-select v-model="model.appointmentMethod" placeholder="请选择预约方式" :disabled="disableSubmit">
            <a-select-option :value="1">自己预约</a-select-option>
            <a-select-option :value="2">咨询师代预约</a-select-option>
            <a-select-option :value="3">老师代预约</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName" label="学生姓名">
          <j-select-patient v-model="model.patientName" @back="backPatientInfo" :backInfo="true"
            :isRadio="true" :disabled="disableSubmit"></j-select-patient>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientPhone" label="电话">
          <a-input placeholder="请输入电话" v-model="model.patientPhone" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorId" label="咨询师姓名">
          <a-select placeholder="请选择咨询师" v-model="model.doctorId" style="width: 100%" :disabled="disableSubmit">
            <a-select-option v-for="item in list" :key="item.id" :value="item.id">{{item.realname}}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="consultationMethod" label="咨询方式">
          <a-select v-model="model.consultationMethod" placeholder="请选择咨询方式" :disabled="disableSubmit">
            <a-select-option :value="1">面对面咨询</a-select-option>
            <a-select-option :value="2">线上咨询</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="visitDate" label="预约日期">
          <a-date-picker valueFormat='YYYY-MM-DD' v-model="model.visitDate" style="width: 100%" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="visitTime" label="预约时间">
          <j-dict-select-tag v-model="model.visitTime" placeholder="请选择时间" dictCode="reservation_time" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="familySituation" label="家庭情况">
          <a-textarea placeholder="请输入家庭情况" v-model="model.familySituation" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="question" label="来询问题"> 
          <j-dict-select-tag v-model="model.question" placeholder="请选择来询问题" dictCode="ask_question" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="consultationPurpose" label="咨询目的">
          <a-textarea placeholder="请输入家庭情况" v-model="model.consultationPurpose" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="majorEvents" label="重大变故">
          <a-textarea placeholder="近期有无重大事情发生" v-model="model.majorEvents" :disabled="disableSubmit" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import moment from "moment"

export default {
  name: "PaConsultAppointmentModal",
  data () {
    return {
      disableSubmit: false,
      title: "操作",
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      validatorRules: {
      },
      url: {
        add: "/pa/paAppointment/add",
        edit: "/pa/paAppointment/edit",
        list: "/sys/user/queryUserByRolelist"
      },
      list: []
    }
  },
  created () {
  },
  methods: {
    add () {
      //初始化默认值
      this.edit({});
    },
    edit (record) {
      this.getList();
      const params = Object.assign({}, record);
      if (params.appointmentTime) params.appointmentTime = moment(params.appointmentTime, 'HH:mm:ss')
      this.model = Object.assign({}, params);
      this.visible = true;
    },
    close () {
      this.$emit('close');
      this.visible = false;
      this.$refs.form.clearValidate();
    },
    getList () {
      getAction(this.url.list, {roleCodes: 'Consultant',pageNo: 1, pageSize: 10000}).then((res) => { 
        this.list = res.result.records
      })
    },
    handleOk () {
      if (this.disableSubmit) {
        this.close()
        return
      }
      const that = this;
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true;
          let httpurl = '';
          let method = '';
          if (!this.model.id) {
            httpurl += this.url.add;
            method = 'post';
          } else {
            httpurl += this.url.edit;
            method = 'put';
          }
          const params = Object.assign({}, this.model)
          httpAction(httpurl, params, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          })
        } else {
          return false;
        }
      })
    },
    handleCancel () {
      this.close()
    },

    backPatientInfo(info, rows) {
      this.model.patientName = info[0].text
      this.model.patientId = info[0].value
      this.model.sysUserId = rows[0].sysUserId
      this.$set(this.model, 'patientPhone', info[0].phone)
      this.$nextTick(() => { 
        this.model.patientPhone = rows[0].telphone
      })
    },
  }
}
</script>

<style lang="less" scoped>
</style>