<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="老师名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorName">
              <a-input v-model="model.doctorName" placeholder="请输入老师名称" disabled></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学生名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
              <a-input v-model="model.patientName" placeholder="请输入学生名称" disabled></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学生电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientPhone">
              <a-input v-model="model.patientPhone" placeholder="请输入学生联系电话"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="就诊日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="visitDate">
              <j-date placeholder="请选择就诊日期" v-model="model.visitDate" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="就诊时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="visitTime">
              <j-time placeholder="请选择就诊时间" v-model="model.visitTime" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学生备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientNote">
              <a-textarea :rows="5" v-model="model.patientNote" placeholder="请输入备注" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction } from '@/api/manage'

export default {
  name: 'PaAppointmentForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/pa/paAppointment/add',
        edit: '/pa/paAppointment/confirm',
        queryById: '/pa/paAppointment/queryById'
      }
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    add () {
      this.edit(this.modelDefault)
    },
    edit (record) {
      this.model = Object.assign({}, record)
      this.visible = true
    },
    submitForm () {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }

      })
    }
  }
}
</script>