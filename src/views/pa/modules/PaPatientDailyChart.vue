<template>
  <a-spin :spinning="confirmLoading">
    <div class="sleepDiary-page">
      <div class="timeHeader">
        <div class="title">请选择日期:</div>
        <j-date placeholder="请选择日期" v-model="currentTime" style="width: 100%" />
      </div>
      <div class="unReport" v-if="!sleepDiary">
        <div class="title">睡眠记录</div>
        <div class="content">
          <div class="time">
            <div class="timebg"></div>
            <div class="time-content">
              <div class="time-label">
                {{ Number(this.currentTime.substr(5, 2)) }}月{{ Number(this.currentTime.substr(8, 2)) }}日
              </div>
              <div class="un-report">暂无记录</div>
            </div>
          </div>
          <div class="record">
            <div class="sleep-time">
              <div class="time-content">
                <img class="icon" src="~@/assets/<EMAIL>" alt="" />
                睡眠时长
              </div>
              <div class="line"></div>
            </div>
            <div class="bedridden-time">
              <div class="time-content">
                <img class="icon" src="~@/assets/<EMAIL>" alt="" />
                卧床时长
              </div>
              <div class="line"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="reportDiary" v-else>
        <div class="score">
          <div class="score-content">睡眠评分：{{ score }}分</div>
          <div class="grade">{{ scoreLabel }}</div>
        </div>
        <div class="analyze">
          <div class="section-header">睡眠分析</div>
          <div class="content">
            <div class="fall_asleep diary">
              <div class="title">入睡花了{{ fall_asleep }}</div>
              <div class="label">{{ fall_asleep_label }}</div>
            </div>
            <div class="weakup_time diary">
              <div class="title">出现了{{ weakup_time }}次夜醒</div>
              <div class="label">{{ weakup_time_label }}</div>
            </div>
            <div class="weakup_style diary">
              <div class="title">起床时感觉{{ weakup_style }}</div>
            </div>
            <div class="sleep_efficiency diary">
              <div class="title">睡眠效率{{ sleep_efficiency }}</div>
              <div class="label">{{ sleep_efficiency_label }}</div>
            </div>
            <div class="napLength diary">
              <div class="title">日间小睡时间{{ nap_length }}</div>
              <div class="label">{{ nap_label }}</div>
            </div>
          </div>
        </div>
        <div class="sleep-record">
          <div class="record-header">
            <div class="title">睡眠记录</div>
            <div class="againButton" @click="againButtonClick()">重填</div>
          </div>
          <div class="content">
            <div class="time">
              <div class="timebg"></div>
              <div class="time-content">
                <div class="time-label">{{ sleep_efficiency }}</div>
                <div class="un-report">睡眠效率</div>
              </div>
            </div>
            <div class="record">
              <div class="sleep-time">
                <div class="time-content">
                  <img class="icon" src="~@/assets/<EMAIL>" alt="" />
                  睡眠时长
                </div>
                <div class="line">{{ sleep_length }}</div>
              </div>
              <div class="bedridden-time">
                <div class="time-content">
                  <img class="icon" src="~@/assets/<EMAIL>" alt="" />
                  卧床时长
                </div>
                <div class="line">{{ bedridden_length }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="sleep-progress">
          <div class="time">
            <div class="willTime">{{ willTime.substr(11, 5) }}</div>
            <div class="doseTime">{{ doseTime.substr(11, 5) }}</div>
            <div class="weakupTime">{{ weakupTime.substr(11, 5) }}</div>
            <div class="getupTime">{{ getupTime.substr(11, 5) }}</div>
          </div>
          <div class="progressTitle">
            <div class="will">睡觉</div>
            <div class="dose">睡着</div>
            <div class="weakup">醒来</div>
            <div class="getup">起床</div>
          </div>
          <div class="progress">
            <div class="willLine line"></div>
            <div class="will-dose"></div>
            <div class="doseLine line"></div>
            <div class="dose-weakup"></div>
            <div class="weakupLine line"></div>
            <div class="weakup-getup"></div>
            <div class="getupLine line"></div>
          </div>
        </div>
        <div class="sleep-diary">
          <div class="diart-mood diary">
            <div class="">睡醒情绪</div>
            <div class="">{{ diary_mood }}</div>
          </div>
          <div class="diary-night_waking diary">
            <div class="">夜醒</div>
            <div class="">{{ diary_night_waking }}</div>
          </div>
          <div class="diary-nap diary">
            <div class="">小睡</div>
            <div class="">{{ diary_nap }}</div>
          </div>
          <div class="diary_drug diary">
            <div class="">服药记录</div>
            <div class="">{{ diary_drug }}</div>
          </div>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import moment, { Moment } from 'moment'

export default {
  name: 'PaSleepDiaryForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      confirmLoading: false,
      sleepDiary: false,
      currentTime: '0000-00-00', // 获取当前时间
      score: '88',
      scoreLabel: '良好',
      //入睡
      fall_asleep: '0',
      fall_asleep_label: '感觉很不错，继续保持',
      //夜醒
      weakup_time: '1',
      weakup_time_label: '这是正常的现象，无需过分担心',
      //起床感觉
      weakup_style: '还可以',
      //睡眠效率
      sleep_efficiency: '99%',
      sleep_efficiency_label: '睡眠效率保持的很好，良好的睡眠效率是睡眠质量的保证~',
      //小睡
      nap_length: '1小时30分钟',
      nap_label: '过多的日间小睡会影响睡眠驱动力的积累，注意控制午睡时间',
      //睡眠时长
      sleep_length: '7小时55分钟',
      //卧床时长
      bedridden_length: '8小时',
      willTime: '23:00',
      doseTime: '23:00',
      weakupTime: '07:00',
      getupTime: '07:00',
      diary_mood: '较好',
      diary_night_waking: '1次，5分钟',
      diary_nap: '1次，30分钟',
      diary_drug: '',
      url: '/pa/paSleepDiary/sleepDiaryDetail',
      record: {}
    }
  },
  computed: {},
  watch: {
    currentTime: {
      immediate: true,
      handler() {
        this.loadDetail(this.record)
      },
    },
  },
  created() {
    this.convertToDate()
  },
  methods: {
    convertToDate() {
      var date = new Date()
      var y = date.getFullYear()
      var m = date.getMonth() + 1
      var d = date.getDate()
      m = m < 10 ? '0' + m : m //月小于10，加0
      d = d < 10 ? '0' + d : d //day小于10，加0
      this.currentTime = y + '-' + m + '-' + d
    },
    loadDetail(record) {
      if(record) {
        this.record = record
      } else {
        return
      }
      if(this.currentTime == '0000-00-00') {
        return
      }
      // this.convertToDate()
      let that = this
      let formData = {
        patientId: record.id,
        recordTime: that.currentTime,
      }
      getAction(this.url, formData).then((result) => {
        if (result.success) {
          that.sleepDiary = true
          that.score = result.result.score
          that.fall_asleep = result.result.fall_asleep
          that.fall_asleep_label = result.result.fall_asleep_label
          that.weakup_time = result.result.weakup_time
          that.weakup_time_label = result.result.weakup_time_label
          that.weakup_style = result.result.weakup_style
          that.sleep_efficiency = result.result.sleep_efficiency
          that.sleep_efficiency_label = result.result.sleep_efficiency_label
          that.nap_length = result.result.nap_length
          that.nap_label = result.result.nap_label
          that.sleep_length = result.result.sleep_length
          that.bedridden_length = result.result.bedridden_length
          that.willTime = result.result.willTime
          that.doseTime = result.result.doseTime
          that.weakupTime = result.result.weakupTime
          that.getupTime = result.result.getupTime
          that.diary_mood = result.result.diary_mood
          that.diary_night_waking = result.result.diary_night_waking
          that.diary_nap = result.result.diary_nap
          that.diary_drug = result.result.diary_drug
        } else {
          that.sleepDiary = false
        }
      })
    },
  },
}
</script>

<style lang="scss">
.sleepDiary-page {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-color: #fbfbfb;

  .timeHeader {
    display: flex;
    width: 100%;
    min-height: 48px;
    background-color: #ffffff;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    flex-direction: row;

    .title {
      width: 100px;
      margin-right: 20px;
    }
  }
}
.reportDiary {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;

  .score {
    display: flex;
    width: 100%;
    min-height: 48px;
    background-color: #ffffff;
    padding: 0 15px;
    margin-top: 8px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .score-content {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #303133;
      line-height: 20px;
    }
    .grade {
      display: flex;
      width: 46px;
      height: 25px;
      background: #7f8fe9;
      border-radius: 2px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
      align-items: center;
      justify-content: center;
    }
  }
  .analyze {
    display: flex;
    width: 100%;
    flex-direction: column;
    background-color: #ffffff;
    padding: 0 15px;
    margin-top: 8px;

    .section-header {
      display: flex;
      width: 100%;
      height: 51px;
      font-size: 14px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #303133;
      line-height: 20px;
      border-bottom: 1px solid #eaeef5;
      align-items: center;
    }
    .content {
      .diary {
        display: flex;
        width: 100%;
        height: 65px;
        flex-direction: column;
        border-bottom: 1px solid #eaeef5;
        justify-content: center;

        &:last-child {
          border-bottom: none;
        }
        .title {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #303133;
          line-height: 20px;
        }
        .label {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8f9399;
          line-height: 17px;
          margin-top: 6px;
        }
      }
    }
  }
  .sleep-record {
    display: flex;
    width: 100%;
    height: 203px;
    flex-direction: column;
    margin-top: 8px;

    .record-header {
      display: flex;
      width: 100%;
      height: 52px;
      background-color: #ffffff;
      border-bottom: 1px solid #eaeef5;
      padding: 0 15px;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 14px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #303133;
        line-height: 20px;
      }
      .againButton {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #7f8fe9;
        line-height: 20px;
      }
    }

    .content {
      display: flex;
      width: 100%;
      height: 162px;
      background-color: #ffffff;

      .time {
        display: flex;
        margin-top: 15px;
        margin-left: 40px;
        position: relative;
        width: 110px;
        height: 110px;
        justify-content: center;
        align-items: center;

        .timebg {
          width: 100%;
          height: 100%;
          background: linear-gradient(312deg, #7f8fe9 0%, #8cc2ff 100%);
          border-radius: 55px;
        }
        .time-content {
          display: flex;
          width: 90px;
          height: 90px;
          background-color: #ffffff;
          border-radius: 45px;
          position: absolute;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .time-label {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #7f8fe9;
            line-height: 20px;
          }
          .un-report {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8f9399;
            line-height: 17px;
            margin-top: 2px;
          }
        }
      }
      .record {
        display: flex;
        width: calc(100% - 150px);
        height: 162px;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .time-content {
          display: flex;
          height: 20px;
          flex-direction: row;
          align-items: center;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8f9399;
          line-height: 20px;

          .icon {
            width: 18px;
            height: 18px;
            margin-right: 5px;
          }
        }

        .line {
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #303133;
          line-height: 20px;
          margin-top: 5px;
          margin-bottom: 9px;
        }
      }
    }
  }
  .sleep-progress {
    display: flex;
    width: 100%;
    height: 155px;
    background: #ffffff;
    margin-top: 8px;
    flex-direction: column;
    padding: 0 15px;
    position: relative;

    .title {
      display: flex;
      width: 100%;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #303133;
      line-height: 20px;
      margin-top: 20px;
      justify-content: center;
    }

    .time {
      display: flex;
      width: 100%;
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      line-height: 17px;
      margin-top: 26px;
      color: #8f9399;

      .willTime {
        width: 61px;
      }
      .doseTime {
        width: calc(50% - 61px);
      }
      .weakupTime {
        width: calc(50% - 61px);
        text-align: right;
      }
      .getupTime {
        color: #7f8fe9;
        width: 61px;
        text-align: right;
      }
    }

    .progressTitle {
      display: flex;
      flex-direction: row;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8f9399;
      line-height: 17px;

      .will {
        width: 61px;
      }
      .dose {
        width: calc(50% - 61px);
      }
      .weakup {
        width: calc(50% - 61px);
        text-align: right;
      }
      .getup {
        color: #7f8fe9;
        width: 61px;
        text-align: right;
      }
    }

    .progress {
      display: flex;
      flex-direction: row;
      width: 100%;
      height: 36px;
      margin-top: 2px;

      .will-dose {
        width: 61px;
        height: 24px;
        margin-top: 12px;
        background: #ffbd3f;
      }
      .dose-weakup {
        width: calc(100% - 126px);
        height: 24px;
        margin-top: 12px;
        background: #f39453;
      }
      .weakup-getup {
        width: 61px;
        height: 24px;
        margin-top: 12px;
        background: #ffbd3f;
      }
      .line {
        width: 1px;
        height: 36px;
        background-color: #eaeef5;
      }
    }
  }
  .sleep-diary {
    display: flex;
    width: 100%;
    height: 192px;
    flex-direction: column;
    background-color: #ffffff;
    margin-top: 8px;
    padding: 0 15px;

    .diary {
      display: flex;
      width: 100%;
      height: 48px;
      border-bottom: 1px solid #eaeef5;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #303133;
      line-height: 20px;
      justify-content: space-between;
      align-items: center;

      &:last-child {
        border-bottom: none;
      }
    }
  }
  .setNotification {
    display: flex;
    height: 44px;
    flex-direction: row;
    justify-content: center;
    margin-top: 8px;
    .notification-label {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8f9399;
      line-height: 17px;
    }
    .notification-set {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #7f8fe9;
      line-height: 17px;
    }
  }
}
.unReport {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;

  .title {
    display: flex;
    width: 100%;
    height: 52px;
    background-color: #ffffff;
    border-bottom: 1px solid #eaeef5;
    margin-top: 8px;
    padding: 0 15px;
    align-items: center;
  }
  .content {
    display: flex;
    width: 100%;
    height: 162px;
    background-color: #ffffff;

    .time {
      display: flex;
      margin-top: 15px;
      margin-left: 40px;
      position: relative;
      width: 110px;
      height: 110px;
      justify-content: center;
      align-items: center;

      .timebg {
        width: 100%;
        height: 100%;
        background: #eaeef5;
        border-radius: 55px;
      }
      .time-content {
        display: flex;
        width: 90px;
        height: 90px;
        background-color: #ffffff;
        border-radius: 45px;
        position: absolute;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .time-label {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #7f8fe9;
          line-height: 20px;
        }
        .un-report {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8f9399;
          line-height: 17px;
          margin-top: 2px;
        }
      }
    }
    .record {
      display: flex;
      width: calc(100% - 150px);
      height: 162px;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .time-content {
        display: flex;
        height: 20px;
        flex-direction: row;
        align-items: center;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8f9399;
        line-height: 20px;

        .icon {
          width: 18px;
          height: 18px;
          margin-right: 5px;
        }
      }

      .line {
        width: 24px;
        min-height: 1px;
        background-color: #000000;
        margin-top: 16px;
        margin-left: 20px;
        margin-bottom: 17px;
      }
    }
  }
  .content-label {
    width: 100%;
    height: 55px;
    background-color: #ffffff;
    padding: 0 15px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #8f9399;
    line-height: 17px;
  }
  .setNotification {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 8px;
    .notification-label {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8f9399;
      line-height: 17px;
    }
    .notification-set {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #7f8fe9;
      line-height: 17px;
    }
  }
  .addSleep {
    display: flex;
    flex: 1;
    position: absolute;
    width: 100%;
    height: 49px;
    bottom: 0;
    background: #7f8fe9;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    align-items: center;
    justify-content: center;
  }
}
</style>