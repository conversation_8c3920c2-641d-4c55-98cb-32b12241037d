<template>
  <j-modal
    :title="title"
    :width="600"
    :visible="visible"
    :maskClosable="true"
    :footer="!isEdit"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel">
    <PaNapDetailForm ref="realForm" />
  </j-modal>
</template>

<script>
  import PaNapDetailForm from './PaNapDetailForm.vue'

  export default {
    name: 'PaSleepDiaryDetailModal',
    components: {
      PaNapDetailForm
    },
    data() {
      return {
        title:'',
        width:800,
        visible: false,
        isEdit: false
      }
    },
    methods:{
      add (value) {
        this.isEdit = false
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add(value);
        })
      },
      edit (value) {
        this.isEdit = true
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(value);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(params, days){
        this.$emit('ok', params, days);
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>

<style scoped>
</style>