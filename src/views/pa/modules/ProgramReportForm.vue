<template>
  <a-drawer
    :title="title"
    :maskClosable="true"
    :width="drawerWidth"
    placement="right"
    :closable="true"
    @close="handleCancel"
    :visible="visible">
    <div v-if="!isEdit">
      <div class="no-print" style="text-align: right;margin-right: 35px;">
        <a-button v-print="'#reportCommonContent'" ghost type="primary" style="margin-right: 10px;">打印</a-button>
        <a-button ghost type="primary" @click="viewEdit">编辑</a-button>
      </div>
      <div id="reportCommonContent">
        <div style="margin: 0 auto;width: 90%;font-family: NSimSun;color: black;" v-if="!datas && detail.report && detail.report.paPatient">
          <h2 style="text-align: center;" align="center">
            <span lang="EN-US" style="font-size: 18.0pt; font-family: 黑体; color: black;">{{ detail.sysOrgName }}</span>
          </h2>
          <h3 style="text-align: center;" align="center">
            <span lang="EN-US">失眠认知行为报告单</span>
          </h3>
          <p style="text-align: right; margin-bottom: 0px; margin-top: 0px;">
            <span style="font-size: 12px; text-align: right; background-color: #ffffff;">编号: {{ detail.report.paPatient.number }}</span>
          </p>
          <table style="border-collapse: collapse; width: 100%; height: 27px; border-style: hidden; margin-left: auto; margin-right: auto; border-bottom: 1px solid black; border-top: 1px solid black;">
            <tbody>
              <tr style="height: 30px; text-align: left;">
                <td style="width: 29.9841%; height: 27px; text-align: left;">
                  <span style="font-size: 14px; text-align: start;">
                    <span style="letter-spacing: 2em; margin-right: -2em;">姓名
                    </span>: {{ detail.report.paPatient.name }}
                  </span>
                </td>
                <td style="width: 24.3917%; height: 27px; text-align: left;">
                  <span style="font-size: 14px; background-color: #ffffff; letter-spacing: 2em; margin-right: -2em;">性别</span>
                  <span lang="EN-US" style="font-size: 14px; background-color: #ffffff;">: {{ detail.report.paPatient.sex == '1' ? '男' : '女' }}</span>
                </td>
                <td style="width: 30.3881%; height: 27px; text-align: left;">
                  <span style="font-size: 14px; background-color: #ffffff; letter-spacing: 2em; margin-right: -2em;">年龄</span>
                  <span lang="EN-US" style="font-size: 14px; background-color: #ffffff;">: {{ detail.report.paPatient.age }}</span>
                </td>
              </tr>

              <tr>
                <td style="width: 29.9841%; text-align: left;">
                  <span style="font-size: 14px; text-align: start;">婚姻状况: {{ detail.report.paPatient.maritalStatus == '1' ? '已婚' : detail.report.paPatient.maritalStatus == '2' ? '未婚' : detail.report.paPatient.maritalStatus == '3' ? '离异' : '丧偶' }}</span>
                </td>
                <td style="width: 24.3917%; text-align: left;">
                  <span style="font-size: 14px; background-color: #ffffff;"><span style="letter-spacing: 2em; margin-right: -2em;">职业</span>: {{ detail.report.paPatient.profession }}</span>
                </td>
                <td style="width: 30.3881%; text-align: left;">
                  <span style="font-size: 14px; background-color: #ffffff;">文化程度: {{ detail.report.paPatient.cultural }}</span>
                </td>
              </tr>
            </tbody>
          </table>

          <div class="report-main" style="padding: 20px 8px">
            <p class="main-title" style="position: relative; font-size: 16px; line-height: 24px; font-weight: 600">方案：{{ detail.schemeGroupName }}</p>

            <div class="report-info" style="padding-bottom: 25px">
              <p class="report-item-title" style="font-size: 14px; font-weight: 600">CBTI课程完成情况：</p>

              <p class="report-item-text" style="font-size: 14px; margin-bottom: 5px">开始时间：{{ detail.report.startDate }}</p>
              <p class="report-item-text" style="font-size: 14px; margin-bottom: 5px">结束时间：{{ detail.report.endDate }}</p>
              <p class="report-item-text" style="font-size: 14px; margin-bottom: 5px">课程完成时间：{{ detail.report.schemeGroupDayNum }}天</p>
              <p class="report-item-text" style="font-size: 14px; margin-bottom: 5px">睡眠日记填写天数：{{ detail.report.sleepDiaryNum }}篇</p>
            </div>

            <div class="report-detail" style="padding-bottom: 25px">
              <p class="report-item-title" style="font-size: 14px; font-weight: 600">睡眠日记情况汇总：</p>

              <div class="report-detail-content" style="display: flex; flex-wrap: wrap; margin: 0 -60px">
                <div class="report-detail-item" style="width: 25%; text-align: center; padding: 10px 0;">
                  <p class="item-time" style="font-weight: 600; margin-bottom: 5px; font-size: 14px;">{{ detail.report.sleepDiaryReport.avgBedTime }}</p>
                  <p class="item-text" style="margin-bottom: 5px; font-size: 14px;">平均卧床时间</p>
                </div>
                <div class="report-detail-item" style="width: 25%; text-align: center; padding: 10px 0;">
                  <p class="item-time" style="font-weight: 600; margin-bottom: 5px; font-size: 14px;">{{ detail.report.sleepDiaryReport.avgSleepTime }}</p>
                  <p class="item-text" style="margin-bottom: 5px; font-size: 14px;">平均睡眠总时长</p>
                </div>
                <div class="report-detail-item" style="width: 25%; text-align: center; padding: 10px 0;">
                  <p class="item-time" style="font-weight: 600; margin-bottom: 5px; font-size: 14px;">{{ detail.report.sleepDiaryReport.avgNightWakeTime }}</p>
                  <p class="item-text" style="margin-bottom: 5px; font-size: 14px;">平均夜醒时长</p>
                </div>
                <div class="report-detail-item" style="width: 25%; text-align: center; padding: 10px 0;">
                  <p class="item-time" style="font-weight: 600; margin-bottom: 5px; font-size: 14px;">{{ detail.report.sleepDiaryReport.sleepEfficiency }}</p>
                  <p class="item-text" style="margin-bottom: 5px; font-size: 14px;">平均睡眠效率</p>
                </div>
                <div class="report-detail-item" style="width: 25%; text-align: center; padding: 10px 0;">
                  <p class="item-time" style="font-weight: 600; margin-bottom: 5px; font-size: 14px;">{{ detail.report.sleepDiaryReport.avgNightWakeNum }}次</p>
                  <p class="item-text" style="margin-bottom: 5px; font-size: 14px;">平均夜醒次数</p>
                </div>
                <div class="report-detail-item" style="width: 25%; text-align: center; padding: 10px 0;">
                  <p class="item-time" style="font-weight: 600; margin-bottom: 5px; font-size: 14px;">{{ detail.report.sleepDiaryReport.avgGoToSleepTime }}</p>
                  <p class="item-text" style="margin-bottom: 5px; font-size: 14px;">平均入睡时长</p>
                </div>
              </div>
            </div>

            <!-- 睡眠时长表 -->
            <div id="sleepChart" class="report-sleep-chart1" ref="sleepChart"></div>

            <!-- 入睡时长 -->
            <div class="report-long-sleep">
              <div class="sleep-tilte" style="display: flex; justify-content: space-between;">
                <p class="title-item" style="font-size: 14px;">最长入睡时长：{{ detail.report.sleepDiaryReport.maxGoToSleepMinuteTime }}分钟</p>
                <p class="title-item" style="font-size: 14px;">最短入睡时长：{{ detail.report.sleepDiaryReport.minGoToSleepMinuteTime }}分钟</p>
                <p class="title-item" style="font-size: 14px;">平均入睡时长：{{ detail.report.sleepDiaryReport.avgGoToSleepTime }}分钟</p>
              </div>

              <div id="longSleep" class="report-sleep-chart" ref="longSleep"></div>
            </div>

            <!-- 夜醒次数 -->
            <div class="report-wake">
              <div class="sleep-tilte" style="display: flex; justify-content: space-between;">
                <p class="title-item" style="font-size: 14px;">最多夜醒次数：{{ detail.report.sleepDiaryReport.maxNightWakeNum }}次</p>
                <p class="title-item" style="font-size: 14px;">最少夜醒次数：{{ detail.report.sleepDiaryReport.minNightWakeNum }}次</p>
                <p class="title-item" style="font-size: 14px;">平均夜醒次数：{{ detail.report.sleepDiaryReport.avgNightWakeNum }}次</p>
              </div>

              <div id="wakeSleep" class="report-sleep-chart" ref="wakeSleep"></div>
            </div>

            <!-- 夜醒时长 -->
            <div class="report-long-wake">
              <div class="sleep-tilte" style="display: flex; justify-content: space-between;">
                <p class="title-item" style="font-size: 14px;">最长夜醒时长：{{ detail.report.sleepDiaryReport.maxNightWakeMinuteTime }}分钟</p>
                <p class="title-item" style="font-size: 14px;">最短夜醒时长：{{ detail.report.sleepDiaryReport.minNightWakeMinuteTime }}分钟</p>
                <p class="title-item" style="font-size: 14px;">平均夜醒时长：{{ detail.report.sleepDiaryReport.avgNightWakeTime }}分钟</p>
              </div>

              <div id="longWake" class="report-sleep-chart" ref="longWake"></div>
            </div>

            <!-- 睡眠变化 -->
            <div class="report-sleep" style="padding-bottom: 25px">
              <p class="report-item-title" style="font-size: 14px; font-weight: 600">睡眠变化：</p>

              <div class="report-sleep-content" style="display: flex; justify-content: space-between;">
                <div class="report-sleep-item" style="width: 200px; padding: 10px 15px; border: 1px solid #999; border-radius: 8px; text-align: center;">
                  <p class="item-time" style="font-size: 18px; font-weight: 500; line-height: 30px; margin: 0;">{{ detail.report.sleepDiaryReport.changeAvgSleepMinuteTimeStr }}</p>
                  <p class="item-text" style="font-size: 14px; line-height: 24px; margin: 0;">平均睡眠时长</p>
                </div>
                <div class="report-sleep-item" style="width: 200px; padding: 10px 15px; border: 1px solid #999; border-radius: 8px; text-align: center;">
                  <p class="item-time" style="font-size: 18px; font-weight: 500; line-height: 30px; margin: 0;">{{ detail.report.sleepDiaryReport.changeAvgBedMinuteTimeStr }}</p>
                  <p class="item-text" style="font-size: 14px; line-height: 24px; margin: 0;">平均卧床时间</p>
                </div>
                <div class="report-sleep-item" style="width: 200px; padding: 10px 15px; border: 1px solid #999; border-radius: 8px; text-align: center;">
                  <p class="item-time" style="font-size: 18px; font-weight: 500; line-height: 30px; margin: 0;">{{ detail.report.sleepDiaryReport.changeSleepEfficiencyStr }}</p>
                  <p class="item-text" style="font-size: 14px; line-height: 24px; margin: 0;">平均睡眠效率</p>
                </div>
              </div>
            </div>

            <!-- 量表变化 -->
            <div class="report-scale" style="padding-bottom: 25px">
              <p class="report-item-title" style="font-size: 14px; font-weight: 600">量表变化：</p>

              <div id="scale" class="report-sleep-chart" ref="scale"></div>
            </div>

            <!-- 问卷 -->
            <div class="report-questionnaire" style="border: 0.5px solid #999; margin-bottom: 25px;">
              <div class="questionnaire-title" style="display: flex;">
                <p class="questionnaire-item1" style="flex: 2; border: 0.5px solid #999; margin: 0; padding: 8px 0; text-align: center; font-size: 14px;">问卷</p>
                <p class="questionnaire-item2" style="flex: 1; border: 0.5px solid #999; margin: 0; padding: 8px 0; text-align: center; font-size: 14px;">答题时间</p>
                <p class="questionnaire-item2" style="flex: 1; border: 0.5px solid #999; margin: 0; padding: 8px 0; text-align: center; font-size: 14px;">得分</p>
                <p class="questionnaire-item2" style="flex: 1; border: 0.5px solid #999; margin: 0; padding: 8px 0; text-align: center; font-size: 14px;">结果</p>
              </div>

              <div class="questionnaire-main" v-for="item in measureResultList" :key="item.schemeNum">
                <div class="questionnaire-main-top" style="border: 0.5px solid #999; margin: 0; padding: 8px 0; text-align: center;">第{{ schemeNumText[Number(item.schemeNum - 1)] }}周</div>
                <div class="questionnaire-main-item" style="display: flex;" v-for="it in item.data" :key="it.id">
                  <p class="questionnaire-item1" style="flex: 2; border: 0.5px solid #999; margin: 0; padding: 8px 0; text-align: center; font-size: 14px;">{{ it.measureName }}</p>
                  <p class="questionnaire-item2" style="flex: 1; border: 0.5px solid #999; margin: 0; padding: 8px 0; text-align: center; font-size: 14px;">{{ it.answerDate }}</p>
                  <p class="questionnaire-item2" style="flex: 1; border: 0.5px solid #999; margin: 0; padding: 8px 0; text-align: center; font-size: 14px;">{{ it.totalPoints }}分</p>
                  <p class="questionnaire-item2" style="flex: 1; border: 0.5px solid #999; margin: 0; padding: 8px 0; text-align: center; font-size: 14px;">{{ it.degreeStr }}</p>
                </div>
              </div>
            </div>

            <!-- 总结 -->
            <div class="report-summary" style="padding-bottom: 25px">
              <p class="report-item-title" style="font-size: 14px; font-weight: 600">CBTI治疗总结：</p>
              <p class="report-item-title2" style="font-size: 14px; margin-bottom: 5px;">通过对比第一周与最后一周的睡眠数据，可以得出一下结论：</p>
              
              <p class="report-summary-item" style="font-size: 14px; margin-bottom: 5px;" v-for="(item, index) in detail.report.summary" :key="index">{{ index + 1 }}、{{ item }}</p>
            </div>

            <!-- 提示 -->
            <div class="report-hint" style="padding-bottom: 25px">
              <p class="report-item-title" style="font-size: 14px; font-weight: 600">温馨提示：</p>
              
              <p class="report-hint-main" style="font-size: 14px;">如果失眠的情况已经持续了 3 个月，并且每周多于 3 次，那么我们需要重新执行之前学过的策略，重启 CBTI 课程能有效帮助我们摆脱失眠困扰。 你明白，当你重新填写睡眠日记的时候就是你打败失眠的开端。 用科学解密睡眠，让睡眠变得轻松！祝您从此拥有良好的睡眠。</p>
            </div>

            <table style="border-collapse: collapse; width: 100%;">
              <tbody>
                <tr>
                  <td style="width: 34.4262%; border-bottom: 1px solid black;">&nbsp;</td>
                  <td style="width: 6%; border-bottom: 1px solid black;"><span style="font-size: 14px; text-align: right;">签字:</span></td><td style="width: 28%;border-bottom: 1px solid black;"><img :src="getAvatarView()" style="width: 80px;height: 50px;text-align: left;"></td>
                  <td style="width: 32.5138%; border-bottom: 1px solid black;"><span style="font-size: 14px; text-align: right;">日期: {{ detail.updateTime }}</span></td>
                </tr>
              </tbody>
            </table>
            <p class="MsoNormal" style="text-align: right; margin-bottom: 0px; margin-top: 0px;" align="center"><span style="font-size: 14px; text-align: right;">（本报告只作为临床参考）</span></p>
            <p class="MsoNormal" style="text-align: center;" align="center"><span lang="EN-US" style="display: none; mso-hide: all;">&nbsp;</span></p>
          </div>
        </div>
        <p v-if="datas" v-html="datas"></p>
      </div>
    </div>
    <div v-if="isEdit">
      <a-spin :spinning="loading">
        <a-row>
          <j-editor :j-height="700" v-model="editContent"/>
        </a-row>
        <a-row style="margin-top: 15px;">
          <a-col :span="18"></a-col>
          <a-col :span="6">
            <a-button ghost type="primary" @click="saveResport" style="margin-right: 10px;">确定</a-button>
            <a-button ghost type="primary" @click="cancelResport">取消</a-button>
          </a-col>
        </a-row>
      </a-spin>
    </div>
  </a-drawer>
</template>

<script>
  import { getAction } from '@api/manage'
  import Vue from 'vue'
  import JEditor from '@comp/jeecg/JEditor.vue'
  import {USER_INFO} from '@/store/mutation-types'
  import {getFileAccessHttpUrl} from '@api/manage'
  import * as echarts from 'echarts/core'
  import { BarChart, LineChart, ScatterChart} from 'echarts/charts'
  import { TitleComponent, TooltipComponent, GridComponent, ToolboxComponent, LegendComponent } from 'echarts/components'
  import { CanvasRenderer } from 'echarts/renderers'
  import moment from 'moment'

  // 注册必须的组件
  echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    ToolboxComponent,
    LegendComponent,
    BarChart,
    LineChart,
    ScatterChart,
    CanvasRenderer,
  ])

  export default {
    name: 'ProgramReportForm',
    components: {
      JEditor
    },
    data() {
      return {
        datas: '',
        schemeNumText: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'],
        detail: {},
        title: '操作',
        loading: false,
        drawerWidth: 700,
        visible: false,
        isEdit: false,
        editContent: '',
        sleepCharts: null,
        longSleepCharts: null,
        wakeSleepCharts: null,
        longWakeCharts: null,
        scaleCharts: null,
        measureResultList: [],
        measureList: [],
        xAxisSleep: [],
        leftX: ['10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00', '00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00'],
        url: {
          getPrintInfoByResultId: '/diagnosis/dxResult/getPrintInfoByResultId',
          generatePrintHtml: '/pa/paPatientSchemeGroup/generatePrintHtml/'
        }
      }
    },
    methods: {
      getAvatarView() {
        if (this.detail.doctorSignaturePath) {
          return getFileAccessHttpUrl(this.detail.doctorSignaturePath)
        }
      },
      handleCancel() {
        this.close()
      },
      edit(record) {
        this.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
        this.visible = true
        this.detail = record
        this.xAxisSleep = []
        let goToSleepTimeList = []
        let nightWakeTimesList = []
        let nightWakeTotalTimeList = []
        let placeholder = [] // 空白
        let sleepList = [] // 睡眠
        let wakeList1 = [] // 醒来清醒
        let wakeList2 = [] // 睡前清醒
        let efficiency = [] // 效率
        let medicineList = [] // 服药
        record.report.sleepDiaryReport.sleepDiaryList.forEach((item, index) => {
          this.xAxisSleep.push(item.recordTime)
          goToSleepTimeList.push(item.goToSleepTime)
          nightWakeTimesList.push(item.nightWakeTimes)
          nightWakeTotalTimeList.push(item.nightWakeTotalTime)
          let startDateTamp = moment(item.recordTime + ' 10:00:00').unix() - 24 * 60 * 60 // 昨天10点 图的起点
          let nightPrepareSleepDateTamp = moment(item.nightPrepareSleepDate).unix() // 上床时间
          let nightAsleepDateTamp = moment(item.nightAsleepDate).unix() // 睡着时间
          let wakeDateTamp = moment(item.wakeDate).unix() // 醒来时间
          let getUpDateTamp = moment(item.getUpDate).unix() // 起床时间

          placeholder.push(Math.round((nightPrepareSleepDateTamp - startDateTamp) * 100 / 3600))
          if (item.goToSleepTime) {
            sleepList.push(Math.round((wakeDateTamp - nightAsleepDateTamp) * 100 / 3600))
          } else {
            sleepList.push('-')
          }

          if (nightAsleepDateTamp) {
            wakeList1.push(Math.round((nightAsleepDateTamp - nightPrepareSleepDateTamp) * 100 / 3600))
            wakeList2.push(Math.round((getUpDateTamp - wakeDateTamp) * 100 / 3600))
          } else {
            wakeList1.push(Math.round((getUpDateTamp - nightPrepareSleepDateTamp) * 100 / 3600))
          }

          if (item.eatMedicine) {
            medicineList.push([index, 2900])
          }
          efficiency.push(item.sleepEfficiency)          
        })
        this.$nextTick(() => {
          this.setSleep(this.xAxisSleep, placeholder, sleepList, wakeList1, wakeList2, efficiency, medicineList)
          this.setLongSleep(this.xAxisSleep, goToSleepTimeList)
          this.setWake(this.xAxisSleep, nightWakeTimesList)
          this.setLongWake(this.xAxisSleep, nightWakeTotalTimeList)
        })

        let map = {}
        for (let i = 0; i < record.report.measureResultList.length; i++) {
          let ai = record.report.measureResultList[i]
          if (!map[ai.schemeNum]) {
            map[ai.schemeNum] = [ai]
          } else {
            map[ai.schemeNum].push(ai)
          }
        }
        this.measureResultList = []
        Object.keys(map).forEach(key => {
          this.measureResultList.push({
            schemeNum: key,
            data: map[key],
          })
        })

        let map2 = {}
        for (let i = 0; i < record.report.measureResultList.length; i++) {
          let ai = record.report.measureResultList[i]
          if (!map2[ai.measureId]) {
            map2[ai.measureId] = [ai]
          } else {
            map2[ai.measureId].push(ai)
          }
        }
        this.measureList = []
        Object.keys(map2).forEach(key => {
          this.measureList.push({
            measureId: key,
            measureName: map2[key][0].measureName,
            data: map2[key],
          })
        })
        this.$nextTick(() => {
          this.setScale(this.xAxisSleep)
        })
        // that.generatePrintHtml(record.id)
      },
      setSleep(xAxisSleep, placeholder, sleepList, wakeList1, wakeList2, efficiency, medicineList) {
        // console.log('this.measureResultList========', efficiency)
        let option = {
          color:['#FFCE7B','#6595F4','#35cd3d'],
          legend: {
            left: 'center',
            itemWidth: 15,
            itemHeight: 15,
            data: [
              {icon: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABBZJREFUWEfVlm1MW1UYx//ntowxWAfoOnCZ1chMJEsTNkWmaSVSqB+6xEDL2AcNC/HbdG5OpjHKiLpMY1Q0mTGZme9sbaHJqIaOoqybIxLMskUDus1RWJDdJdD20tKX23vMJelkfaH3lg/E+6k5fc75/c7znPucS7DKD1llPv7/AjuNltI8XtgFoGpN/poGno9vYRjmIs/zFwkhV84N2BzLZXlFGdDVmVtKilUf+vyBchGi3ngPSkuLMeG9iXA4ssgtXLfuChfhnr/gclxOJ5KzgKlxry0Q4MxFRYVofc4C3ROPoaxMfYdx/W8vHGf60feDOzF2xOO2dyZL5CRgatzbHwhwRt2T1YvwioceyJjlX0cuof2No4v/x0E0v7htk0uDZQsk4LX6neh886Ckl+jGxBRaXzgIQphT5wase3IWyAWegHUd/xq9jj5QMObzbmtPYlxyBlYCF2Gjl8fwyqG3AIr3PIP212QJmOqb+wNUMMpJe3Jt/PNhWHa3IR6Pe35ynX5KssAduLocQ+w/i3UXReQ+rC+I9vYOTHqnQj+ftRZKElgK79Rux9DVcXR4r+ckMcX6sf+lV8Fx82PuH7srswokw8GyAHsbQ+EgOny3ZUmEwjGMX/PiwIHXAUq7PIM9Ly8rYGpotgUEwVyrLoe48wQ8MUmOhCBQsHPzOON04btvT4sCrZ7Bnq8yCojtlRB069RleEe7IwUuV8LHLeD3sWt49+gHyM9TsizHPTLsss2mFRAvlk1QjEUJUXftqEFFJAbMzGQ8b9ky4Q+GIZ7+ro+PY3T0EijFnvOD9lMZG5G+ztIGQk/se7gSlo3lwI0JQIgve+AzSSTgn37yOUZGRqFSrbc7e09alr0L9PVNJ5Rg2nr0BhRP3wK4gKS3LVkiDdzl7D35TNbb0FBv+WvbhpKtH1VVA+N/igdGkoAYlJA43P4itmm1WLLzjHBx3l2tuNZgCTRt0azft1kDTHglwxOB7tk5vB31o7r60UTal4WnCNQ1WPo0BYWmLyoqgVusLIGoL4SIP4RjyjAuKHix5lnhKQL6evNhUBz7prIK98/OSRZIgRPG5Rywpq15lkNobgCFa5e6DIeYtZIEVgJPyYA40FzX/NsMEba/X7IJj+cXZJSgAoUIj3IL/6Vdxs4zd0JjYzmJM9NiwJf3bsaDyry7JSgFvxBD1B/CVT6KbkUUwwwPVQ7wtBkQB/UGcweAI+LvFlKApxVrF0Xi0RjiER4+KmCI4fG9MoogKFQMY3eetaY0GSk1zPhFVFv7bPF9RRs+uxkOtogLaaCANs5gkqH4g/DgAZQQws4KdH9ye5UCzliC5Mk6Q5ORgNQoQXQUtEZByHQUggcCMxxTwrH0YpEDliyQy6Jy5kj+KJWzqJzYVRf4F7lMDD9LsopTAAAAAElFTkSuQmCC', name: '药物'},
              {name: '清醒'},
              {name: '睡眠'},
              {name: '效率'}
            ]
          },
          xAxis: {
            type: 'category',
            data: xAxisSleep
          },
          yAxis: [
            {
              type: 'value',
              min: 0,
              max: 2900,
              splitNumber: 15,
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: true,
                // 这里重新定义就可以
                formatter: (value) => {
                  var texts = []
                  const index = value / 100
                  texts.push(this.leftX[index - 1])
                  return texts
                }
              },
            },
            {
              type: 'value',
              min: 0,
              max: 100,
              splitLine: {
                show: false,
              },
              interval: Math.round(100 / 5),
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          series: [
            {
              name: 'Placeholder',
              type: 'bar',
              stack: 'Total',
              silent: true,
              itemStyle: {
                borderColor: 'transparent',
                color: 'transparent'
              },
              emphasis: {
                itemStyle: {
                  borderColor: 'transparent',
                  color: 'transparent'
                }
              },
              data: placeholder
            },
            {
              name: '清醒',
              type: 'bar',
              stack: 'Total',
              data: wakeList2
            },
            {
              name: '睡眠',
              type: 'bar',
              stack: 'Total',
              data: sleepList
            },
            {
              name: '清醒',
              type: 'bar',
              stack: 'Total',
              data: wakeList1
            },
            {
              name: '效率',
              type: 'line',
              yAxisIndex: 1,
              data: efficiency
            },
            {
              name: '药物',
              symbolSize: 20,
              data: medicineList,
              symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABBZJREFUWEfVlm1MW1UYx//ntowxWAfoOnCZ1chMJEsTNkWmaSVSqB+6xEDL2AcNC/HbdG5OpjHKiLpMY1Q0mTGZme9sbaHJqIaOoqybIxLMskUDus1RWJDdJdD20tKX23vMJelkfaH3lg/E+6k5fc75/c7znPucS7DKD1llPv7/AjuNltI8XtgFoGpN/poGno9vYRjmIs/zFwkhV84N2BzLZXlFGdDVmVtKilUf+vyBchGi3ngPSkuLMeG9iXA4ssgtXLfuChfhnr/gclxOJ5KzgKlxry0Q4MxFRYVofc4C3ROPoaxMfYdx/W8vHGf60feDOzF2xOO2dyZL5CRgatzbHwhwRt2T1YvwioceyJjlX0cuof2No4v/x0E0v7htk0uDZQsk4LX6neh886Ckl+jGxBRaXzgIQphT5wase3IWyAWegHUd/xq9jj5QMObzbmtPYlxyBlYCF2Gjl8fwyqG3AIr3PIP212QJmOqb+wNUMMpJe3Jt/PNhWHa3IR6Pe35ynX5KssAduLocQ+w/i3UXReQ+rC+I9vYOTHqnQj+ftRZKElgK79Rux9DVcXR4r+ckMcX6sf+lV8Fx82PuH7srswokw8GyAHsbQ+EgOny3ZUmEwjGMX/PiwIHXAUq7PIM9Ly8rYGpotgUEwVyrLoe48wQ8MUmOhCBQsHPzOON04btvT4sCrZ7Bnq8yCojtlRB069RleEe7IwUuV8LHLeD3sWt49+gHyM9TsizHPTLsss2mFRAvlk1QjEUJUXftqEFFJAbMzGQ8b9ky4Q+GIZ7+ro+PY3T0EijFnvOD9lMZG5G+ztIGQk/se7gSlo3lwI0JQIgve+AzSSTgn37yOUZGRqFSrbc7e09alr0L9PVNJ5Rg2nr0BhRP3wK4gKS3LVkiDdzl7D35TNbb0FBv+WvbhpKtH1VVA+N/igdGkoAYlJA43P4itmm1WLLzjHBx3l2tuNZgCTRt0azft1kDTHglwxOB7tk5vB31o7r60UTal4WnCNQ1WPo0BYWmLyoqgVusLIGoL4SIP4RjyjAuKHix5lnhKQL6evNhUBz7prIK98/OSRZIgRPG5Rywpq15lkNobgCFa5e6DIeYtZIEVgJPyYA40FzX/NsMEba/X7IJj+cXZJSgAoUIj3IL/6Vdxs4zd0JjYzmJM9NiwJf3bsaDyry7JSgFvxBD1B/CVT6KbkUUwwwPVQ7wtBkQB/UGcweAI+LvFlKApxVrF0Xi0RjiER4+KmCI4fG9MoogKFQMY3eetaY0GSk1zPhFVFv7bPF9RRs+uxkOtogLaaCANs5gkqH4g/DgAZQQws4KdH9ye5UCzliC5Mk6Q5ORgNQoQXQUtEZByHQUggcCMxxTwrH0YpEDliyQy6Jy5kj+KJWzqJzYVRf4F7lMDD9LsopTAAAAAElFTkSuQmCC',
              type: 'scatter'
            }
          ]
        }

        let sleepDom = this.$refs.sleepChart
        this.sleepCharts = echarts.init(sleepDom)
        this.sleepCharts.setOption(option)
      },
      setLongSleep(xAxisSleep, data) {
        let obj = {
          xAxis: [
            {
              type: 'category',
              data: xAxisSleep,
              axisPointer: {
                type: 'shadow',
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              name: '分钟',
              axisLabel: {
                formatter: '{value} 分钟',
              },
              splitLine: {
                show: false,
              },
            }
          ],
          series: [
            {
              type: 'line',
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' 分钟'
                },
              },
              data: data,
            },
          ]
        }

        let sleepDom = this.$refs.longSleep
        this.longSleepCharts = echarts.init(sleepDom)
        this.longSleepCharts.setOption(obj)
      },
      setWake(xAxisSleep, data) {
        let obj = {
          xAxis: [
            {
              type: 'category',
              data: xAxisSleep,
              axisPointer: {
                type: 'shadow',
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              name: '次数',
              axisLabel: {
                formatter: '{value} 次',
              },
              splitLine: {
                show: false,
              },
            }
          ],
          series: [
            {
              type: 'line',
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' 次'
                },
              },
              data: data,
            },
          ]
        }

        let sleepDom = this.$refs.wakeSleep
        this.wakeSleepCharts = echarts.init(sleepDom)
        this.wakeSleepCharts.setOption(obj)
      },
      setLongWake(xAxisSleep, data) {
        let obj = {
          xAxis: [
            {
              type: 'category',
              data: xAxisSleep,
              axisPointer: {
                type: 'shadow',
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              name: '分钟',
              axisLabel: {
                formatter: '{value} 分钟',
              },
              splitLine: {
                show: false,
              },
            }
          ],
          series: [
            {
              type: 'line',
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' 分钟'
                },
              },
              data: data,
            },
          ]
        }

        let sleepDom = this.$refs.longWake
        this.longWakeCharts = echarts.init(sleepDom)
        this.longWakeCharts.setOption(obj)
      },
      setScale(xAxisSleep) {
        const legends = this.measureList.map(item => item.measureName)
        let dataList = []
        for(let i = 0; i < legends.length; i++) {
          const item = this.measureList[i]
          let data = []
          xAxisSleep.forEach(date => {
            const hasItem = item.data.filter(it => it.answerDate == date)
            if (hasItem.length) {
              data.push(hasItem[0].totalPoints)
            } else {
              data.push(null)
            }
          })

          dataList.push({
            name: item.measureName,
            type: 'line',
            stack: 'Total',
            data: data
          })
        }


        let obj = {
          legend: {
            x: 'center',
            y: 'top',
            padding: 10,
            data: legends
          },
          grid: {
            top: '30%',
            bottom: '3%',
            left: '0%',
            right: '0%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: xAxisSleep,
              axisPointer: {
                type: 'shadow',
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              name: '分',
              axisLabel: {
                formatter: '{value} 分',
              },
              splitLine: {
                show: false,
              },
            }
          ],
          series: dataList
        }

        let sleepDom = this.$refs.scale
        this.scaleCharts = echarts.init(sleepDom)
        this.scaleCharts.setOption(obj)
      },
      getBody() {
        let ele = this.getFormData(document.querySelector('#reportCommonContent'));
        let htm = ele.innerHTML;
        console.log('htm', ele.innerHTML);
        return '<body>' + htm + '</body>';
      },
      // 处理form表单的默认状态
      getFormData(ele) {
        let copy = ele.cloneNode(true);

        //update-begin--Author:sunjianlei  Date:20190510 for：支持忽略打印的标签----------------------
        let allElements = copy.querySelectorAll('*');
      [].forEach.call(allElements, function (item) {
        let attr = item.getAttribute('ignore-print');
            attr = (attr == null ? item.getAttribute('ignoreprint') : attr);
            if (attr != null && attr.toString() === 'true') {
                item.outerHTML = ''
            }
      });
        //update-end--Author:sunjianlei  Date:20190510 for：支持忽略打印的标签----------------------

        let copiedInputs = copy.querySelectorAll('input,select,textarea');
      [].forEach.call(copiedInputs, function (item, i) {
        let typeInput = item.getAttribute('type');
          let copiedInput = copiedInputs[i];
          if (typeInput === undefined) {
            typeInput = item.tagName === 'SELECT' ? 'select' : item.tagName === 'TEXTAREA' ? 'textarea' : '';
          }
          if (typeInput === 'radio' || typeInput === 'checkbox') {

            copiedInput.setAttribute('checked', item.checked);

          } else if (typeInput === 'text' || typeInput === '') {
            copiedInput.value = item.value;
            copiedInput.setAttribute('value', item.value);
          } else if (typeInput === 'select') {
        [].forEach.call(copiedInput.querySelectorAll('option'), function (op, b) {
          if (op.selected) {
                op.setAttribute('selected', true);
              }
        });
          } else if (typeInput === 'textarea') {
            copiedInput.value = item.value;
            copiedInput.setAttribute('value', item.value);
          }
      });

        //update-begin--Author:jianlei  Date:20190507 for：支持Canvas打印--------------------
        var sourceCanvas = ele.querySelectorAll('canvas');
        var copyCanvas = copy.querySelectorAll('canvas');

      [].forEach.call(copyCanvas, function (item, i) {
          var url = sourceCanvas[i].toDataURL()
            //update-begin--Author:sunjianlei  Date:20190510 for：canvas宽度自适应----------------------
            item.outerHTML = '<img src="' + url + '" style="width:100%;"/>'
            //update-end--Author:sunjianlei  Date:20190510 for：canvas宽度自适应----------------------
        });
          //update-end--Author:jianlei  Date:20190507 for：支持Canvas打印----------------------

        return copy;
      },

      batchPrint(ids) {
        let that = this
        that.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
        that.visible = true
        that.generatePrintHtml(ids)
      },
      generatePrintHtml(ids) {
        this.datas = ''
        getAction(this.url.generatePrintHtml+ids, { ids: ids }).then((res) => {
          if (res.success) {
            this.datas = res.result
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.isEdit = false
      },
      // 根据屏幕变化,设置抽屉尺寸
      resetScreenSize() {
        let screenWidth = document.body.clientWidth
        if (screenWidth < 500) {
          this.drawerWidth = screenWidth
        } else {
          this.drawerWidth = 900
        }
      },
      viewEdit() {
        this.datas = this.getBody()
        let that_ = this
        this.isEdit = true
        this.editContent = this.datas
        this.loading = true
        setTimeout(function() {
          that_.loading = false
        }, 250)
      },
      saveResport() {
        this.isEdit = false
        this.datas = this.editContent
      },
      cancelResport() {
        this.isEdit = false
      }
    }
  }
</script>

<style scoped lang="less">
.report-sleep-chart1 {
  width: 100%;
  height: 600px;
}
.report-main {
  padding: 20px 8px;
  .main-title {
    position: relative;
    font-size: 16px;
    line-height: 24px;
    font-weight: 600;

    &::after {
      content: '';
      position: absolute;
      top: 4px;
      left: -8px;
      width: 4px;
      height: 16px;
      background: #1890FF;
      border-radius: 2px;
    }
  }

  .report-info, .report-detail, .report-sleep, .report-scale, .report-summary, .report-hint {
    padding-bottom: 25px;
    .report-item-title {
      font-size: 14px;
      font-weight: 600;
    }
  }

  .report-info {
    .report-item-text {
      margin-bottom: 5px;
    }
  }

  .report-detail {
    .report-detail-content {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -60px;
      
      .report-detail-item {
        width: 25%;
        text-align: center;
        padding: 10px 0;
        .item-time {
          font-weight: 600;
          margin-bottom: 5px;
        }

        .item-text {
          margin-bottom: 5px;
        }
      }
    }
  }

  .report-long-sleep, .report-wake, .report-long-wake {
    .sleep-tilte {
      display: flex;
      justify-content: space-between;
    }
  }

  .report-sleep {
    .report-sleep-content {
      display: flex;
      justify-content: space-between;

      .report-sleep-item {
        width: 200px;
        padding: 10px 15px;
        border: 1px solid #999;
        border-radius: 8px;
        text-align: center;
        .item-time {
          font-size: 18px;
          font-weight: 500;
          line-height: 30px;
          margin: 0;
        }

        .item-text {
          font-size: 14px;
          line-height: 24px;
          margin: 0;
        }
      }
    }
  }

  .report-questionnaire {
    border: 0.5px solid #999;
    margin-bottom: 25px;
    .questionnaire-title {
      display: flex;

      .questionnaire-item1 {
        flex: 2;
        border: 0.5px solid #999;
        margin: 0;
        padding: 8px 0;
        text-align: center;
      }

      .questionnaire-item2 {
        flex: 1;
        border: 0.5px solid #999;
        margin: 0;
        padding: 8px 0;
        text-align: center;
      }
    }

    .questionnaire-main {
      .questionnaire-main-top {
        border: 0.5px solid #999;
        margin: 0;
        padding: 8px 0;
        text-align: center;
      }

      .questionnaire-main-item {
        display: flex;

        .questionnaire-item1 {
          flex: 2;
          border: 0.5px solid #999;
          margin: 0;
          padding: 8px 0;
          text-align: center;
        }

        .questionnaire-item2 {
          flex: 1;
          border: 0.5px solid #999;
          margin: 0;
          padding: 8px 0;
          text-align: center;
        }
      }
    }
  }

  .report-summary {

    .report-item-title2 {
      margin-bottom: 5px;
    }

    .report-summary-item {
      margin-bottom: 5px;
    }
  }
}
</style>