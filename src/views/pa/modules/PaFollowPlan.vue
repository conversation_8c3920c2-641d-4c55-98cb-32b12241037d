<template>
  <div class="follow-plan">
    <div class="table-operator">
      <div class="search">
        <div class="search-item">
          <div class="item-label">随访日期：</div>
          <div class="item-content">
            <a-range-picker v-model="diaryDate" format="YYYY-MM-DD" @change='changeDateQuery' />
            <a-button @click="loadData" type="primary">查询</a-button>
          </div>
        </div>
      </div>

      <a-button @click="addPlan" type="primary">新增随访</a-button>
    </div>
    <a-button @click="batchPrint" type="primary">批量打印</a-button>
    <a-table ref="table" size="middle" bordered rowKey="id" class="j-table-force-nowrap" :columns="columns" :dataSource="dataSource" :pagination="ipagination" @change="handleTableChange" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" :loading="loading">
      <span slot="action" slot-scope="text, record">
        <a @click="handlePrintForm(record)">内容预览</a>
        <a-divider type="vertical" />
        <a @click="exportExcelOption(record)">记录下载</a>
        <a-divider type="vertical" />
        <a @click="handleRemark(record)">备注</a>
      </span>
    </a-table>

    <pa-follow-plan-model ref="addPlan" @ok="loadData"></pa-follow-plan-model>
    <!-- 打印表单页 -->
    <report-common-form ref="ReportForm"></report-common-form>
    <a-modal v-model:visible="visible" title="备注" @ok="handleOk" @cancel="handleCancel">
      <a-textarea v-model:value="textContent" placeholder="请输入备注" :autosize="{minRows: 4, maxRows: 6}" />
    </a-modal>
  </div>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ReportCommonForm from './ReportCommonForm'
import PaFollowPlanModel from './PaFollowPlanModel'
import { httpAction, getAction } from '@/api/manage'

export default {
  name: 'pa-follow-plan',

  mixins: [JeecgListMixin, mixinDevice],

  components: {
    ReportCommonForm,
    PaFollowPlanModel
  },

  props: {
    patientDetail: {
      type: Object,
      default: () => { }
    }
  },

  data () {
    return {
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: {
        reserveStatus: 1
      },
      diaryDate: [],
      /* 数据源 */
      dataSource: [],
      loading: false,
      /* 排序参数 */
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      columns: [
        {
          title: '随访开始时间',
          align: 'center',
          dataIndex: 'startTime'
        },
        {
          title: '随访结束时间',
          align: 'center',
          dataIndex: 'endTime'
        },
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'patientName'
        },
        {
          title: '随访老师',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '随访备注',
          align: 'center',
          dataIndex: 'note'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/pa/paFollowUp/list',
        edit: '/pa/paFollowUp/edit',
        exportXlsUrl: 'pa/paFollowUp/exportDoc',
        exportBatch: 'pa/paFollowUp/exportBatch'
      },
      paPatientSchemeId: '',
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      visible: false,
      textContent: '',
      diaryDate: [],
      chooseData: {}
    }
  },

  methods: {
    changeDateQuery () {
      if (this.diaryDate.length) {
        this.queryParam.startDate = this.diaryDate[0].format('YYYY-MM-DD')
        this.queryParam.endDate = this.diaryDate[1].format('YYYY-MM-DD')
      } else {
        this.queryParam.startDate = ''
        this.queryParam.endDate = ''
      }
    },

    addPlan () {
      this.$refs.addPlan.add()
      this.$refs.addPlan.title = '新增随访记录'
    },

    handlePrintForm (record) {
      this.$refs.ReportForm.edit(record)
      this.$refs.ReportForm.title = '内容预览'
    },

    exportExcelOption (record) {
      let url = `${window._CONFIG['domianURL']}/${this.url.exportXlsUrl}?id=` + record.id
      window.location.href = url
    },

    batchPrint: function () {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择至少一条记录！')
        return
      } else if (this.selectedRowKeys.length > 50) {
        this.$message.warning('请不要选择超过五十条记录！')
        return
      } else {
        var ids = this.selectedRowKeys.join(',')
        let url = `${window._CONFIG['domianURL']}/${this.url.exportBatch}?ids=` + ids
        window.location.href = url
      }
    },

    loadData (arg) {
      if (!this.url.list) {
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      params.patientId = this.patientDetail.id
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result;
          if (res.result.total) {
            this.ipagination.total = res.result.total;
          } else {
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    handleTableChange (pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      console.log(pagination)
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
      this.loadData();
    },

    handleRemark (record) {
      this.visible = true
      this.chooseData = record
    },

    handleOk () {
      this.chooseData.note = this.textContent
      httpAction(this.url.edit, this.chooseData, 'put').then((res) => {
        if (res.success) {
          this.$message.success(res.message);
        } else {
          this.$message.warning(res.message);
        }
      }).finally(() => {
        this.handleCancel()
      })
    },

    handleCancel () {
      this.visible = false
    }
  }
}
</script>

<style lang="less">
.follow-plan {
  padding: 0 10px 20px;

  .table-operator {
    display: flex;
    justify-content: space-between;

    .ant-btn {
      margin-bottom: 8px;
    }

    .search {
      display: flex;

      .search-item {
        display: flex;
        margin-right: 20px;

        .item-label {
          margin-bottom: 8px;
          line-height: 32px;
        }

        .item-content {
          .ant-btn {
            margin-left: 12px;
          }
        }
      }
    }
  }

  .ant-btn {
    margin-bottom: 20px;
  }
}
</style>