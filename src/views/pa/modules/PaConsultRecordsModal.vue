<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <!-- 来访者基本信息（姓名、性别、电话、年龄），咨询时间（格式yyyy- mm-dd  hh-mm），咨询主诉（多行文本框），本次咨询要点（多行文本框），诊断评估（文本框），关注等级（文本框），问题类型（文本框），结案状态（文本框） -->
      <a-form-model ref="form" :model="model" :rules="validatorRules">
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName" label="学生姓名">
          <j-select-patient v-model="model.patientName" @back="backPatientInfo" :backInfo="true" :isRadio="true" :disabled="disableSubmit"></j-select-patient>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sex" label="性别">
          <j-dict-select-tag v-model="model.sex" placeholder="请选择性别" dictCode="sex" disabled />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone" label="电话">
          <a-input v-model="model.phone" placeholder="请输入电话" disabled />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="age" label="年龄">
          <a-input v-model="model.age" placeholder="请输入年龄" disabled />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="consultTime" label="咨询时间">
          <a-date-picker showTime valueFormat='YYYY-MM-DD HH:mm' v-model="model.consultTime" style="width: 100%" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="question" label="咨询主诉">
          <a-textarea v-model="model.question" :rows="4" placeholder="请输入咨询主诉" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="consultPoint" label="本次咨询要点">
          <a-textarea v-model="model.consultPoint" :rows="4" placeholder="请输入本次咨询要点" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="assessment" label="诊断评估">
          <a-input v-model="model.assessment" placeholder="请输入诊断评估" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="level" label="关注等级">
          <a-input v-model="model.level" placeholder="请输入关注等级" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="questionType" label="问题类型">
          <a-input v-model="model.questionType" placeholder="请输入问题类型" :disabled="disableSubmit" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status" label="结案状态">
          <a-input v-model="model.status" placeholder="请输入结案状态" :disabled="disableSubmit" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction } from '@/api/manage'

export default {
  name: "PaConsultAppointmentModal",
  data () {
    return {
      title: "操作",
      disableSubmit: false,
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      validatorRules: {
      },
      url: {
        add: "/pa/consultRecord/add",
        edit: "/pa/consultRecord/edit",
      },
    }
  },
  created () {
  },
  methods: {
    add () {
      //初始化默认值
      this.edit({});
    },
    edit (record) {
      this.model = Object.assign({}, record);
      this.visible = true;
    },
    close () {
      this.$emit('close');
      this.visible = false;
      this.disableSubmit = false
      this.$refs.form.clearValidate();
    },
    handleOk () {
      const that = this;
      console.log("model",this.model);
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true;
          let httpurl = '';
          let method = '';
          if (!this.model.id) {
            httpurl += this.url.add;
            method = 'post';
          } else {
            httpurl += this.url.edit;
            method = 'put';
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          })
        } else {
          return false;
        }
      })
    },
    handleCancel () {
      this.close()
    },
    backPatientInfo(info,rows) {
      this.model.patientName = info[0].text
      this.model.patientId = info[0].value
      this.model.sex = rows[0].sex
      this.model.age = rows[0].age
      this.model.phone = rows[0].telphone
      this.model = Object.assign({}, this.model);
    },
  }
}
</script>

<style lang="less" scoped>
</style>