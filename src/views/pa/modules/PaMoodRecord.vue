<template>
  <div class="mood-record">
    <div class="table-operator">
      <div class="search">
        <div class="search-item">
          <div class="item-label">心情状态：</div>
          <div class="item-content">
            <a-button type="primary" v-for="item in statusList" :key="item" :ghost="queryParam.moodType !== item" @click="setStatus(item)">{{ item }}</a-button>
          </div>
        </div>
        <div class="search-item">
          <div class="item-label">日期：</div>
          <div class="item-content">
            <a-range-picker v-model="diaryDate" format="YYYY-MM-DD" @change='changeDateQuery' />
          </div>
        </div>
      </div>

      <a-button @click="loadData" type="primary">查询</a-button>
    </div>
    <a-table ref="table" size="middle" bordered rowKey="id" class="j-table-force-nowrap" :columns="columns" :dataSource="dataSource" :pagination="ipagination" @change="handleTableChange" :loading="loading">
      <template slot="reason" slot-scope="text, record">
        <a-tooltip placement="top">
          <template #title>
            <span>{{ record.reason }}</span>
          </template>
          <p class="remark-text">{{ record.reason }}</p>
        </a-tooltip>
      </template>
      <template slot="doctorNote" slot-scope="text, record">
        <a-tooltip placement="top">
          <template #title>
            <span>{{ record.doctorNote }}</span>
          </template>
          <p class="remark-text">{{ record.doctorNote }}</p>
        </a-tooltip>
      </template>
      <span slot="action" slot-scope="text, record">
        <a @click="reservationConfirm(record)">详情</a>
        <a-divider type="vertical" />
        <a @click="handleRemark(record)">老师备注</a>
      </span>
    </a-table>

    <PaMoodDetailModal ref="modalForm" @ok="modalFormOk"></PaMoodDetailModal>
    <a-modal v-model:visible="visible" title="老师备注" @ok="handleOk" @cancel="handleCancel">
      <a-textarea v-model:value="textContent" placeholder="请输入备注" :autosize="{minRows: 4, maxRows: 6}" />
    </a-modal>
  </div>
</template>

<script>
import { filterObj } from '@/utils/util'
import { getAction, httpAction } from '@/api/manage'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PaMoodDetailModal from './PaMoodDetailModal.vue'

export default {
  name: 'pa-mood-record',

  mixins: [JeecgListMixin, mixinDevice],

  components: {
    PaMoodDetailModal
  },

  props: {
    id: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: {
        moodType: ''
      },
      diaryDate: [],
      statusList: ['生气', '不开心', '平淡', '开心', '兴奋'],
      /* 数据源 */
      dataSource: [],
      loading: false,
      /* 排序参数 */
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      columns: [
        {
          title: '日期',
          align: 'center',
          dataIndex: 'recordTime'
        },
        {
          title: '学生名称',
          align: 'center',
          dataIndex: 'patientName'
        },
        {
          title: '心情状态',
          align: 'center',
          dataIndex: 'moodType'
        },
        {
          title: '内容',
          align: 'center',
          scopedSlots: { customRender: 'reason' }
        },
        {
          title: '老师备注',
          align: 'center',
          scopedSlots: { customRender: 'doctorNote' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/pa/paMoodDiary/list',
        edit: '/pa/paMoodDiary/edit',
      },
      paPatientSchemeId: '',
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      visible: false,
      textContent: '',
      chooseData: {}
    }
  },

  methods: {
    setStatus (value) {
      if (value === this.queryParam.moodType) {
        this.queryParam.moodType = ''
      } else {
        this.queryParam.moodType = value
      }

      this.loadData(1)
    },

    changeDateQuery () {
      if (this.diaryDate.length) {
        this.queryParam.startDate = this.diaryDate[0].format('YYYY-MM-DD')
        this.queryParam.endDate = this.diaryDate[1].format('YYYY-MM-DD')
      } else {
        this.queryParam.startDate = ''
        this.queryParam.endDate = ''
      }
    },

    reservationConfirm (record) {
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title = "心情日记详情";
      this.$refs.modalForm.disableSubmit = true;
    },

    handleTableChange (pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      console.log(pagination)
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
      this.loadData();
    },

    loadData (arg) {
      if (!this.url.list) {
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      params.patientId = this.id
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result;
          if (res.result.total) {
            this.ipagination.total = res.result.total;
          } else {
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    handleRemark (record) {
      this.visible = true
      this.chooseData = record
    },

    handleOk () {
      this.chooseData.doctorNote = this.textContent
      httpAction(this.url.edit, this.chooseData, 'put').then((res) => {
        if (res.success) {
          this.$message.success(res.message);
          this.loadData(1)
        } else {
          this.$message.warning(res.message);
        }
      }).finally(() => {
        this.handleCancel()
      })
    },

    handleCancel () {
      this.visible = false
      this.textContent = ''
    }
  }
}
</script>

<style lang="less">
.mood-record {
  padding: 0 10px 20px;

  .table-operator {
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;

    .search {
      display: flex;

      .search-item {
        display: flex;
        margin-right: 20px;

        .item-label {
          margin-bottom: 8px;
          line-height: 32px;
        }
      }
    }
  }

  .remark-text {
    width: 200px;
    margin: 0;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
  }
}
</style>