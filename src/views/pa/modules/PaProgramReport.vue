<template>
  <div class="report-management">
    <div class="table-operator">
      <a-button @click="createReport" type="primary">生成报告</a-button>
    </div>
    <a-table
      ref="table"
      size="middle"
      bordered
      rowKey="id"
      class="j-table-force-nowrap"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      @change="handleTableChange"
      :loading="loading">
      <span slot="action" slot-scope="text, record">
        <a @click="handlePrintForm(record)">内容预览</a>
        <!-- <a-divider type="vertical"/>
        <a @click="exportExcelOption(record)">报告下载</a> -->
      </span>
    </a-table>

    <program-report-form ref='reportForm' v-if="showReport" @close="showReport = false"></program-report-form>
    <pa-create-report ref="modalForm" @ok="modalFormOk"/>
  </div>
</template>

<script>
import PaCreateReport from './PaCreateReport.vue'
import ProgramReportForm from './ProgramReportForm.vue'
import {getAction, putAction} from '@/api/manage'
import moment from 'moment'


export default {
  name: 'pa-program-report',

  components: {
    ProgramReportForm,
    PaCreateReport,
  },

  props: {
    id: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      columns: [
        {
          title: '报告名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '生成报告时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
    ],
      url: {
        list: '/pa/paPatientSchemeGroupReport/listAll',
        exportDocUrl: 'pa/paPatientSchemeGroup/exportDoc',
        changeStatusById: '/pa/paPatientSchemeGroup/changeStatusById',
        schemeGroupDetail: '/pa/paPatientSchemeGroup/schemeGroupDetail',
        sublist: '/pa/paPatientScheme/sublist',
        measurelist: '/pa/paPatientScheme/measurelist',
        trainlist: '/pa/paPatientScheme/trainlist',
        editSub: '/pa/paPatientScheme/editSub',
      },
      programList: [],
      chooseProgram: '',
      isPause: false,
      loading:false,
      program: {},
      // hasInProgress: false, // 是否有进行中的方案
      patientSchemeGroupId: '',
      dataSource: [],
      showReport: false,
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
    }
  },

  mounted() {
    this.patientSchemeGroupId = this.$route.query.patientSchemeGroupId
    this.loadList(this.patientSchemeGroupId ? false : true)
  },

  computed: {
    // 是否有进行中的方案
    hasInProgress() {
      return !!this.programList.filter(item => item.status == 0).length
    }
  },

  methods: {
    handleTableChange(pagination, filters, sorter) {
      this.ipagination = pagination;
    },

    loadList(flag = true) {
      var params = {}
      params.patientId = this.$store.getters.patientDetail.id
      params.pageNo = 1
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || res.result
        }
      })
    },

    getTodayScheme(id) {
      const params = {
        patientId: this.$store.getters.patientDetail.id
      }
      if (id) params.patientSchemeGroupId = this.patientSchemeGroupId
      getAction(this.url.schemeGroupDetail, params).then((res) => {
        if (res.success && res.result) {
          this.program = res.result
          // this.dataSource = this.program.paPatientSchemeList
          this.patientSchemeGroupId = this.program.id
        }
      })
    },



    setData() {
      this.getTodayScheme(this.patientSchemeGroupId)
    },

    changeStatus(type) {
      if (type === 1) {
        this.$confirm({
          title: '暂停方案',
          content: '方案暂停后，学生无执行中方案，可以查询出方案【恢复方案】继续执行，或为学生新增方案！',
          onOk:() => {
            this.isPause = true
            this.changeStatusById(2)
          }
        })
      } else {
        this.isPause = false
        this.changeStatusById(0)
      }
    },

    changeStatusById(status) {
      let params = {
        id: this.patientSchemeGroupId, // 当前执行方案id
        status
      }
      getAction(this.url.changeStatusById, params).then((res) => {
        if (res.success) {
          this.programList.filter(item => item.id === this.patientSchemeGroupId)[0].status = status ? 2 : 0
          this.program.status = status ? 2 : 0
          this.$message.success('修改成功！')
        } else {
          this.$message.success('修改失败，请稍后重试！')
        }
      }).catch(() => {
        this.$message.success('修改失败，请稍后重试！')
      })
    },

    handlePrintForm(record) {
      this.showReport = true
      this.$nextTick(() => {
        this.$refs.reportForm.edit(record)
        this.$refs.reportForm.title = '内容预览'
      })
    },

    handleExportDoc(record) {
      if (record.status != '1') {
        this.$message.error('未答完题目不能查看报告！')
        return
      }
      let url = `${window._CONFIG['domianURL']}/${this.url.exportDocUrl}?groupId=` + record.id
      window.location.href = url
    },
    handleDetail(record) {
      this.$refs.dailyFrom.title = '计划详情';
      this.$refs.dailyFrom.detail(record);
      this.$refs.dailyFrom.disableSubmit = true;
    },

    createReport() {
      this.$refs.modalForm.title = '生成报告';
      this.$refs.modalForm.add();
      this.$refs.modalForm.disableSubmit = false;
    },

    modalFormOk() {
      // 新增/修改 成功时，重载列表
      this.loadList()
    },

    handleAdd: function () {
      this.$refs.modalForm.add();
      this.$refs.modalForm.title = "新增";
      this.$refs.modalForm.disableSubmit = false;
    },

    onCellChange(key, dataIndex, value) {
      this.updateSchemeSub(key,value)
      if (this.activeKey === 1) {
        const dataSource = [...this.dataSource1]
        const target = dataSource.find(item => item.key === key)
        if (target) {
          target[dataIndex] = value
          this.dataSource1 = dataSource
        }
      } else if (this.activeKey === 2) {
        const dataSource = [...this.dataSource2]
        const target = dataSource.find(item => item.key === key)
        if (target) {
          target[dataIndex] = value
          this.dataSource2 = dataSource
        }
      } else if (this.activeKey === 3) {
        const dataSource = [...this.dataSource3]
        const target = dataSource.find(item => item.key === key)
        if (target) {
          target[dataIndex] = value
          this.dataSource3 = dataSource
        }
      }
    },

    updateSchemeSub(id,status) {
      let params = {}
      params.id = id
      params.treatmentCondition = status
      putAction(this.url.editSub, params).then((res) => {

      })
    },

    exportExcelOption(record) {
      let url = `${window._CONFIG['domianURL']}/${this.url.exportOptionXls}?id=` + record.id
      window.location.href = url
    },
  }
}
</script>

<style lang="less" scoped>
.report-management {
  padding: 0 10px 20px;

  .table-operator {
    display: flex;
    justify-content: space-between;
  }

  .table-operator {
    display: flex;
    justify-content: space-between;

    .ant-select {
      width: 200px;
      margin-right: 12px;
    }
  }

  .current-info {
    display: flex;
    align-items: center;
    padding: 10px 0 15px 0;

    .ant-btn {
      margin: 0 12px;
    }

    .info {
      margin: 0;
    }
  }
}

.success-text {
  color: green;
}

.default-text {
  color: blue;
}

.error-text {
  color: red;
}
</style>

<style lang="less">
.select-tag {
  width: 150px;

  .ant-select-selection {
    width: 150px;
  }
}
</style>