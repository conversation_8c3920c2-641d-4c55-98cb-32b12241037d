<template>
  <div class="weeklyReport-page">
    <div class="timeHead">
      <a-icon class="icon" type="left" @click="previousWeek()" />
      {{ weekFormat }}
      <a-icon class="icon" type="right" @click="nextWeek()" />
    </div>
    <div class="analyze">
      <div class="section-header">周睡眠分析</div>
      <div class="content">
        <div class="fallAsleep-week week-diary">
          <div class="title">本周平均入睡时长{{ fallAsleep_average }}</div>
          <div class="label">{{ fallAsleepAverageLabel }}</div>
        </div>
        <div class="weakupLength-week week-diary">
          <div class="title">本周平均夜醒时长是{{ weakupLength_average }}</div>
          <div class="label">{{ weakupLengthAverageLabel }}</div>
        </div>
        <div class="weakupTime-week week-diary">
          <div class="title">本周平均夜醒次数是{{ weakupTime_average }}次</div>
          <div class="label">{{ weakupTimeAverageLabel }}</div>
        </div>
        <div class="laiBed-week week-diary">
          <div class="title">本周赖床时长是{{ laiBed_average }}</div>
          <div class="label">{{ laiBedaAverageLabel }}</div>
        </div>
      </div>
    </div>
    <div class="efficiency">
      <div class="section-header">睡眠效率</div>
      <div class="charts-box">
        <v-chart :forceFit="true" :height="height" :data="chartData">
          <v-tooltip />
          <v-axis />
          <v-legend />
          <v-stack-bar position="日期*时长" color="name" />
        </v-chart>
      </div>
      <div class="efficiency-content">
        <div class="sleep-data efficiency-diary">
          <div class="title">
            <img class="icon" src="~@/assets/diary_sleep.png" alt="" />
            平均睡眠时长
          </div>
          <div class="sleepdata">
            {{ sleepDateAverage }}
          </div>
        </div>
        <div class="fallAsleep-data efficiency-diary">
          <div class="title">
            <img class="icon" src="~@/assets/diary_fallAsleep.png" alt="" />
            平均入睡时长
          </div>
          <div class="sleepdata">
            {{ fallAsleep_average }}
          </div>
        </div>
        <div class="bedridden-data efficiency-diary">
          <div class="title">
            <img class="icon" src="~@/assets/diary_bedridden.png" alt="" />
            平均卧床时长
          </div>
          <div class="sleepdata">
            {{ bedriddenDateAverage }}
          </div>
        </div>
        <div class="efficiency-data efficiency-diary">
          <div class="title">
            <img class="icon" src="~@/assets/diary_efficiency.png" alt="" />
            平均睡眠效率
          </div>
          <div class="sleepdata" v-if="efficiencyAverage != '-'">{{ efficiencyAverage * 100 }}%</div>
          <div class="sleepdata" v-else>
            {{ efficiencyAverage }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { httpAction } from '@/api/manage'
import { mapGetters } from 'vuex'
import moment from 'moment'
import { DataSet } from '@antv/data-set'
import { postAction } from '@/api/manage'

export default {
  name: 'PaPatientForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      url: '/pa/paSleepDiary/sleepDiaryReportForWeek/',
      weekFormat: '',
      fallAsleep_average: '8',
      fallAsleepAverageLabel: '对比上周，平均入睡时长没有变化',
      fallAsleep_week: '',
      weakupLength_average: '5',
      weakupLengthAverageLabel: '对比上周，平均夜醒时长增加了2分钟',
      weakupLength_week: '',
      weakupTime_average: '2',
      weakupTimeAverageLabel: '对比上周，平均夜醒平均次数没有变化',
      weakupTime_week: '',
      laiBed_average: '10',
      laiBedaAverageLabel: '对比上周，平均赖床时长没有变化',
      laiBed_week: '',
      sleepDateAverage: '',
      bedriddenDateAverage: '',
      efficiencyAverage: '',
      weekStart: '',
      weekEnd: '',
      column: {},
      currentTime: new Date(), // 获取当前时间
      chartsDataColumn2: null,
      sourceData: [
        {
          name: '卧床时长',
          '01-30': 0,
          '01-31': 0,
          '02-01': 0,
          '02-02': 0,
          '02-03': 0,
          '02-04': 0,
          '02-05': 0,
        },
        {
          name: '睡眠时长',
          '01-30': 0,
          '01-31': 0,
          '02-01': 0,
          '02-02': 0,
          '02-03': 0,
          '02-04': 0,
          '02-05': 0,
        },
      ],
      // tips: [1, 2, 3, 4, 5, 6, 8],
      height: 243,
      record: {},
      xAxis: []
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    },
    chartData() {
      const dv = new DataSet.View().source(this.sourceData)
      dv.transform({
        type: 'fold',
        fields: this.xAxis,
        key: '日期',
        value: '时长',
      })
      const data = dv.rows
      data.forEach((item) => (item.meal = 1.0))
      console.log(data)
      return data
    },
  },
  created() {
    let lieBeds = [0, 0, 0, 0, 0, 0, 0]
    let sleeps = [0, 0, 0, 0, 0, 0, 0]
    this.configDate(lieBeds, sleeps);
    this.getWeekStartAndEnd(this.currentTime)
  },
  methods: {
    configDate(lieBeds, sleeps) {
      
      if(this.xAxis.length == 0) {
        return
      }
      let xAxis = this.xAxis
      console.log(xAxis);
      let firstObj = { name: '卧床时长' }
      xAxis.forEach((item, index) => {
        firstObj[item] = lieBeds[index]
      })
      let secondObj = { name: '睡眠时长' }
      xAxis.forEach((item, index) => {
        secondObj[item] = sleeps[index]
      })
      console.log(secondObj)
      this.sourceData = [firstObj, secondObj]
    },
    previousWeek() {
      this.currentTime = new Date(new Date(this.currentTime).getTime() - 24 * 60 * 60 * 1000 * 7) //计算当前日期 -1
      this.getWeekStartAndEnd(this.currentTime)
    },
    nextWeek() {
      this.currentTime = new Date(new Date(this.currentTime).getTime() + 24 * 60 * 60 * 1000 * 7) //计算当前日期 -1
      this.getWeekStartAndEnd(this.currentTime)
    },
    // 获取指定日期的那一周的开始、结束日期
    getWeekStartAndEnd(val) {
      let now = ''
      if (val) {
        now = new Date(val) // 日期
      } else {
        now = new Date() // 日期
      }
      let nowDayOfWeek = now.getDay() // 本周的第几天
      let nowDay = now.getDate() // 当前日
      let nowMonth = now.getMonth() // 当前月
      let nowYear = now.getFullYear() // 当前年
      let day = nowDayOfWeek || 7

      let weekStart = this.getWeekStartDate(nowYear, nowMonth, nowDay, nowDay + 1 - day)
      let weekEnd = this.getWeekEndDate(nowYear, nowMonth, nowDay, nowDay + 7 - day)
      this.weekStart = weekStart
      this.weekEnd = weekEnd
      this.weekFormat = weekStart.substring(5) + '~' + weekEnd.substring(5)
      if(this.record) {
        this.loadDetail(this.record)
      }
    },

    // 获得某一周的开始日期
    getWeekStartDate(nowYear, nowMonth, nowDay, nowDayOfWeek) {
      let weekStartDate = new Date(nowYear, nowMonth, nowDayOfWeek)
      // this.weekStart = weekStartDate;
      return this.formatDate(weekStartDate)
    },

    // 获得某一周的结束日期
    getWeekEndDate(nowYear, nowMonth, nowDay, nowDayOfWeek) {
      let weekEndDate = new Date(nowYear, nowMonth, nowDayOfWeek)
      // this.weekEnd = weekEndDate;
      return this.formatDate(weekEndDate)
    },
    formatDate(date) {
      var myyear = date.getFullYear()
      var mymonth = date.getMonth() + 1
      var myweekday = date.getDate()
      if (mymonth < 10) {
        mymonth = '0' + mymonth
      }
      if (myweekday < 10) {
        myweekday = '0' + myweekday
      }
      return myyear + '-' + mymonth + '-' + myweekday
    },

    loadDetail(record) {
      if (record) {
        this.record = record
      } else {
        return
      }
      if (this.weekStart == '') {
        return
      }
      // this.convertToDate()
      let urlPath = this.url+this.record.id
      let that = this
      let formData = {
        endDate: that.weekEnd,
        startDate: that.weekStart,
      }
      postAction(urlPath, formData).then((result) => {
        if (result.success) {
          that.fallAsleep_average = result.result.fallAsleepAverage
          that.fallAsleepAverageLabel = result.result.fallAsleepAverageLabel
          that.weakupLength_average = result.result.weakupLengthAverage
          that.weakupLengthAverageLabel = result.result.weakupLengthAverageLabel
          that.weakupTime_average = result.result.weakupTimeAverage
          that.weakupTimeAverageLabel = result.result.weakupTimeAverageLabel
          that.laiBed_average = result.result.laiBedaAverage
          that.laiBedaAverageLabel = result.result.laiBedaAverageLabel
          that.sleepDateAverage = result.result.sleepDateAverage
          that.bedriddenDateAverage = result.result.bedriddenDateAverage
          that.efficiencyAverage = result.result.efficiencyAverage
          that.xAxis = result.result.xAxis
          that.configDate(result.result.lieBeds, result.result.sleeps);
        } else {
          that.sleepDiary = false
        }
      })
    },
  },
}
</script>

<style lang="scss">
.weeklyReport-page {
  display: flex;
  width: 100%;
  height: 100%;
  background: #fbfbfb;
  flex-direction: column;

  .timeHead {
    display: flex;
    width: 100%;
    min-height: 48px;
    background: #ffffff;
    align-items: center;
    justify-content: center;

    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #8f9399;
    line-height: 20px;

    .icon {
      width: 40px;
    }
  }

  .analyze {
    display: flex;
    width: 100%;
    flex-direction: column;
    background-color: #ffffff;
    padding: 0 15px;
    margin-top: 8px;
    .content {
      .week-diary {
        display: flex;
        width: 100%;
        height: 65px;
        flex-direction: column;
        border-bottom: 1px solid #eaeef5;
        justify-content: center;

        &:last-child {
          border-bottom: none;
        }
        .title {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #303133;
          line-height: 20px;
        }
        .label {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8f9399;
          line-height: 17px;
          margin-top: 6px;
        }
      }
    }
  }

  .efficiency {
    display: flex;
    width: 100%;
    flex-direction: column;
    background-color: #ffffff;
    padding: 0 15px;
    margin-top: 8px;
    padding-bottom: 20px;

    .charts-box {
      width: 100%;
      height: 243px;
      background-color: #ffffff;
      margin-top: 8px;
    }
    .efficiency-content {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin-top: 15px;

      .efficiency-diary {
        display: flex;
        width: 50%;
        flex-direction: column;
        align-items: center;
        margin-top: 9px;
      }
      .title {
        display: flex;
        flex-direction: row;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8f9399;
        line-height: 20px;
        justify-content: center;
        .icon {
          width: 18px;
          height: 18px;
        }
      }
      .sleepdata {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #303133;
        line-height: 20px;
        margin-top: 5px;
      }
    }
  }

  .section-header {
    display: flex;
    width: 100%;
    height: 51px;
    font-size: 14px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #303133;
    line-height: 20px;
    border-bottom: 1px solid #eaeef5;
    align-items: center;
  }
}
</style>