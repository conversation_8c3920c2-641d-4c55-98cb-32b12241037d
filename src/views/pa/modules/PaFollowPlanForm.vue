<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="学生名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="patientName">
              <a-input v-model="model.patientName" disabled :defaultValue="nickname()" placeholder="请输入学生名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="随访性质" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <a-radio-group v-model="model.type">
                <a-radio value="初访">初访</a-radio>
                <a-radio value="回访">回访</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="随访开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="startTime">
              <j-date placeholder="请选择开始时间" :disabled="isEdit" :showTime="true" date-format="YYYY-MM-DD HH:mm:ss" v-model="model.startTime" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="随访结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="endTime">
              <j-date placeholder="请选择结束时间" v-model="model.endTime" :showTime="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="睡眠问题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sleepProblem">
              <a-checkbox-group v-model="model.sleepProblem">
                <a-checkbox value="入睡困难">入睡困难</a-checkbox>
                <a-checkbox value="维持困难">维持困难</a-checkbox>
                <a-checkbox value="过早睡醒">过早睡醒</a-checkbox>
                <a-checkbox value="不在预期时间醒来">不在预期时间醒来</a-checkbox>
              </a-checkbox-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="详情情况" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sleepProblemNote">
              <a-textarea :rows="5" v-model="model.sleepProblemNote" :disabled="isEdit" placeholder="失眠有多久了，主要表现为什么症状，平均每晚睡眠总时间，平均每周有多少天失眠，日间功能状况没有合适的睡眠时间或不恰当的睡眠环境而失眠，是否其他疾病引起失眠" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="日间情况" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="daytimeState">
              <a-checkbox-group v-model="model.daytimeState">
                <a-checkbox value="精神不佳">精神不佳</a-checkbox>
                <a-checkbox value="注意力下降">注意力下降</a-checkbox>
                <a-checkbox value="记忆力下降">记忆力下降</a-checkbox>
                <a-checkbox value="易激怒">易激怒</a-checkbox>
                <a-checkbox value="情绪低落">情绪低落</a-checkbox>
              </a-checkbox-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="伴随症状" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="daytimeStateNote">
              <a-textarea :rows="5" v-model="model.daytimeStateNote" :disabled="isEdit" placeholder="记录伴随症状，描述伴随症状与主要症状之间的相互关系" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="主观感受" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="feeling">
              <a-select
                placeholder="请选择"
                v-model="model.feeling"
                >
                <a-select-option value="明显有效">明显有效</a-select-option>
                <a-select-option value="改善">改善</a-select-option>
                <a-select-option value="持平">持平</a-select-option>
                <a-select-option value="变差">变差</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item class="no-item" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="feelingNote">
              <a-textarea :rows="5" v-model="model.feelingNote" :disabled="isEdit" placeholder="补充说明" style="width: 100%"/>
            </a-form-model-item>
          </a-col>  
          <a-col :span="24">
            <a-form-model-item label="用药情况" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="medicationGuide">
              <div class="medication-group">
                <div class="medication-item" v-for="(item, index) in model.medicationGuide" :key="item.sec">
                  <a-select
                    placeholder="请选择药品"
                    v-model="item.id"
                    @select="(value) => handleSelect(value, item)"
                    >
                    <a-select-option v-for="it in medicationList" :key="it.id" :value="it.id">{{ it.tradeName }}</a-select-option>
                  </a-select>
                  <a-select
                    placeholder="请选择用量"
                    v-model="item.dosage"
                    >
                    <a-select-option v-for="it in dosages" :key="it" :value="it">{{ it }}</a-select-option>
                  </a-select>
                  <a-button shape="circle" @click="addMedication">+</a-button>
                  <a-button shape="circle" @click="delMedication(item)" v-if="index !== 0">-</a-button>
                </div>
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item class="no-item" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="medicationGuideNote">
              <a-textarea :rows="5" v-model="model.medicationGuideNote" :disabled="isEdit" placeholder="补充说明" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注与建议" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="note">
              <a-textarea :rows="5" v-model="model.note" :disabled="isEdit" placeholder="请输入备注与建议" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
          
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { mapGetters } from 'vuex'
  import moment from 'moment'

  export default {
    name: 'PaPatientSchemeForm',
    data() {
      return {
        patientDetail: JSON.parse(localStorage.getItem("PatientDetail")),
        dosages: ["1/4片", "1/3片", "1/2片", "2/3片", "3/4片", "1片", "1.25片", "1.5片",
							"1.75片", "2片", "2.25片", "2.5片", "2.75片", "3片", "3.5片", "4片",
							 "4.5片", "5片", "5.5片", "6片"],
        medicationList: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        model: {
          patientId: '',
          patientName: '',
          type: '初访',
          startTime: null,
          endTime: null,
          sleepProblem: null,
          sleepProblemNote: null,
          daytimeState: null,
          daytimeStateNote: null,
          // feeling: '',
          feelingNote: null,
          medicationGuide: [
            {
              sec: new Date().getTime(),
              // id: null,
              tradeName: null,
              // dosage: null,
            }
          ],
          medicationGuideNote: null,
          note: null
        },
        disabledEdit: true,
        validatorRules: {},
        url: {
          add: '/pa/paFollowUp/add',
          listAll: '/base/baseMedicine/listAll',
        },
        isEdit: false,
      }
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
      this.getListAll()
    },
    methods: {
      ...mapGetters(['nickname', 'userInfo']),
      getListAll() {
        getAction(this.url.listAll).then((res) => {
          this.medicationList = res.result
        })
      },
      addMedication() {
        this.model.medicationGuide.push({
          sec: new Date().getTime(),
          tradeName: null,
        })
      },
      delMedication(item) {
        this.model.medicationGuide = this.model.medicationGuide.filter(it => it.sec !== item.sec)
      },
      handleSelect(value, item) {
        const name = this.medicationList.filter(it => it.id === value)[0].tradeName
        item.tradeName = name
      },
      add() {
        this.modelDefault.patientName = this.patientDetail.name
        this.modelDefault.patientId = this.patientDetail.id
        this.disabledEdit = false
        this.edit(this.modelDefault)
        this.isEdit = false        
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.visible = true
        this.isEdit = true
      },
      submitForm() {
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {  
            this.confirmLoading = true
            const params = {
              ...this.model
            }
            params.medicationGuide = JSON.stringify(params.medicationGuide)
            params.sleepProblem = JSON.stringify(params.sleepProblem)
            params.daytimeState = JSON.stringify(params.daytimeState)
            httpAction(this.url.add, params, 'post').then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.$emit('ok')
              } else {
                this.$message.warning(res.message)
              }
            }).finally(() => {
              this.confirmLoading = false
            })
          }
        })
      }
    }
  }
</script>

<style lang="scss">
.no-item {
  margin-left: 287px;
}
.medication-group {
  display: flex;
  flex-direction: column;

  .medication-item {
    display: flex;
    align-items: center;
    padding-bottom: 10px;

    .ant-select {
      width: 200px;
      margin-right: 20px;
    }

    .ant-btn {
      min-width: 24px;
      width: 24px;
      height: 24px;
      line-height: 22px;
      text-align: center;
      margin-right: 10px;
    }
  }
}
</style>
