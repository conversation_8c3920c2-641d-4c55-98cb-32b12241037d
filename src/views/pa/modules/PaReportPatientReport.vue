<template>
  <div class="sleep-diary">
    <a-tabs type="card" v-model="chooseTab" @change="handleChange">
      <a-tab-pane :key="1" tab="睡眠统计">
        <div class="sleep-report">
          <div class="head-search">
            <a-col :span="2">
              <a-button @click="searchWeekReport" type="primary">周视图</a-button>
            </a-col>
            <a-col :span="3">
              <a-button @click="searchMonthReoprt" type="primary">月视图</a-button>
            </a-col>
            <a-col :span="9">
              <a-range-picker
                format="YYYY-MM-DD"
                :placeholder="['开始时间', '结束时间']"
                @change="onReportSleepDateChange"
              />
            </a-col>
            <a-col :span="2">
              <a-button @click="searchRangeReoprt" type="primary">查询</a-button>
            </a-col>
          </div>
          <div class="charts-box">
            <div id="charts" class="report-sleep-chart" ref="sleepChart"></div>
          </div>
        </div>
      </a-tab-pane>

      <a-tab-pane :key="2" tab="服药记录">
        <div class="sleep-report">
          <div class="head-search">
            <a-col :span="2">
              <a-button @click="searchWeekReport" type="primary">周视图</a-button>
            </a-col>
            <a-col :span="3">
              <a-button @click="searchMonthReoprt" type="primary">月视图</a-button>
            </a-col>
            <a-col :span="9">
              <a-range-picker
                format="YYYY-MM-DD"
                :placeholder="['开始时间', '结束时间']"
                @change="onReportDrugDateChange"
              />
            </a-col>
            <a-col :span="2">
              <a-button @click="searchRangeReoprt" type="primary">查询</a-button>
            </a-col>
          </div>
          <div class="charts-box">
            <div id="chartd" class="report-drug-chart" ref="drugChart"></div>
          </div>
        </div>
      </a-tab-pane>

      <a-tab-pane :key="3" tab="心情记录">
        <PaMoodRecord :id="id"></PaMoodRecord>
      </a-tab-pane>

      <a-tab-pane :key="4" tab="忧虑记录">
        <PaWorryRecord :id="id"></PaWorryRecord>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { getAction, postAction } from '@/api/manage'
import { DataSet } from '@antv/data-set'
import * as echarts from 'echarts/core'
import { BarChart, LineChart, ScatterChart} from 'echarts/charts'
import { TitleComponent, TooltipComponent, GridComponent, ToolboxComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { formatDate } from '@/utils/util'
import PaMoodRecord from './PaMoodRecord.vue'
import PaWorryRecord from './PaWorryRecord'

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  ToolboxComponent,
  LegendComponent,
  BarChart,
  LineChart,
  ScatterChart,
  CanvasRenderer,
])

export default {
  name: 'pa-report-parient-report',
  components: {
    PaMoodRecord,
    PaWorryRecord
  },

  props: {
    id: {
      type: String,
      default: '',
    },
  },

  data() {
    const scale = [
      {
        dataKey: 'temperature',
        min: 0,
      },
      {
        dataKey: 'percentage',
        min: 0,
      },
    ]
    return {
      scale,
      chooseTab: 1,
      dateStart: '',
      dateEnd: '',
      currentTime: new Date(), // 获取当前时间
      dateFormat: 'YYYY-MM-DD',
      columns: [],
      url: {
        list: '/pa/paSleepDiary/list',
        report: '/pa/paSleepDiary/sleepDiaryReportForWeek/',
        reportNew: `/pa/paSleepDiary/listDiaryData/`,
        drugReport: `/pa/paSleepDiaryMedicine/listMedicine/`
      },
      superFieldList: [],
      sourceData: [],
      height: 400,

      axisGrid: null,
      chartLineData: [],
      sleepCharts: null,
      drugCharts: null,
      xAxisSleep: [],
      xAxisDrug: [],
      napLengthArr: [],       //小睡      
      nightWakeLengthArr: [], //夜醒
      sleepEfficiencyArr: [], //睡眠效率
      sleepLengthArr: [],     //睡眠总时长
      soberLengthArr: [],     //清醒总时长
    }
  },

  computed: {
    drugChartOpt() {
      let obj = {
        title: {
          text: 'Stacked Line',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            axis: 'x'
          }
        },
        legend: {
          data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'time',
          name: '日期',
          min: this.dateStart,
          max: this.dateEnd,
          maxInterval: 3600 * 24 * 1000,
          axisTick: {
            show: true
          },
          axisLine: {
            show: true
          }
        },
        yAxis: {
          type: 'category',
          data: ['1/4', '1/3', '1/2', '2/3', '3/4', '1', '1.25', '1.5', '1.75', '2', '2.25', '2.5', '2.75', '3', '3.5', '4', '4.5', '5', '5.5', '6'],
          axisTick: {
            show: false
          }
        },
        series: [],
      }
      return obj
    },
    sleepChartOpt() {
      let obj = {
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: ['小睡总时长', '夜醒总时长', '睡眠总时长', '清醒总时长', '睡眠效率'],
        },
        xAxis: [
          {
            type: 'category',
            data: this.xAxisSleep,
            axisPointer: {
              type: 'shadow',
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '时长',
            axisLabel: {
              formatter: '{value} 分',
            },
            splitLine: {
              show: false,
            },
          },
          {
            type: 'value',
            name: '效率',
            max: 100,
            axisLabel: {
              formatter: '{value} %',
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: '小睡总时长',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return Math.floor(value/60) + '时' + value%60 + ' 分'
              },
            },
            data: this.napLengthArr,
          },{
            name: '夜醒总时长',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return Math.floor(value/60) + '时' + value%60 + ' 分'
              },
            },
            data: this.nightWakeLengthArr,
          },{
            name: '睡眠总时长',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return Math.floor(value/60) + '时' + value%60 + ' 分'
              },
            },
            data: this.sleepLengthArr,
          },{
            name: '清醒总时长',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return Math.floor(value/60) + '时' + value%60 + ' 分'
              },
            },
            data: this.soberLengthArr,
          },
          {
            name: '睡眠效率',
            type: 'line',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' %'
              },
            },
            data: this.sleepEfficiencyArr,
          },
        ],
      }
      return obj
    },
  },

  created() {
    this.handleChange()
    this.getWeekStartAndEnd(true)
  },

  methods: {
    handleChange() {
      if (this.chooseTab === 1) {
        // TODO: 睡眠日记
        if (this.sleepCharts) {
        } else {
          this.$nextTick(() => {
            let sleepDom = this.$refs.sleepChart
            this.sleepCharts = echarts.init(sleepDom)
            this.sleepCharts.setOption(this.sleepChartOpt)
          })
        }
      } else if (this.chooseTab === 2) {
        if (this.drugCharts) {
        } else {
          this.$nextTick(() => {
            let drugDom = this.$refs.drugChart
            this.drugCharts = echarts.init(drugDom)
            this.drugCharts.setOption(this.drugChartOpt)
          })
        }
      }
      this.loadDetail()
    },

    setEchart() {
      let sleepDom = this.$refs.sleepChart
      this.sleepCharts = echarts.init(sleepDom)
      this.sleepCharts.setOption(this.sleepChartOpt)
    },

    loadData(arg) {},
    loadDetail() {
      if (this.chooseTab == 1) {
        this.loadSleepDetail(this.id)
      } else if (this.chooseTab == 2) {
        this.loadDrugDetail(this.id)
      }
    },

    configDate(lieBeds, sleeps) {},
    configSleepData(data) {
      this.napLengthArr = [],       //小睡     
      this.nightWakeLengthArr = [],       //夜醒
      this.sleepEfficiencyArr = [],       //睡眠效率
      this.sleepLengthArr = [],       //睡眠总时长
      this.soberLengthArr = [],       //清醒总时长
      
      this.xAxisSleep.forEach((item,index) => {
        let isReport = false
        let reportData;
        data.forEach((report, j) => {
          if (report.recordDate.substr(5, 5) == item) {
            isReport = true
            reportData = report
          }
        })
        if (isReport) {
          this.napLengthArr.push(reportData.napLength)
          this.nightWakeLengthArr.push(reportData.nightWakeLength)
          this.sleepEfficiencyArr.push(reportData.sleepEfficiency)
          this.sleepLengthArr.push(reportData.sleepLength)
          this.soberLengthArr.push(reportData.soberLength)
        } else {
          this.napLengthArr.push(0)
          this.nightWakeLengthArr.push(0)
          this.sleepEfficiencyArr.push(0)
          this.sleepLengthArr.push(0)
          this.soberLengthArr.push(0)
        }
      })
      this.sleepCharts.setOption(this.sleepChartOpt)
    },

    configDrugData(list) {
      let key = 'medicineName'
      var keysArr = list.map(item=>item[key])
      var keys = [...new Set(keysArr)]
      var datas = []
      var newList = keys.map(item=> {
        datas.push(list.filter(i=>i[key] == item))
          return {
            [key]: item,
            list: list.filter(i=>i[key] == item)
          }
      })
      
      this.drugChartOpt.data = keys;
      this.drugChartOpt.series = []
      newList.forEach((item,index) => {
        let seriesData = []
        item.list.forEach((drugItem, drugIndex) => {
          seriesData.push(drugItem.recordDate + ' ' + drugItem.useDate),
          seriesData.push(drugItem.dosage.split('片')[0])
        })
        let seriesObj = {
          name: item.medicineName,
          type: 'scatter',
          data: [seriesData],
        }
        this.drugChartOpt.series.push(seriesObj)
      })
      console.log(this.drugChartOpt.series)
      this.drugCharts.setOption(this.drugChartOpt)
    },


    // 获取指定日期的那一周的开始、结束日期
    getWeekStartAndEnd(isBegin) {
      let now = new Date() // 日期
      let nowDayOfWeek = now.getDay() // 本周的第几天
      let nowDay = now.getDate() // 当前日
      let nowMonth = now.getMonth() // 当前月
      let nowYear = now.getFullYear() // 当前年
      let day = nowDayOfWeek || 7

      let weekStart = this.getWeekStartDate(nowYear, nowMonth, nowDay, nowDay + 1 - day)
      let weekEnd = this.getWeekEndDate(nowYear, nowMonth, nowDay, nowDay + 7 - day)
      this.dateStart = weekStart
      this.dateEnd = weekEnd
      this.getXAxisArr(isBegin)
      this.loadDetail()
    },

    getMonthStartAndEnd(val) {
      var d = new Date()
      var year = d.getFullYear()
      var month = d.getMonth() + 1
      month = month < 10 ? '0' + month : month
      var date = d.getDate()
      var firstday = year + '-' + month + '-' + '01'
      var lastday = ''
      if (
        month == '01' ||
        month == '03' ||
        month == '05' ||
        month == '07' ||
        month == '08' ||
        month == '10' ||
        month == '12'
      ) {
        lastday = year + '-' + month + '-' + 31
      } else if (month == '02') {
        if ((year % 4 == 0 && year % 100 != 0) || (year % 100 == 0 && year % 400 == 0)) {
          lastday = year + '-' + month + '-' + 29
        } else {
          lastday = year + '-' + month + '-' + 28
        }
      } else {
        lastday = year + '-' + month + '-' + 30
      }
      this.dateStart = firstday
      this.dateEnd = lastday
      
      this.getXAxisArr()
      this.loadDetail()
    },

    getXAxisArr(isBegin) {
      var startTimeValue = Date.parse(this.dateStart) // 转换为时间戳
      var endTimeValue = Date.parse(this.dateEnd) // 转换为时间戳
      let days = (endTimeValue - startTimeValue) / 86400000

      if (isBegin) {
        this.xAxisSleep = []
        this.xAxisDrug = []
        for (let i = 0; i <= days; i++) {
          let date = startTimeValue + i * 86400000
          this.xAxisSleep.push(formatDate(date, 'yyyy-MM-dd hh:mm:ss').substr(5, 5))
          this.xAxisDrug.push(formatDate(date, 'yyyy-MM-dd hh:mm:ss').substr(5, 5))
        }
         return;
      }

      if (this.chooseTab == 1) {
        this.xAxisSleep = []
        for (let i = 0; i <= days; i++) {
          let date = startTimeValue + i * 86400000
          this.xAxisSleep.push(formatDate(date, 'yyyy-MM-dd hh:mm:ss').substr(5, 5))
        }
      } else {
        this.xAxisDrug = []
        for (let i = 0; i <= days; i++) {
          let date = startTimeValue + i * 86400000
          this.xAxisDrug.push(formatDate(date, 'yyyy-MM-dd hh:mm:ss').substr(5, 5))
        }
      }
    },

    // 获得某一周的开始日期
    getWeekStartDate(nowYear, nowMonth, nowDay, nowDayOfWeek) {
      let weekStartDate = new Date(nowYear, nowMonth, nowDayOfWeek)
      return this.formatDate(weekStartDate)
    },

    // 获得某一周的结束日期
    getWeekEndDate(nowYear, nowMonth, nowDay, nowDayOfWeek) {
      let weekEndDate = new Date(nowYear, nowMonth, nowDayOfWeek)
      // this.weekEnd = weekEndDate;
      return this.formatDate(weekEndDate)
    },
    formatDate(date) {
      var myyear = date.getFullYear()
      var mymonth = date.getMonth() + 1
      var myweekday = date.getDate()
      if (mymonth < 10) {
        mymonth = '0' + mymonth
      }
      if (myweekday < 10) {
        myweekday = '0' + myweekday
      }
      return myyear + '-' + mymonth + '-' + myweekday
    },

    loadSleepDetail(id) {
      if (id) {
        this.id = id
      } else {
        return
      }
      if (this.dateStart == '') {
        return
      }
      // this.convertToDate()
      let urlPath = this.url.reportNew + this.id
      let that = this
      let formData = {
        endDate: that.dateEnd,
        startDate: that.dateStart,
      }
      // getAction(`/sys/dict/loadDictItem/${this.dict}`, { key: this.value })
      getAction(urlPath, formData).then((result) => {
        if (result.success) {
          
          that.configSleepData(result.result);
        } else {
          that.sleepDiary = false
        }
      })
    },
    loadDrugDetail(id) {
      if (id) {
        this.id = id
      } else {
        return
      }
      if (this.dateStart == '') {
        return
      }
      // this.convertToDate()
      let urlPath = this.url.drugReport + this.id
      let that = this
      let formData = {
        endDate: that.dateEnd,
        startDate: that.dateStart,
      }
      // getAction(`/sys/dict/loadDictItem/${this.dict}`, { key: this.value })
      getAction(urlPath, formData).then((result) => {
        if (result.success) {
          
          that.configDrugData(result.result);
        } else {
          that.sleepDiary = false
        }
      })
    },

    searchWeekReport() {
      this.getWeekStartAndEnd(false)
    },
    searchMonthReoprt() {
      this.getMonthStartAndEnd(this.currentTime)
      // this.sleepCharts.setOption(this.sleepChartOpt)
    },
    // range datePicker 查询
    searchRangeReoprt() {
      this.loadDetail()
    },
    onReportSleepDateChange(value, dateString) {
      // console.log(dateString[0], dateString[1])
      this.dateStart = dateString[0]
      this.dateEnd = dateString[1]

      this.getXAxisArr()
      this.loadDetail()
    },
    onReportDrugDateChange(value, dateString) {
      this.dateStart = dateString[0]
      this.dateEnd = dateString[1]

      this.getXAxisArr()
      this.loadDetail()
    }
  },
}
</script>

<style lang="less">
.head-search {
  height: 40px;
}
.charts-box {
  width: 100%;
  height: 400px;
  background-color: #ffffff;
  margin-top: 8px;
}

.report-sleep-chart {
  width: 100%;
  height: 400px;
}

.report-drug-chart {
  width: 100%;
  height: 400px;
}

.sleep-diary {
  padding: 0 10px 20px;
}
</style>