<template>
  <div class="sleep-diary">
    <a-tabs type="card" v-model="chooseTab" @change="handleChange">
      <a-tab-pane :key="1" tab="睡眠日记">
        <div class="search">
          <a-range-picker
            v-model="queryParam.diaryDate"
            format="YYYY-MM-DD"
          />

          <a-button @click="loadData" type="primary">查询</a-button>
        </div>
        <pa-sleep-diary-detail :dataSource="dataSource2" ref="detail" @handleTableChange="handleTableChange" />
      </a-tab-pane>

      <a-tab-pane :key="2" tab="睡眠数据">
        <a-table
          ref="table"
          size="middle"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          class="j-table-force-nowrap"
          @change="handleTableChange">

          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>
          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
          </template>
          <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
            <a-button
              v-else
              :ghost="true"
              type="primary"
              icon="download"
              size="small"
              @click="downloadFile(text)">
              下载
            </a-button>
          </template>

          <span slot="action" slot-scope="text, record">
            <a @click="handleChartDetail(record)">睡眠日记</a>
          </span>

        </a-table>
      </a-tab-pane>
    </a-tabs>
    <pa-sleep-diary-chart-modal ref="modalChart" @ok="modalFormOk"></pa-sleep-diary-chart-modal>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PaSleepDiaryChartModal from './PaSleepDiaryChartModal'
import PaSleepDiaryDetail from './PaSleepDiaryDetail'
import { getAction } from '@/api/manage'
import moment from 'moment'

export default {
  name: 'pa-sleep-diary',

  mixins:[JeecgListMixin, mixinDevice],

  components: {
    PaSleepDiaryChartModal,
    PaSleepDiaryDetail
  },

  data() {
    return {
      chooseTab: 1,
      columns: [
        {
          title:'日期',
          align:"center",
          dataIndex: 'recordTime',
          customRender:function (text) {
            return !text?"":(text.length>10?text.substr(0,10):text)
          }
        },
        {
          title:'学生名称',
          align:"center",
          dataIndex: 'patientName'
        },
        {
          title:'上床时间',
          align:"center",
          dataIndex: 'nightPrepareSleepDate'
        },
        {
          title:'睡觉时间',
          align:"center",
          dataIndex: 'nightAsleepDate'
        },
        {
          title:'醒来时间',
          align:"center",
          dataIndex: 'wakeDate'
        },
        {
          title:'起床时间',
          align:"center",
          dataIndex: 'getUpDate'
        },
        {
          title:'夜醒次数',
          align:"center",
          dataIndex: 'nightWakeTimes'
        },
        {
          title:'夜醒总时长（分钟）',
          align:"center",
          dataIndex: 'nightWakeTotalTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align:"center",
          width:147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list2: "/pa/paSleepDiary/list",
        list: '/pa/paSleepDiary/pageDiaryByPatientId/'

      },
      superFieldList:[],
      dataSource2: []
    }
  },

  created() {
    this.handleChange()
  },

  methods: {
    handleChange() {
      if (this.chooseTab === 2) {
        this.loadData2()
      }
    },

    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      this.ipagination = pagination;
      if (this.chooseTab === 1) {
        this.loadData();
      } else {
        this.loadData2()
      }
    },

    loadData(arg) {
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.$refs.detail.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      if (params.diaryDate && params.diaryDate.length) {
        params.startDate = params.diaryDate[0].format('YYYY-MM-DD')
        params.endDate = params.diaryDate[1].format('YYYY-MM-DD')
        delete params.diaryDate
      }

      this.loading = true;
      getAction(this.url.list + this.$store.getters.patientDetail.id, params).then((res) => {
        if (res.success) {
          res.result.records.forEach(item => {
            item.sleepList = [] // 睡眠
            item.napList = [] // 小睡
            item.wakeList = [] // 清醒
            item.medicineList = [] // 服药
            item.bedridden = {} // 卧床
            item.sleepPrescription = {} // 睡眠处方
            let startDateTamp = moment(item.recordTime + ' 10:00:00').unix() - 24 * 60 * 60 // 昨天10点 图的起点
            let nightPrepareSleepDateTamp = moment(item.nightPrepareSleepDate).unix() // 上床时间
            let nightAsleepDateTamp = moment(item.nightAsleepDate).unix() // 睡着时间
            let wakeDateTamp = moment(item.wakeDate).unix() // 醒来时间
            let getUpDateTamp = moment(item.getUpDate).unix() // 起床时间
            let adviceGetUpTime = item.adviceSleep && item.adviceSleep.getUpTime ? moment(item.recordTime + ' ' + item.adviceSleep.getUpTime).unix() : 0 // 睡眠处方起床时间
            let adviceSleepTime = item.adviceSleep && item.adviceSleep.sleepTime ? moment(item.recordTime + ' ' + item.adviceSleep.sleepTime).unix() - 24 * 60 * 60 : 0 // 睡眠处方睡觉时间
            if (adviceGetUpTime - adviceSleepTime > 24 * 60 * 60) {
              if (adviceSleepTime < startDateTamp) {
                adviceSleepTime = adviceSleepTime + 24 * 60 * 60
              } else {
                adviceGetUpTime = adviceGetUpTime - 24 * 60 * 60
              }
            }
            // 卧床
            item.bedridden = {
              left: Math.round((nightPrepareSleepDateTamp - startDateTamp) * 40 / 3600),
              width: Math.round((getUpDateTamp - nightPrepareSleepDateTamp) * 40 / 3600)
            }

            // 睡眠处方
            item.sleepPrescription = {
              left: Math.round((adviceSleepTime - startDateTamp) * 40 / 3600),
              width: Math.round((adviceGetUpTime - adviceSleepTime) * 40 / 3600)
            }

            // 晚上睡眠时间段
            item.sleepList.push({
              left: Math.round((nightAsleepDateTamp - startDateTamp) * 40 / 3600),
              width: Math.round((wakeDateTamp - nightAsleepDateTamp) * 40 / 3600)
            })

            if (item.nightWakeTotalTime) {
              const wakeItem = Math.round(item.nightWakeTotalTime / item.nightWakeTimes)
              const wakeWidth = Math.round((item.nightWakeTotalTime * 60) * 40 / 3600)
              const wakeItemWidth = Math.round((wakeItem * 60) * 40 / 3600)
              const wakeDash = Math.round((item.sleepList[0].width - wakeWidth) / (item.nightWakeTimes + 1))
              for(let i = 0; i < item.nightWakeTimes; i++) {
                item.wakeList.push({
                  left: wakeDash * (i + 1) + item.sleepList[0].left,
                  width: wakeItemWidth
                })
              }
            }

            if (nightAsleepDateTamp) {
              // 晚上睡前清醒时间段
              item.wakeList.push({
                left: Math.round((nightPrepareSleepDateTamp - startDateTamp) * 40 / 3600),
                width: Math.round((nightAsleepDateTamp - nightPrepareSleepDateTamp) * 40 / 3600)
              })
              // 早晨醒后清醒时间段
              item.wakeList.push({
                left: Math.round((wakeDateTamp - startDateTamp) * 40 / 3600),
                width: Math.round((getUpDateTamp - wakeDateTamp) * 40 / 3600)
              })
            } else {
              // 整晚没睡清醒时间段
              item.wakeList.push({
                left: Math.round((nightPrepareSleepDateTamp - startDateTamp) * 40 / 3600),
                width: Math.round((getUpDateTamp - nightPrepareSleepDateTamp) * 40 / 3600)
              })
            }

            // 日间小睡
            if (item.naps && item.naps.length) {
              item.naps.forEach(it => {
                let sleepDateTamp = moment(it.startDate).unix()
                let wakeUpDateTamp = moment(it.endDate).unix()
                item.napList.push({
                  left: Math.round((sleepDateTamp - startDateTamp) * 40 / 3600),
                  width: Math.round((wakeUpDateTamp - sleepDateTamp) * 40 / 3600)
                })
              })
            }

            // 服药记录
            if (item.medicines && item.medicines.length) {
              item.medicines.forEach(it => {
                let recordDate = moment(it.recordDate + ' ' + it.useDate).unix()
                item.medicineList.push({
                  left: Math.round((recordDate - startDateTamp) * 40 / 3600),
                })
              })
            }
          })
          this.dataSource2 = res.result.records;
          if(res.result.total)
          {
            this.$refs.detail.ipagination.total = res.result.total;
          }else{
            this.$refs.detail.ipagination.total = 0;
          }
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    loadData2(arg) {
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      params.patientId = this.$store.getters.patientDetail.id
      this.loading = true;
      getAction(this.url.list2, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records||res.result;
          if(res.result.total)
          {
            this.ipagination.total = res.result.total;
          }else{
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    handleChartDetail(record) {
      this.$refs.modalChart.edit(record);
      this.$refs.modalChart.title="睡眠日记";
      this.$refs.modalChart.disableSubmit = true;
    }
  }
}
</script>

<style lang="less">
.sleep-diary {
  padding: 0 10px 20px;

  .ant-calendar-picker {
    margin-right: 12px;
  }
}
</style>