<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="睡眠日记日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="recordTime">
              <j-date placeholder="请选择睡眠日记日期" v-model="model.recordTime" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="昨晚准备睡觉时间" :labelCol="labelCol" :wrapperCol="wrapperCol"
                               prop="nightPrepareSleepDate">
              <j-date placeholder="请选择昨晚准备睡觉时间" v-model="model.nightPrepareSleepDate"
                      style="width: 100%" :showTime="true" dateFormat="YYYY-MM-DD HH:mm:ss"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="昨晚睡觉时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nightAsleepDate">
              <j-date placeholder="请选择昨晚睡觉时间" v-model="model.nightAsleepDate" style="width: 100%" :showTime="true"
                      dateFormat="YYYY-MM-DD HH:mm:ss"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="醒来时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wakeDate">
              <j-date placeholder="请选择醒来时间" v-model="model.wakeDate" style="width: 100%" :showTime="true"
                      dateFormat="YYYY-MM-DD HH:mm:ss"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="起床时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="getUpDate">
              <j-date placeholder="请选择起床时间" v-model="model.getUpDate" style="width: 100%" :showTime="true"
                      dateFormat="YYYY-MM-DD HH:mm:ss"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="夜醒次数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nightWakeTimes">
              <a-input-number v-model="model.nightWakeTimes" placeholder="请输入夜醒次数" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="夜醒总时长（分钟）" :labelCol="labelCol" :wrapperCol="wrapperCol"
                               prop="nightWakeTotalTime">
              <a-input-number v-model="model.nightWakeTotalTime" placeholder="请输入夜醒总时长（分钟）" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否小睡" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isNap">
              <j-dict-select-tag v-model="model.isNap" placeholder="请选择是否小睡" dictCode="yn"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="早晨感觉如何" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="mornFeelType">
              <j-dict-select-tag v-model="model.mornFeelType" placeholder="请选择早晨感觉如何" dictCode="weakup_style"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
              <a-textarea :rows="5" v-model="model.remark" placeholder="请输入备注" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction } from '@/api/manage'
  import moment, { Moment } from 'moment';

  export default {
    name: 'PaSleepDiaryForm',
    components: {},
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        validatorRules: {},
        url: {
          add: '/pa/paSleepDiary/add',
          edit: '/pa/paSleepDiary/edit',
          queryById: '/pa/paSleepDiary/queryById'
        }
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
    },
    methods: {
      disabledDate() {
        return new Date() && new Date() < moment().endOf('day');
      },
      moment,
      add() {
        this.edit(this.modelDefault)
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.visible = true
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }

        })
      }
    }
  }
</script>