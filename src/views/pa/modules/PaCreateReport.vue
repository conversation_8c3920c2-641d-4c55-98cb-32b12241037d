<template>
  <j-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :maskClosable="false"
    :footer="true"
    switchFullscreen
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <pa-create-report-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"/>
  </j-modal>
</template>

<script>
import PaCreateReportForm from './PaCreateReportForm.vue'

export default {
  name: 'PaCreateReport',
  components: {
    PaCreateReportForm
  },
  data() {
    return {
      title:'',
      width:800,
      visible: false,
      disableSubmit: false,
      isEdit: false,
    }
  },
  methods:{
    add () {
      this.visible=true
      this.$nextTick(()=>{
        this.$refs.realForm.add();
      })
    },
    detail (record) {
      this.visible=true
      this.$nextTick(()=>{
        this.$refs.realForm.loadData(record);
      })
    },
    close () {
      this.$emit('close');
      this.visible = false;
    },
    handleOk () {
      this.$refs.realForm.submitForm();
    },
    submitCallback(){
      this.$emit('ok');
      this.visible = false;
    },
    handleCancel () {
      this.close()
    }
  }
}
</script>

<style scoped>
</style>