<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item v-if="step > 1 && !isEdit" :label="step === 2 ? '一级标签' : '二级标签'" :labelCol="labelCol" :wrapperCol="wrapperCol">
              {{ model.parentName }}
            </a-form-model-item>
            <a-form-model-item label="标签名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入标签名称" @change="trimmedStr"></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction,getAction } from '@/api/manage'
  import { duplicateCheck } from '@api/api'

  export default {
    name: 'PaGroupForm',
    components: {},
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      },
      step: {
        type: Number,
        default: 1
      }
    },
    data() {
      return {
        model: {
          parentName: '',
          parentId: '',
          name: ''
        },
        detail: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        validatorRules: {
          name: [
            { required: true, message: '请输入标签名称!' },{validator: this.validateName,}
          ]
        },
        url: {
          add: '/label/add',
          edit: '/label/edit',
          check: '/label/checkName',
        },
        isEdit: false,
        parentName: ''
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
    },
    methods: {
      add(record) {
        this.isEdit = false
        if (!record) return
        this.model.parentName = record.name
        this.model.parentId = record.id
      },
      validateName(rule, value, callback){
        var params = {
          name: this.model.name,
          parentId: this.model.parentId,
          id: this.model.id
        };
        getAction(this.url.check,params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback("标签名称已存在!")
          }
        })
      },
      edit(record) {
        this.isEdit = true
        this.model = Object.assign({}, record)
        this.visible = true
      },
      trimmedStr(){
        this.model.name = this.model.name.replace(/^\s*/,"")
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.isEdit) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let params = Object.assign({}, this.model)
            httpAction(httpurl, params, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }

        })
      }
    }
  }
</script>