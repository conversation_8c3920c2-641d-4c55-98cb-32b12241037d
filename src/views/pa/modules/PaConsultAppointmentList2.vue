<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学生姓名">
              <a-input placeholder="请输入学生姓名" v-model="queryParam.patientName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="电话">
              <a-input placeholder="请输入电话" v-model="queryParam.patientPhone"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="咨询师姓名">
              <a-input placeholder="请输入咨询师姓名" v-model="queryParam.doctorName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange">
        <template slot="appointmentMethod" slot-scope="text, record"> 
          {{record.appointmentMethod == 1 ? '自己预约' : record.appointmentMethod == 2 ? '咨询师代预约' : record.appointmentMethod == 3 ? '老师代预约' : ''}}
        </template>

        <template slot="consultationMethod" slot-scope="text, record"> 
          {{record.consultationMethod == 1 ? '面对面咨询' : record.consultationMethod == 2 ? '线上咨询' : ''}}
        </template>

        <template slot="status" slot-scope="text"> 
          <a-badge :color="text | statusTypeFilter" :text="text | statusFilter"/>
          <!-- 1_待确认，2_已确认，3_已完成，4_已取消，5_已逾约 -->
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
          <a-divider v-if="record.status == '1'" type="vertical" />
          <a v-if="record.status == '1'" @click="handleConfrim(record)">预约确认</a>
          <a-divider v-if="record.status == '1' || record.status == '2'" type="vertical" />
          <a v-if="record.status == '1' || record.status == '2'" @click="handleCancel(record)">取消预约</a>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <paConsultAppointment-modal ref="modalForm" @ok="modalFormOk"></paConsultAppointment-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import PaConsultAppointmentModal from './PaConsultAppointmentModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, httpAction } from '@/api/manage'
import { Modal } from 'ant-design-vue'

const statusMap = {
    3: {
      color: '#D9D9D9',
      text: '已完成'
    },
    2: {
      color: 'green',
      text: '已确认'
    },
    4: {
      color: 'yellow',
      text: '已取消'
    },
    1: {
      color: 'red',
      text: '待确认'
    },
    5: {
      color: 'purple',
      text: '已逾约'
    },
    0: {
      color: '#D9D9D9',
      text: '未知'
    }
  }

export default {
  name: "PaConsultAppointmentList",
  mixins: [JeecgListMixin],
  components: {
    PaConsultAppointmentModal
  },
  data () {
    return {
      description: '预约咨询',
      queryParam: {},
      // 表头 学生姓名，电话、咨询师姓名、预约方式、咨询方式、预约日期、预约时间、状态
      columns: [
        {
          title: '学生姓名',
          align: "center",
          dataIndex: 'patientName'
        },
        {
          title: '班级',
          align: "center",
          dataIndex: 'className'
        },
        {
          title: '电话',
          align: "center",
          dataIndex: 'patientPhone'
        },
        {
          title: '咨询师姓名',
          align: "center",
          dataIndex: 'doctorName'
        },
        {
          title: '预约方式',
          align: "center",
          dataIndex: 'appointmentMethod',
          scopedSlots: { customRender: 'appointmentMethod' },
        },
        {
          title: '咨询方式',
          align: "center",
          dataIndex: 'consultationMethod',
          scopedSlots: { customRender: 'consultationMethod' },
        },
        {
          title: '预约日期',
          align: "center",
          dataIndex: 'visitDate'
        },
        {
          title: '预约时间',
          align: "center",
          dataIndex: 'visitTime'
        },
        {
          title: '状态',
          align: "center",
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' },
        }
      ],
      url: {
        list: "/pa/paAppointment/list",
        delete: "/pa/paAppointment/delete",
        edit: '/pa/paAppointment/edit',
        confirm: '/pa/paAppointment/confirm',
        deleteBatch: "/pa/paAppointment/deleteBatch",
        exportXlsUrl: "/pa/paAppointment/exportXls",
        importExcelUrl: "/pa/paAppointment/importExcel",
      },
      chooseTab: 1,
    }
  },
  filters: {
    statusFilter(type) {
      return statusMap[type].text
    },
    statusTypeFilter(type) {
      return statusMap[type].color
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  methods: {
    loadData(arg, id) {
      if(!this.url.list){
        this.$message.error("请设置url.list属性!")
        return
      }
      this.dataSource = []
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }

      var params = this.getQueryParams();//查询条件
      if (id) {
        params.id = id
      }
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records||res.result;
          if(res.result.total)
          {
            this.ipagination.total = res.result.total;
          }else{
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },
    // TODO:
    handleConfrim(record) {
      Modal.confirm({
        title: '确认预约',
        content: '是否确认预约？',
        onOk: () => {
          let that = this
          let httpurl = this.url.confirm
          let method = 'put'
          httpAction(httpurl, record, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.modalFormOk()
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.loading = false
          })
        },
        onCancel: () => {
        },
        class: 'test',
      });
    },
    handleCancel(record) { 
      Modal.confirm({
        title: '取消预约',
        content: '取消后不可恢复，是否确认删除？',
        onOk: () => {
          this.handleUpdateStatus(record.id, '4')
        },
        onCancel: () => {
        },
        class: 'test',
      });
    },
    handleUpdateStatus (id, status, doctorNote = '') {
      let that_ = this
      that_.loading = true
      let param = {
        id,
        status
      }
      if (doctorNote) param.doctorNote = doctorNote
      httpAction(that_.url.edit, param, 'put').then((res) => {
        if (res.success) {
          that_.$message.success(res.message)
          that_.modalFormOk()
        } else {
          that_.$message.warning(res.message)
        }
      }).finally(() => {
        that_.loading = false
      })
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>