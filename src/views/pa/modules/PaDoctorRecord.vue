<template>
  <div class="doctor-record">
    <div class="table-operator">
      <div class="search">
        <div class="search-item">
          <div class="item-label">任务名称：</div>
          <div class="item-content">
            <j-input placeholder="请输入任务名称" v-model="queryParam.title"></j-input>
          </div>
        </div>
        <div class="search-item">
          <div class="item-label">状态：</div>
          <div class="item-content">
            <a-select v-model="queryParam.status" placeholder="请选择状态" allowClear style="width: 120px">
              <a-select-option :value="0">待完成</a-select-option>
              <a-select-option :value="1">已完成</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="search-item">
          <div class="item-label">完成时间：</div>
          <div class="item-content">
            <a-range-picker
              v-model="diaryDate"
              format="YYYY-MM-DD"
              allowClear
              @change='changeDateQuery'
            />
          </div>
        </div>
      </div>

      <a-button @click="loadData" type="primary">查询</a-button>
    </div>
    <a-button style="margin-bottom: 12px;" @click="handleAdd" type="primary">新增</a-button>
    <a-table
      ref="table"
      size="middle"
      bordered
      rowKey="id"
      class="j-table-force-nowrap"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      @change="handleTableChange"
      :loading="loading">
      <template slot="status" slot-scope="text, record">
        {{ record.status == 1 ? '已完成' : '待完成' }}
      </template>
      <span slot="action" slot-scope="text, record">
        <a @click="handleDetail(record)">详情</a>
        <a-divider type="vertical"/>
        <a @click="handleEdit(record)">编辑</a>
        <a-divider type="vertical"/>
        <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a>删除</a>
        </a-popconfirm>
      </span>
    </a-table>

    <PaDoctorRecordModal ref="modalForm" @ok="modalFormOk"></PaDoctorRecordModal>
  </div>
</template>

<script>
import { filterObj } from '@/utils/util'
import { getAction } from '@/api/manage'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PaDoctorRecordModal from './PaDoctorRecordModal.vue'

export default {
  name: 'pa-doctor-record',

  mixins:[JeecgListMixin, mixinDevice],

  components: {
    PaDoctorRecordModal
  },

  props: {
    id: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: {
        reserveStatus: 1
      },
      /* 数据源 */
      diaryDate: [],
      dataSource:[],
      loading:false,
      /* 排序参数 */
      isorter:{
        column: 'createTime',
        order: 'desc',
      },
      columns: [
        {
          title: '任务名称',
          align: 'center',
          dataIndex: 'title'
        },
        {
          title: '学生姓名',
          align: 'center',
          dataIndex: 'patientName'
        },
        {
          title: '状态',
          align: 'center',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '完成时间',
          align: 'center',
          dataIndex: 'limitDate'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align:"center",
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/ta/taDoctorAdvice/list',
        delete: '/ta/taDoctorAdvice/delete'
      },
      paPatientSchemeId: '',
      ipagination:{
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
    }
  },

  methods: {
    changeDateQuery(){
      if (this.diaryDate.length){
        this.queryParam.startDate = this.diaryDate[0].format('YYYY-MM-DD')
        this.queryParam.endDate = this.diaryDate[1].format('YYYY-MM-DD')
      }else {
        this.queryParam.startDate = ''
        this.queryParam.endDate = ''
      }
    },

    loadData(arg) {
      if(!this.url.list){
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      params.patientId = this.id
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records||res.result;
          if(res.result.total)
          {
            this.ipagination.total = res.result.total;
          }else{
            this.ipagination.total = 0;
          }
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },

    editPrescription() {
      this.$refs.modalPrescription.title="修改睡眠处方"
      this.$refs.modalPrescription.loadData(this.id);
    },

    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      console.log(pagination)
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
      this.loadData1();
    },
  }
}
</script>

<style lang="less">
.doctor-record {
  padding: 0 10px 20px;

  .table-operator {
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;

    .search {
      display: flex;

      .search-item {
        display: flex;
        margin-right: 20px;

        .item-label {
          margin-bottom: 8px;
          line-height: 32px;
        }
      }
    }
  }
}
</style>