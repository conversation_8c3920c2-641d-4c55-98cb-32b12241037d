<template>
  <a-spin :spinning="confirmLoading">
    <a-table rowKey="id" :columns="columns" :data-source="dataSource" bordered :pagination="false"
              size="small">
      <template slot="totalNum" slot-scope="scope">
        <span>{{ scope.totalNum }}分钟</span>
      </template>
    </a-table>
  </a-spin>
</template>

<script>
  import { mapGetters } from 'vuex'

  export default {
    name: 'PaSleepDiaryDetailForm',
    data() {
      return {
        confirmLoading: false,
        detail: {},
        columns: [
          {
            title: '',
            children: [
              {
                title: '开始时间',
                align: 'center',
                width: 130,
                dataIndex: 'startDate'
              },
              {
                title: '结束时间',
                align: 'center',
                width: 130,
                dataIndex: 'endDate'
              },
              {
                title: '小睡时长（分钟）',
                align: 'center',
                width: 140,
                scopedSlots: { customRender: 'totalNum' }
              }
            ]
          }
        ],
        dataSource: [],
        disabled: false,
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    methods: {
      ...mapGetters(['nickname', 'userInfo']),
      edit(value) {
        this.disabled = true
        this.detail = value
        this.columns[0].title = value.recordTime
        this.dataSource = value.naps
      },
    }
  }
</script>

<style lang="scss" scoped>
.table-title {
  display: block;
  text-align: center;
}
.title-item {
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-select {
    width: 50px;
    margin: 0 8px;
  }
}
</style>