<template>
  <j-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :maskClosable="false"
    :footer="false"
    switchFullscreen
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel">
    <pa-daily-plan-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"/>
  </j-modal>
</template>

<script>
import PaDailyPlanForm from './PaDailyPlanForm.vue'

export default {
  name: 'PaDailyPlanModal',
  components: {
    PaDailyPlanForm
  },
  data() {
    return {
      title:'',
      width:800,
      visible: false,
      disableSubmit: false,
      isEdit: false,
    }
  },
  methods:{
    detail (record) {
      this.visible=true
      this.$nextTick(()=>{
        this.$refs.realForm.loadData(record);
      })
    },
    close () {
      this.$emit('close');
      this.visible = false;
    },
    submitCallback(){
      this.$emit('ok');
      this.visible = false;
    },
    handleCancel () {
      this.close()
    }
  }
}
</script>

<style scoped>
</style>