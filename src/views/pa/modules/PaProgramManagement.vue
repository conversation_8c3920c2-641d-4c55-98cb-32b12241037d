<template>
  <div class="report-management">
    <div class="table-operator">
      <div>
        <a-select v-model="patientSchemeGroupId" placeholder="请选择方案" :allowClear="false">
          <a-select-option v-for="item in programList" :key="item.id" :value="item.id">
            {{ item.name }}
          </a-select-option>
        </a-select>
        <a-button @click="setData" type="primary">查询</a-button>
      </div>

      <a-button @click="handleAdd" type="primary" icon="plus">新增方案</a-button>
    </div>

    <div class="current-info" v-if="program.name">
      <template v-if="program.status === 0">
        <p class="info">当前执行方案：{{ program.name }}</p>
        <a-button @click="changeStatus(1)" icon="pause">暂停</a-button>
      </template>
      <template v-if="program.status === 2">
        <p class="info">此方案已暂停</p>
        <a-button @click="changeStatus(2)" :disabled="hasInProgress">恢复方案</a-button>
        <p class="info" v-if="hasInProgress">若要恢复此方案，请先暂停进行中的方案</p>
      </template>
      <template v-if="program.status === 1">
        <p class="info">此方案已完成</p>
      </template>
      <template v-if="program.status === 4">
        <p class="info">此方案未开始</p>
      </template>
      <template v-if="program.status === 5">
        <p class="info">此方案已过期</p>
      </template>
    </div>

    <a-table
      ref="table"
      size="middle"
      bordered
      rowKey="id"
      class="j-table-force-nowrap"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      @change="handleTableChange"
      :loading="loading">
      <template slot="status" slot-scope="scope">
        <span class="error-text" v-if="scope.status === 0">待完成</span>
        <span class="default-text" v-if="scope.status === 3">进行中</span>
        <span class="success-text" v-if="scope.status === 1">已完成</span>
        <span class="error-text" v-if="scope.status === 2">暂停中</span>
      </template>
      <template slot="treatmentCondition" slot-scope="text, record">
        <editable-cell :text="text" @change="onCellChange(record.id, 'treatmentCondition', $event)"/>
      </template>
      <span slot="action" slot-scope="text, record">
        <a @click="handleDetail(record)">详情</a>
      </span>
    </a-table>

    <pa-program-modal ref="modalForm" @ok="modalFormOk"/>
    <pa-daily-plan-modal ref="dailyFrom" />
  </div>
</template>

<script>
const EditableCell = {
  template: `
    <div class="editable-cell">
      <div v-if="editable">
        <j-dict-select-tag class="select-tag" @change="handleChange" @pressEnter="check" v-model="value" placeholder="请选择治疗情况"
                                dictCode="treatment_condition"/>
        <a-icon
          type="check"
          @click="check"
        />
      </div>
      <div v-else class="editable-cell-text-wrapper">
        {{ transform(value) || ' ' }}
        <a-icon type="edit" class="editable-cell-icon" @click="edit" />
      </div>
    </div>
  `,
  props: {
    text: Number
  },
  data() {
    return {
      value: this.text,
      editable: false,
    }
  },
  methods: {
    handleChange(value) {
      this.value = value
    },
    check() {
      this.editable = false
      this.$emit('change', this.value)
    },
    edit() {
      this.editable = true
    },
    transform(value) {
      if (value == 1) {
        return '很好'
      } else if (value == 2) {
        return '好'
      } else if (value == 3) {
        return '一般'
      } else if (value == 4) {
        return '差'
      } else if (value == 5) {
        return '很差'
      }
    }
  }
}

import PaProgramModal from './PaProgramModal.vue'
import PaDailyPlanModal from './PaDailyPlanModal.vue'
import {getAction, putAction} from '@/api/manage'
import moment from 'moment'


export default {
  name: 'pa-report-management',

  components: {
    PaProgramModal,
    PaDailyPlanModal,
    EditableCell
  },

  props: {
    id: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      columns: [
        {
          title: '计划内容名称',
          align: 'center',
          dataIndex: 'schemeName'
        },
        {
          title: '状态',
          align: 'center',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '治疗内容开始时间',
          align: 'center',
          dataIndex: 'startDate'
        },
        {
          title: '治疗内容完成时间',
          align: 'center',
          dataIndex: 'endDate'
        },
        {
          title: '治疗情况',
          dataIndex: 'treatmentCondition',
          align:"center",
          width: 200,
          scopedSlots: { customRender: 'treatmentCondition' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
    ],
      url: {
        list: '/pa/paPatientSchemeGroup/list',
        changeStatusById: '/pa/paPatientSchemeGroup/changeStatusById',
        schemeGroupDetail: '/pa/paPatientSchemeGroup/schemeGroupDetail',
        sublist: '/pa/paPatientScheme/sublist',
        measurelist: '/pa/paPatientScheme/measurelist',
        trainlist: '/pa/paPatientScheme/trainlist',
        editSub: '/pa/paPatientScheme/editSub',
      },
      programList: [],
      chooseProgram: '',
      isPause: false,
      loading:false,
      program: {},
      // hasInProgress: false, // 是否有进行中的方案
      patientSchemeGroupId: '',
      dataSource: [],
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
    }
  },

  mounted() {
    this.patientSchemeGroupId = this.$route.query.patientSchemeGroupId
    this.loadList(this.patientSchemeGroupId ? false : true)
  },

  computed: {
    // 是否有进行中的方案
    hasInProgress() {
      return !!this.programList.filter(item => item.status == 0).length
    }
  },

  methods: {
    handleTableChange(pagination, filters, sorter) {
      this.ipagination = pagination;
    },

    loadList(flag = true) {
      var params = {}
      params.patientId = this.$store.getters.patientDetail.id
      params.pageNo = 1
      params.pageSize = 10000
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          res.result.records.forEach(item => {
            if (moment(item.startDate).unix() > (Date.parse(new Date()) / 1000)) item.status = 4
            // if (moment(item.endDate).unix() < (Date.parse(new Date()) / 1000)) item.status = 5
          })
          this.programList = res.result.records || res.result
          if (!flag) {
            this.setData()
          } else {
            this.getTodayScheme()
          }

        }
      })
    },

    getTodayScheme(id) {
      const params = {
        patientId: this.$store.getters.patientDetail.id
      }
      if (id) params.patientSchemeGroupId = this.patientSchemeGroupId
      getAction(this.url.schemeGroupDetail, params).then((res) => {
        if (res.success && res.result) {
          this.program = res.result
          this.dataSource = this.program.paPatientSchemeList
          this.patientSchemeGroupId = this.program.id
        }
      })
    },



    setData() {
      this.getTodayScheme(this.patientSchemeGroupId)
    },

    changeStatus(type) {
      if (type === 1) {
        this.$confirm({
          title: '暂停方案',
          content: '方案暂停后，学生无执行中方案，可以查询出方案【恢复方案】继续执行，或为学生新增方案！',
          onOk:() => {
            this.isPause = true
            this.changeStatusById(2)
          }
        })
      } else {
        this.isPause = false
        this.changeStatusById(0)
      }
    },

    changeStatusById(status) {
      let params = {
        id: this.patientSchemeGroupId, // 当前执行方案id
        status
      }
      getAction(this.url.changeStatusById, params).then((res) => {
        if (res.success) {
          this.programList.filter(item => item.id === this.patientSchemeGroupId)[0].status = status ? 2 : 0
          this.program.status = status ? 2 : 0
          this.$message.success('修改成功！')
        } else {
          this.$message.success('修改失败，请稍后重试！')
        }
      }).catch(() => {
        this.$message.success('修改失败，请稍后重试！')
      })
    },

    handleDetail(record) {
      this.$refs.dailyFrom.title = '计划详情';
      this.$refs.dailyFrom.detail(record);
      this.$refs.dailyFrom.disableSubmit = true;
    },

    modalFormOk() {
      // 新增/修改 成功时，重载列表
      this.loadList()
    },

    handleAdd: function () {
      this.$refs.modalForm.add();
      this.$refs.modalForm.title = "新增";
      this.$refs.modalForm.disableSubmit = false;
    },

    onCellChange(key, dataIndex, value) {
      this.updateSchemeSub(key,value)
      if (this.activeKey === 1) {
        const dataSource = [...this.dataSource1]
        const target = dataSource.find(item => item.key === key)
        if (target) {
          target[dataIndex] = value
          this.dataSource1 = dataSource
        }
      } else if (this.activeKey === 2) {
        const dataSource = [...this.dataSource2]
        const target = dataSource.find(item => item.key === key)
        if (target) {
          target[dataIndex] = value
          this.dataSource2 = dataSource
        }
      } else if (this.activeKey === 3) {
        const dataSource = [...this.dataSource3]
        const target = dataSource.find(item => item.key === key)
        if (target) {
          target[dataIndex] = value
          this.dataSource3 = dataSource
        }
      }
    },

    updateSchemeSub(id,status) {
      let params = {}
      params.id = id
      params.treatmentCondition = status
      putAction(this.url.editSub, params).then((res) => {

      })
    },
  }
}
</script>

<style lang="less" scoped>
.report-management {
  padding: 0 10px 20px;

  .table-operator {
    display: flex;
    justify-content: space-between;

    .ant-select {
      width: 200px;
      margin-right: 12px;
    }
  }

  .current-info {
    display: flex;
    align-items: center;
    padding: 10px 0 15px 0;

    .ant-btn {
      margin: 0 12px;
    }

    .info {
      margin: 0;
    }
  }
}

.success-text {
  color: green;
}

.default-text {
  color: blue;
}

.error-text {
  color: red;
}
</style>

<style lang="less">
.select-tag {
  width: 150px;

  .ant-select-selection {
    width: 150px;
  }
}
</style>