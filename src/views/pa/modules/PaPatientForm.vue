<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-card title="基本信息">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="老师名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="doctorName">
                <a-input v-model="model.doctorName" :defaultValue="nickname()" disabled placeholder="请输入老师名称"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="手机号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="telphone">
                <a-input v-model="model.telphone" placeholder="请输入手机号"></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <template v-if="!model.id">
              <a-col :span="12">
                <a-form-model-item label="登录密码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="password">
                  <a-input type="password" placeholder="请输入登录密码" v-model="model.password" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="确认密码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="confirmpassword">
                  <a-input type="password" @blur="handleConfirmBlur" placeholder="请重新输入登录密码" v-model="model.confirmpassword" />
                </a-form-model-item>
              </a-col>
            </template>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="标签" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="labels">
                <j-label-select v-model="model.labels" :defaultValue="model.labels" @change="changeSelectedGroups"></j-label-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="真实姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
                <a-input v-model="model.name" placeholder="请输入真实姓名"></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="生日" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="birthday">
                <j-date placeholder="请选择生日" v-model="model.birthday" style="width: 100%" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sex">
                <j-dict-select-tag type="radio" v-model="model.sex" dictCode="sex" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-card>
        <br />
        <a-card title="其他信息">
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="昵称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nickname">
                <a-input v-model="model.nickname" placeholder="请输入昵称"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="number">
                <a-input v-model="model.number" placeholder="请输入编号"></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="用户头像" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="avatarUrl">
                <j-image-upload class="avatar-uploader" text="上传" v-model="model.avatarUrl"></j-image-upload>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="身高" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stature">
                <a-input v-model="model.stature" placeholder="请输入身高" suffix="cm"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="体重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weight">
                <a-input v-model="model.weight" placeholder="请输入体重" suffix="kg"></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="职业" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="profession">
                <a-input v-model="model.profession" placeholder="请输入职业"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="婚否" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="maritalStatus">
                <j-dict-select-tag type="radio" v-model="model.maritalStatus" dictCode="marital_status" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="文化程度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cultural">
                <j-dict-select-tag placeholder="请选择文化程度" v-model="model.cultural" dictCode="cultural" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="民族" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nationality">
                <j-dict-select-tag placeholder="请选择民族" v-model="model.nationality" dictCode="nationality" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="主诉" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="principleAction">
                <a-input v-model="model.principleAction" placeholder="请输入主诉"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idCard">
                <a-input v-model="model.idCard" placeholder="请输入身份证号"></a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="上床时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="goBedTime">
                <a-time-picker placeholder="请选择上床时间" v-model="model.goBedTime" format="HH:mm" value-format="HH:mm" style="width: 100%" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="起床时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wakeUpTime">
                <a-time-picker placeholder="请选择起床时间" v-model="model.wakeUpTime" format="HH:mm" value-format="HH:mm" style="width: 100%" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="是否服用药物" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isMedicine">
                <j-dict-select-tag type="radio" v-model="model.isMedicine" placeholder="请输入是否服用促进睡眠药物" dictCode="yn" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="是否开启日志提醒" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isSleepDiaryRemind">
                <j-dict-select-tag type="radio" v-model="model.isSleepDiaryRemind" placeholder="请输入是否开启睡眠日志填写提醒" dictCode="yn" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-card>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction } from '@/api/manage'
import { mapGetters } from 'vuex'
import moment from 'moment'
import JLabelSelect from '@/components/jeecgbiz/JLabelSelect'

export default {
  name: 'PaPatientForm',
  components: { JLabelSelect },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      model: {
        labels: null
      },
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        labels: [
          { required: false, message: '请输入标签!' }
        ],
        name: [
          { required: true, message: '请输入真实姓名!' }
        ],
        number: [
          { required: false, message: '请输入编号!' }
        ],
        birthday: [
          { required: true, message: '请输入生日!' }
        ],
        sex: [
          { required: true, message: '请输入性别!' }
        ],
        telphone: [
          { required: true, pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号!' }
        ],
        idCard: [
          { required: false, pattern: /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/, message: '请输入正确的身份证号!' }
        ],
        password: [{
          required: true,
          pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,
          message: '密码由8位数字、大小写字母和特殊符号组成!'
        },
        { validator: this.validateToNextPassword, trigger: 'change' }],
        confirmpassword: [{ required: true, message: '请重新输入登录密码!' },
        { validator: this.compareToFirstPassword }]
      },
      confirmDirty: false,
      url: {
        add: '/pa/paPatient/add',
        edit: '/pa/paPatient/edit',
        queryById: '/pa/paPatient/queryById',
        queryGroup: '/pa/paGroup/listAll'
      },
      groups: [],
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
    // this.loadGroupList()
  },
  methods: {
    moment,
    ...mapGetters(['nickname', 'userInfo']),
    changeSelectedGroups (value) {
      this.model.labels = value
    },
    add () {
      this.modelDefault.doctorId = this.userInfo().id
      this.modelDefault.doctorName = this.userInfo().realname
      this.edit(this.modelDefault)
    },
    edit (record) {
      this.model = Object.assign({}, record)
      this.visible = true
    },
    submitForm () {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
            this.model.doctorId = this.userInfo().id
          } else {
            httpurl += this.url.edit
            method = 'put'
            delete this.model.doctorId
          }
          if (this.model.labels && this.model.labels.length > 0) {
            let labelList = JSON.parse(JSON.stringify(this.model.labels))
            this.model.labels = []
            labelList.forEach(it => {
              this.model.labels.push({
                labelId: it.labelId || it
              })
            })
          }

          //时间格式化
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    },
    loadGroupList () {
      const that = this
      httpAction(that.url.queryGroup, {}, 'get').then((res) => {
        if (res.success) {
          that.groups = res.result
        } else {
          that.$message.warning('分组获取失败')
        }
      })
    },
    handleConfirmBlur (e) {
      const value = e.target.value
      this.confirmDirty = this.confirmDirty || !!value
    },
    validateToNextPassword (rule, value, callback) {
      const confirmpassword = this.model.confirmpassword
      if (value && confirmpassword && value !== confirmpassword) {
        callback('两次输入的密码不一样！')
      }
      if (value && this.confirmDirty) {
        this.$refs.form.validateField(['confirmpassword'])
      }
      callback()
    },
    compareToFirstPassword (rule, value, callback) {
      if (value && value !== this.model.password) {
        callback('两次输入的密码不一样！')
      } else {
        callback()
      }
    }
  }
}
</script>