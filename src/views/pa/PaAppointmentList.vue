<template>
  <div class="appointment-list">
    <a-tabs type="card" v-model="chooseTab" @change="handleChange">
      <a-tab-pane :key="1" tab="预约总览">
        <PaAppointmentOverview ref="pane1" @setTab="setTab"></PaAppointmentOverview>
      </a-tab-pane>

      <a-tab-pane :key="2" tab="预约列表">
        <PaAppointmentList ref="pane2"></PaAppointmentList>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { getAction } from '@/api/manage'
import moment from 'moment'
import PaAppointmentList from './modules/PaAppointmentList.vue'
import PaAppointmentOverview from './modules/PaAppointmentOverview.vue'

export default {
  components: {
    PaAppointmentList,
    PaAppointmentOverview
  },

  data() {
    return {
      chooseTab: 1,
    }
  },

  methods: {
    handleChange() {
      if (this.chooseTab === 1) {        
        this.$nextTick(() => {
          this.$refs.pane1.loadData()
        })
      }
      if (this.chooseTab === 2) {        
        this.$nextTick(() => {
          this.$refs.pane2.queryParam.id = ''
          this.$refs.pane2.loadData()
        })
      }
    },

    setTab(item) {
      this.chooseTab = 2
      this.$nextTick(() => {
        this.$refs.pane2.loadData(1, item.id)
      })
    }
  }
}
</script>

<style lang="less">
.appointment-list {
  display: flex;
  width: 100%;
  min-height: 600px;
  flex-direction: column;
  background-color: #ffffff;
  margin-top: 8px;
  padding: 0 15px;
}
</style>