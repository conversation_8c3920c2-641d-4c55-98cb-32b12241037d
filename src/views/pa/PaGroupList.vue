<template>
  <a-row :gutter="10">
    <a-col :md="leftColMd" :sm="24" style="margin-bottom: 20px">
      <a-card :bordered="false">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :md="6" :sm="8">
                <a-form-item label="标签名称">
                  <a-input placeholder="请输入标签名称" v-model="queryParam.name"></a-input>
                  <!-- <j-input placeholder="请输入标签名称" v-model="queryParam.name"></j-input> -->
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="8">
                <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                  <!--              <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"-->
                  <!--                             style="margin-left: 8px"></j-super-query>-->
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->

        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd()" type="primary" icon="plus">新增</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel">
                <a-icon type="delete" />
                删除
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <!-- <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{selectedRowKeys.length}}</a>项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
          </div> -->

          <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange">

            <span slot="action" slot-scope="text, record">
              <a @click="handleOpen(record)">学生</a>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
                <a-menu slot="overlay">
                  <a-menu-item v-if="record.step < 3">
                    <a @click="handleAdd(record)">新建</a>
                  </a-menu-item>
                  <a-menu-item :disabled="!!record.patientCount" @click="handleEdit(record)">
                    编辑
                  </a-menu-item>
                  <a-menu-item :disabled="!!record.patientCount">
                    <template v-if="record.patientCount">删除</template>
                    <a-popconfirm v-else title="确定删除吗?" @confirm="handleDelete(record)">
                      删除
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </div>
        <pa-group-modal ref="modalForm" :step="step" @ok="modalFormOk"></pa-group-modal>
      </a-card>
    </a-col>
    <a-col :md="rightColMd" :sm="24" v-if="this.rightcolval == 1">
      <a-card :bordered="false">
        <div style="text-align: right;">
          <a-icon type="close-circle" @click="hideUserList" />
        </div>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="24">
              <a-col :md="6" :sm="6">
                <a-form-item label="真实姓名">
                  <a-input placeholder="" v-model="queryParam2.name"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="6">
                <a-form-item label="昵称">
                  <a-input placeholder="" v-model="queryParam2.nickname"></a-input>
                </a-form-item>
              </a-col>
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-col :md="9" :sm="24">
                  <a-button type="primary" @click="searchQuery2" icon="search" style="margin-left: 21px">查询</a-button>
                  <a-button type="primary" @click="searchReset2" icon="reload" style="margin-left: 8px">重置</a-button>
                </a-col>
              </span>
            </a-row>
          </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <!-- <div class="table-operator" :md="24" :sm="24">
          <a-button @click="handleAddUserRole" type="primary" icon="plus" style="margin-top: 16px">添加学生</a-button>
        </div> -->
        <!-- table区域-begin -->
        <div>
          <a-table style="height:500px" ref="table2" bordered size="middle" rowKey="id" :columns="columns2" :dataSource="dataSource2" :pagination="ipagination2" :loading="loading2" @change="handleTableChange2">
            <!-- <span slot="action" slot-scope="text, record">
               <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete2(record.id)">
                    <a>删除</a>
               </a-popconfirm>
              </span> -->
            <template slot="imgSlot" slot-scope="text">
              <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
              <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;" />
            </template>
          </a-table>
        </div>
        <!-- 表单区域 -->
        <SelectPatientModal ref="SelectPatientModal" @selectFinished="selectOK"></SelectPatientModal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PaGroupModal from './modules/PaGroupModal'
import { filterObj } from '@/utils/util'
import SelectPatientModal from '../../components/jeecgbiz/modal/SelectPatientModal'
import { deleteAction, getAction, putAction } from '@/api/manage'

export default {
  name: 'PaGroupList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    PaGroupModal,
    SelectPatientModal
  },
  data () {
    return {
      description: '学生标签管理页面',
      // 表头
      columns: [
        {
          title: '名称',
          align: 'left',
          dataIndex: 'name',
          width: 300,
        },
        {
          title: '学生人数',
          align: 'center',
          dataIndex: 'patientCount'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      currentGroupId: '',
      url: {
        list: '/label/tree',
        delete: '/label/delete',
        deleteBatch: '/pa/paGroup/deleteBatch',
        exportXlsUrl: '/pa/paGroup/exportXls',
        importExcelUrl: 'pa/paGroup/importExcel',
        list2: '/pa/paPatient/listByLabel/',
        delete2: '/pa/paPatient/clearGroup',
        addGroup: '/pa/paPatient/addGroup'
      },
      dictOptions: {},
      superFieldList: [],
      rightcolval: 0,
      selectedRowKeys1: [],
      queryParam2: {},
      ipagination2: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      // 高级查询参数
      superQueryParams2: '',
      // 高级查询拼接条件
      superQueryMatchType2: 'and',
      isorter2: {
        column: 'createTime',
        order: 'desc'
      },
      filters2: {},
      loading2: false,
      dataSource2: [],
      columns2: [
        {
          title: '老师名称',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '真实姓名',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '昵称',
          align: 'center',
          dataIndex: 'nickname'
        },
        {
          title: '编号',
          align: 'center',
          dataIndex: 'number'
        },
        {
          title: '用户头像',
          align: 'center',
          dataIndex: 'avatarUrl',
          scopedSlots: { customRender: 'imgSlot' }
        }
      ],

      step: 1,
    }
  },
  created () {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    leftColMd () {
      return this.selectedRowKeys1.length === 0 ? 24 : 12
    },
    rightColMd () {
      return this.selectedRowKeys1.length === 0 ? 0 : 12
    }
  },
  methods: {
    searchQuery () {
      this.loadData(1);
    },
    loadData (arg) {
      if (!this.url.list) {
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result;
          if (res.result.total) {
            this.ipagination.total = res.result.total;
          } else {
            this.ipagination.total = 0;
          }
          this.dataSource.forEach(it1 => {
            it1.step = 1
            if (it1.children) {
              it1.children.forEach(it2 => {
                it2.step = 2
                if (it2.children) {
                  it2.children.forEach(it3 => {
                    it3.step = 3
                  })
                }
              })
            }
          })
          //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },
    handleAdd (scope) {
      this.step = scope ? scope.step + 1 : 1
      if (scope) {
        this.$refs.modalForm.add(scope);
      } else {
        this.$refs.modalForm.add();
      }
      this.$refs.modalForm.title = this.step === 1 ? '新增一级标签' : this.step === 2 ? '新增二级标签' : '新增三级标签'
      this.$refs.modalForm.disableSubmit = false;
    },
    handleEdit (scope) {
      this.step = scope.step
      this.$refs.modalForm.edit(scope);
      this.$refs.modalForm.title = this.step === 1 ? '编辑一级标签' : this.step === 2 ? '编辑二级标签' : '编辑三级标签'
      this.$refs.modalForm.disableSubmit = false;
    },
    handleDelete: function (record) {
      if (record.patientCount) return
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete + '/' + record.id).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    initDictConfig () {
    },
    getSuperFieldList () {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'name', text: '标签名称' })
      fieldList.push({ type: 'int', value: 'patientCount', text: '学生人数' })
      this.superFieldList = fieldList
    },
    handleOpen (record) {
      this.rightcolval = 1
      this.selectedRowKeys1 = [record.id]
      this.currentGroupId = record.id
      this.loadData2()
    },
    hideUserList () {
      this.selectedRowKeys1 = []
    },
    loadData2 (arg) {
      if (!this.url.list2) {
        this.$message.error('请设置url.list2属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination2.current = 1
      }
      if (this.currentGroupId === '') return
      let params = this.getQueryParams2()//查询条件
      // params.groupId = this.currentGroupId
      this.loading2 = true
      getAction(this.url.list2 + this.currentGroupId, params).then((res) => {
        if (res.success) {
          this.dataSource2 = res.result.records
          this.ipagination2.total = res.result.total
        }
        this.loading2 = false
      })
    },
    getQueryParams2 () {
      //获取查询条件
      let sqp = {}
      if (this.superQueryParams2) {
        sqp['superQueryParams'] = encodeURI(this.superQueryParams2)
        sqp['superQueryMatchType'] = this.superQueryMatchType2
      }
      var param = Object.assign(sqp, this.queryParam2, this.isorter2, this.filters2)
      param.field = this.getQueryField2()
      param.pageNo = this.ipagination2.current
      param.pageSize = this.ipagination2.pageSize
      return filterObj(param)
    },
    getQueryField2 () {
      var str = 'id,'
      this.columns2.forEach(function (value) {
        str += ',' + value.dataIndex
      })
      return str
    },
    searchQuery2 () {
      this.loadData2(1)
    },
    searchReset2 () {
      this.queryParam2 = {}
      this.loadData2(1)
    },
    handleAddUserRole () {
      if (this.currentGroupId == '') {
        this.$message.error('请选择一个角色!')
      } else {
        this.$refs.SelectPatientModal.visible = true
      }
    },
    handleTableChange2 (pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter2.column = sorter.field
        this.isorter2.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination2 = pagination
      this.loadData2()
    },
    handleDelete2: function (id) {
      if (!this.url.delete2) {
        this.$message.error('请设置url.delete2属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete2, { id: id }).then((res) => {
        if (res.success) {
          that.$message.success(res.message)
          that.loadData2()
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    selectOK (data) {
      let params = {}
      params.groupId = this.currentGroupId
      params.patientIdList = data.map(row => row['id'])
      putAction(this.url.addGroup, params).then((res) => {
        if (res.success) {
          this.loadData2()
          this.$message.success(res.message)
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>