<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">

          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学生名称">
              <a-input placeholder="请输入学生名称" v-model="queryParam.patientName"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="学生联系方式">
                <a-input placeholder="请输入学生联系方式" v-model="queryParam.patientPhone"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="家长名称">
                <a-input placeholder="请输入家长名称" v-model="queryParam.parentName"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="家长联系方式">
                <a-input placeholder="请输入家长联系方式" v-model="queryParam.parentPhone"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('回访记录')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange">

        <span slot="type" slot-scope="text">
          {{text=="1" ? "初访":"回访"}}
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <paReturnVisit-modal ref="modalForm" @ok="modalFormOk"></paReturnVisit-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import PaReturnVisitModal from './modules/PaReturnVisitModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: "PaReturnVisitList",
  mixins: [JeecgListMixin],
  components: {
    PaReturnVisitModal
  },
  data () {
    return {
      description: '回访记录管理页面',
      // 表头
      columns: [
        {
          title: '学生姓名',
          align: "center",
          dataIndex: 'patientName'
        },
        // {
        //   title: '学生联系方式',
        //   align: "center",
        //   dataIndex: 'patientPhone'
        // },
        {
          title: '家长名称',
          align: "center",
          dataIndex: 'parentName'
        },
        // {
        //   title: '家长联系方式',
        //   align: "center",
        //   dataIndex: 'parentPhone'
        // },
        {
          title: '家长与学生之间关系',
          align: "center",
          dataIndex: 'relation'
        },
        {
          title: '回访开始日期',
          align: "center",
          dataIndex: 'startTime'
        },
        {
          title: '回访结束日期',
          align: "center",
          dataIndex: 'endTime'
        },
        {
          title: '回访类型',
          align: "center",
          dataIndex: 'type',
          scopedSlots: { customRender: 'type' },
        },
        {
          title: '回访标题',
          align: "center",
          dataIndex: 'title'
        },
        {
          title: '回访原因',
          align: "center",
          dataIndex: 'reasion'
        },
        {
          title: '回访备注与建议',
          align: "center",
          dataIndex: 'note'
        },
        {
          title: '老师名称',
          align: "center",
          dataIndex: 'doctorName'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' },
        }
      ],
      url: {
        list: "/pa/paReturnVisit/list",
        delete: "/pa/paReturnVisit/delete",
        deleteBatch: "/pa/paReturnVisit/deleteBatch",
        exportXlsUrl: "pa/paReturnVisit/exportXls",
        importExcelUrl: "pa/paReturnVisit/importExcel",
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  methods: {

  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>