<template>
  <div class="page-header-index-wide" style="user-select:none;">
    <div>
      <a-row :gutter="24" type="flex" :style="{ marginTop: '24px' }">
        <a-col :xl="8" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :loading="loading" :bordered="false">
            <div class="topTitle">
              <div class="left">
                <span></span>
                <div class="name">
                  测评任务
                </div>
              </div>
              <div class="right" @click="getRouter('/ps/PsEvaluationList')">
                更多 >
              </div>
            </div>
            <div class="progress">
              <div class="progressDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <a-progress :percent="30" />
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
              <div class="progressDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <a-progress :percent="30" />
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
              <div class="progressDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <a-progress :percent="30" />
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :xl="16" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :loading="loading" :bordered="false">
            <div class="topTitle">
              <div class="left">
                <span></span>
                <div class="name">
                  经典测试
                </div>
              </div>
              <div class="right">
                更多 >
              </div>
            </div>
            <div class="ceshi">
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }" v-for="item, index of jingdianList" :key="index">
                <div class="ceshiInfo">
                  <img :src="imgUrl + item.picture" alt="">
                  <div class="ceshiRight">
                    <div class="rightTitle">
                      {{ item.name }}
                    </div>
                    <div class="rightInfo">
                      {{ item.description }}
                    </div>
                    <div class="rightCeshi" @click="startQuestions(item)">
                      去测试 >
                    </div>
                  </div>
                </div>
              </a-col>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <a-row :gutter="24" type="flex" :style="{ marginTop: '24px' }">
        <a-col :xl="16" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :loading="loading" :bordered="false">
            <div class="topTitle">
              <div class="left">
                <span></span>
                <div class="name">
                  专家推荐
                </div>
              </div>
              <div class="right">
                更多 >
              </div>
            </div>
            <div class="ceshi">
              <a-col :sm="24" :md="12" :xl="12" :style="{ marginBottom: '24px' }" v-for="item, index of zhuanjiaList" :key="index">
                  <div class="ceshiInfo">
                    <img :src="imgUrl + item.picture" alt="">
                    <div class="ceshiRight">
                      <div class="rightTitle">
                        {{ item.name }}
                      </div>
                      <div class="rightInfo">
                        {{ item.description }}
                      </div>
                      <div class="rightCeshi" @click="startQuestions(item)">
                        去测试 >
                      </div>
                    </div>
                  </div>
                </a-col>
            </div>
          </a-card>
        </a-col>
        <a-col :xl="8" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :loading="loading" :bordered="false">
            <div class="topTitle">
              <div class="left">
                <span></span>
                <div class="name">
                  我的报告
                </div>
              </div>
              <div class="right">
                更多 >
              </div>
            </div>
            <div class="progress">
              <div class="newsDetail" v-for="i in patientList" :key="i.id">
                <div class="newsLeft">
                  <div class="progressTitle">{{i.measureName}}</div>
                  <div class="annotation">
                    测试时间：{{ i.createTime }}
                  </div>
                </div>
                <div class="chakan" @click="handlePrintForm(i)">
                  立即查看
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
    <!-- 打印表单页 -->
    <report-common-form ref="ReportForm"></report-common-form>
  </div>
</template>

<script>
import { patient, psEvaluationUser, listMeasure } from '@/api/api'
import ReportCommonForm from '../../diagnosis/modules/ReportCommonForm'
import {getAction, httpAction} from '@api/manage'
import { postAction } from '../../../api/manage'
export default {
  name: 'EducationHome',
  components: {
      ReportCommonForm
    },
  data() {
    return {
      imgUrl: 'https://eduyun.zhisongkeji.com/zhisong-jiaoyu/',
      jingdianList: [],
      zhuanjiaList: [],
      patientList: [],
      url: {
        startQuestions: '/home/<USER>',
      },
    }
  },
  filters: {

  },
  created() {
    this.getPatient()
    this.getpsEvaluationUser()
    this.getfindPsCategoryAll()
  },
  methods: {
    // 路由跳转
    getRouter(path) {
      this.$router.push({ path: path });
    },
    // 我的报告
    getPatient() {
      patient({
        pageNo: 1,
        pageSize: 10,
        status: 2
      }).then((res) => {
        if (res.success) {
          this.patientList = res.result.records
        }
      });
    },
    // 测评任务
    getpsEvaluationUser() {
      psEvaluationUser({}).then((res) => {
        if (res.success) {
        } else {
        }
      });
    },
    // 量表管理 
    // 经典测试	categoryId: '89195262696e48cc811d8a105272a5c2'
    // 专家推荐	categoryId: '89195262696e48cc811d8a105272a5c3
    getfindPsCategoryAll() {
      listMeasure({
        categoryId: '89195262696e48cc811d8a105272a5c2'
      }).then((res) => {
        if (res.success) {
          let data = res.result
          if (data.length > 4) {
            this.jingdianList = data.slice(0,4)
          } else {
            this.jingdianList = data
          }
        } 
      });
      listMeasure({
        categoryId: '89195262696e48cc811d8a105272a5c3'
      }).then((res) => {
        if (res.success) {
          let data = res.result
          if (data.length > 4) {
            this.zhuanjiaList = data.slice(0,4)
          } else {
            this.zhuanjiaList = data
          }
        }
      });
    },
    handlePrintForm(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能预览！')
        return
      }
      this.$refs.ReportForm.edit(record)
      this.$refs.ReportForm.title = '内容预览'
    },
    startQuestions(record) {
      if (record.id) {
        let that = this
        let httpurl = that.url.startQuestions
        getAction(httpurl, { 'id': record.id }).then((res) => {
          if (res.success) {
            this.$notification['success']({
              message: '添加成功',
              duration: 3,
              description: '已在选择的终端推送答题信息'
            })
            that.confirmLoading = false
            that.close()
          } else {
            that.$message.error(res.message)
          }
        })
      } else {
        this.$message.warning('量表套餐中未包含任何量表!')
      }
    },
  }
}
</script>

<style lang="less" scoped>
.topTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .left {
    display: flex;
    align-items: center;

    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #0486FE;
      border-radius: 50%;
      margin-right: 10px;

    }

    .name {
      color: #0486FE;
      font-size: 18px;
    }
  }
}

.progressDetail {
  padding: 12px 30px;
  border-radius: 5px;
  margin-bottom: 15px;

  .progressTitle {
    font-size: 14px;
    color: #333333;
  }

  .annotation {
    margin-top: 15px;
    color: #999999;
    font-size: 12px;
  }
}

.progressDetail:first-child {
  background: rgba(187, 177, 255, 0.07);
}

.progressDetail:nth-child(2) {
  background: rgba(231, 246, 255, 0.45);
}

.progressDetail:last-child {
  background: rgba(255, 245, 221, 0.33);
}

.newsDetail {
  border-bottom: 1px dashed #EBEBEB;
  padding-bottom: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .chakan {
    width: 82px;
    height: 32px;
    background: #0486FE;
    border-radius: 16px;
    text-align: center;
    line-height: 32px;
    color: #fff;
    cursor: pointer;
    font-size: 12px;
  }

  .progressTitle {
    font-size: 14px;
    color: #333333;
  }

  .annotation {
    margin-top: 15px;
    color: #999999;
    font-size: 12px;
  }
}

.newsDetail:last-child {
  border: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.ceshiInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  cursor: pointer;
  height: 146px;

  img {
    width: 124px;
    height: 124px;
    margin-right: 10px;
  }

  .ceshiRight {
    flex: 1;
    width: 65%;

    .rightTitle {
      font-size: 14px;
      color: #333;
      font-weight: bold;
    }

    .rightInfo {
      width: 100%;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;

      margin: 5px 0;
      font-size: 12px;
      color: #666666;
    }

    .rightCeshi {
      color: #0486FE;
      font-size: 14px;
    }
  }
}
</style>
