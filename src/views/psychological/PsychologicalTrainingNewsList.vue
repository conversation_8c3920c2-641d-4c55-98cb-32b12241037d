<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="标题">
              <j-input placeholder="请输入标题" v-model="queryParam.title"></j-input>
            </a-form-item>
          </a-col>
<!--          <a-col :xl="6" :lg="7" :md="8" :sm="24">-->
<!--            <a-form-item label="副标题">-->
<!--              <a-input placeholder="请输入副标题" v-model="queryParam.subTitle"></a-input>-->
<!--            </a-form-item>-->
<!--          </a-col>-->
<!--          <template v-if="toggleSearchStatus">-->
<!--            <a-col :xl="6" :lg="7" :md="8" :sm="24">-->
<!--              <a-form-item label="类别">-->
<!--                <a-select placeholder="请选择类别" v-model="queryParam.type">-->
<!--                  <a-select-option :value="1">文章</a-select-option>-->
<!--                  <a-select-option :value="2">视频</a-select-option>-->
<!--                </a-select>-->
<!--              </a-form-item>-->
<!--            </a-col>-->
<!--            <a-col :xl="6" :lg="7" :md="8" :sm="24">-->
<!--              <a-form-item label="素材分类名称">-->
<!--                <a-input placeholder="请输入素材分类名称" v-model="queryParam.materialCategoryName"></a-input>-->
<!--              </a-form-item>-->
<!--            </a-col>-->
<!--          </template>-->
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
<!--      <a-button type="primary" icon="download" @click="handleExportXls('心理培训-学院资讯')">导出</a-button>-->
<!--      <a-upload-->
<!--        name="file"-->
<!--        :showUploadList="false"-->
<!--        :multiple="false"-->
<!--        :headers="tokenHeader"-->
<!--        :action="importExcelUrl"-->
<!--        @change="handleImportExcel"-->
<!--      >-->
<!--        <a-button type="primary" icon="import">导入</a-button>-->
<!--      </a-upload>-->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="type" slot-scope="text, record">
          {{ record.type == 1 ? '文章' : '视频' }}
        </template>

        <template slot="status" slot-scope="text, record">
          {{ record.status == 1 ? '启用' : '禁用' }}
        </template>

        <template slot="content" slot-scope="text, record">
          <div class="content-cell">
            {{ record.content }}
          </div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <psychologicalTrainingNews-modal ref="modalForm" @ok="modalFormOk"></psychologicalTrainingNews-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import PsychologicalTrainingNewsModal from './modules/PsychologicalTrainingNewsModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'PsychologicalTrainingNewsList',
  mixins: [JeecgListMixin],
  components: {
    PsychologicalTrainingNewsModal,
  },
  data() {
    return {
      description: '学院资讯',
      // 表头
      columns: [
        {
          title: '标题',
          align: 'center',
          dataIndex: 'title',
          width: 200
        },
        // {
        //   title: '副标题',
        //   align: 'center',
        //   dataIndex: 'subTitle',
        // },
        // {
        //   title: '类别',
        //   align: 'center',
        //   dataIndex: 'type',
        //   scopedSlots: { customRender: 'type' },
        // },
        // {
        //   title: '素材分类名称',
        //   align: 'center',
        //   dataIndex: 'materialCategoryName',
        // },
        // {
        //   title: '描述',
        //   align: 'center',
        //   dataIndex: 'description',
        // },
        // {
        //   title: '视频地址',
        //   align: 'center',
        //   dataIndex: 'videoPath',
        // },
        {
          title: '内容',
          align: 'center',
          dataIndex: 'content',
          width: 600,
          scopedSlots: { customRender: 'content' }
        },
        {
          title: '创建人',
          align: 'center',
          dataIndex: 'createBy_dictText',
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime',
        },
        // {
        //   title: '状态',
        //   align: 'center',
        //   dataIndex: 'status',
        //   scopedSlots: { customRender: 'status' },
        // },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/psychological/psychologicalTrainingNews/list',
        delete: '/psychological/psychologicalTrainingNews/delete',
        deleteBatch: '/psychological/psychologicalTrainingNews/deleteBatch',
        exportXlsUrl: 'psychological/psychologicalTrainingNews/exportXls',
        importExcelUrl: 'psychological/psychologicalTrainingNews/importExcel',
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {},
}
</script>
<style scoped>
@import '~@assets/less/common.less';

.content-cell {
  width: 580px;
  word-wrap: break-word;
  word-break: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 16px;
  max-height: 32px; /* 2行的高度，line-height * 2 */
  text-align: left;
}
</style>