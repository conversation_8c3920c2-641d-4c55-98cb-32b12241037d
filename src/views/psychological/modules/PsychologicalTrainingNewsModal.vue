<template>
  <j-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">
      
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title" label="标题">
          <a-input placeholder="请输入标题" v-model="model.title" />
        </a-form-model-item>
        <a-form-model-item label="标题图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="titleImg">
          <j-image-upload  v-model="model.titleImg" :bizPath="bizPath"></j-image-upload>
        </a-form-model-item>
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subTitle" label="副标题">-->
<!--          <a-input placeholder="请输入副标题" v-model="model.subTitle" />-->
<!--        </a-form-model-item>-->
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type" label="类别">-->
<!--          <a-radio-group v-model="model.type">-->
<!--            <a-radio :value="1">文章</a-radio>-->
<!--            <a-radio :value="2">视频</a-radio>-->
<!--          </a-radio-group>-->
<!--        </a-form-model-item>-->
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="materialCategoryName" label="素材分类名称">-->
<!--          <a-input placeholder="请输入素材分类名称" v-model="model.materialCategoryName" />-->
<!--        </a-form-model-item>-->
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description" label="描述">-->
<!--          <a-input placeholder="请输入描述" v-model="model.description" />-->
<!--        </a-form-model-item>-->
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="videoPath" label="视频地址">-->
<!--          <a-input placeholder="请输入视频地址" v-model="model.videoPath" />-->
<!--        </a-form-model-item>-->
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content" label="内容(html)">
          <j-editor v-model="model.content" />
        </a-form-model-item>
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status" label="状态">-->
<!--          <a-radio-group v-model="model.status">-->
<!--            <a-radio :value="1">启用</a-radio>-->
<!--            <a-radio :value="2">禁用</a-radio>-->
<!--          </a-radio-group>-->
<!--        </a-form-model-item>-->
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import moment from "moment"

  export default {
    name: "PsychologicalTrainingNewsModal",
    data () {
      return {
        bizPath: 'psychologicalTraining',
        title:"操作",
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        validatorRules:{
        },
        url: {
          add: "/psychological/psychologicalTrainingNews/add",
          edit: "/psychological/psychologicalTrainingNews/edit",
        },
      }
    },
    created () {
    },
    methods: {
      add () {
        //初始化默认值
        this.edit({});
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      close () {
        this.$emit('close');
        this.visible = false;
        this.$refs.form.clearValidate();
      },
      handleOk () {
        const that = this;
        // 触发表单验证
         this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }else{
             return false;
          }
        })
      },
      handleCancel () {
        this.close()
      },


    }
  }
</script>

<style lang="less" scoped>

</style>