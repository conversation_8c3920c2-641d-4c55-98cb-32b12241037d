<template>
  <div class="page-header-index-wide" style="user-select:none;">
    <div>
      <a-row :gutter="24" type="flex" :style="{ marginTop: '24px' }">
        <a-col :xl="14" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :loading="loading" :bordered="false">
            <a-row :gutter="20">
              <div class="other">
                <div class="otherInfo">
                  <img src="@/assets/image/other.png" alt="">
                  <div class="otherTitle">
                    心理社区
                  </div>
                  <div class="othermess">
                    用户自由发声</br>
                    畅谈无阻的交流平台
                  </div>
                </div>
                <div class="line"></div>
                <div class="otherInfo">
                  <img src="@/assets/image/other1.png" alt="">
                  <div class="otherTitle">
                    心理社区
                  </div>
                  <div class="othermess">
                    用户自由发声</br>
                    畅谈无阻的交流平台
                  </div>
                </div>
                <div class="line"></div>
                <div class="otherInfo">
                  <img src="@/assets/image/other2.png" alt="">
                  <div class="otherTitle">
                    心理社区
                  </div>
                  <div class="othermess">
                    用户自由发声</br>
                    畅谈无阻的交流平台
                  </div>
                </div>
              </div>
            </a-row>
          </a-card>
        </a-col>
        <a-col :xl="10" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :loading="loading" :bordered="false">
            <div class="topTitle">
              <div class="left">
                <span></span>
                <div class="titleBg">
                  新闻
                </div>
                <div class="name">
                  中心
                </div>
              </div>
              <div class="right">
                更多 >
              </div>
            </div>
            <div class="progress">
              <div class="newsDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
              <div class="newsDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
              <div class="newsDetail">
                <div class="progressTitle">2023年第一次测评</div>
                <div class="annotation">
                  开始时间：2023年11月1日 结束时间：2023年12月1日
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>

export default {
  name: 'EducationHome',
  components: {},
  data() {
    return {
    }
  },
  filters: {

  },
  created() {

  },
  methods: {

  }
}
</script>

<style lang="less" scoped>
.page-header-index-wide {
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  padding: 30px;
}



.topTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .left {
    display: flex;
    align-items: center;

    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #0486FE;
      border-radius: 50%;
    }

    .titleBg {
      width: 49px;
      height: 25px;
      background-size: 100% 100%;
      text-align: center;
      line-height: 25px;
      color: #fff;
      font-size: 18px;
      margin-left: 5px;
      margin-right: 5px;
    }

    .name {
      color: #0486FE;
      font-size: 18px;
    }
  }
}

.progressDetail {
  padding: 12px 30px;
  border-radius: 5px;
  margin-bottom: 15px;

  .progressTitle {
    font-size: 14px;
    color: #333333;
  }

  .annotation {
    margin-top: 15px;
    color: #999999;
    font-size: 12px;
  }
}

.progressDetail:first-child {
  background: rgba(187, 177, 255, 0.07);
}

.progressDetail:nth-child(2) {
  background: rgba(231, 246, 255, 0.45);
}

.progressDetail:last-child {
  background: rgba(255, 245, 221, 0.33);
}

.newsDetail {
  border-bottom: 1px dashed #EBEBEB;
  padding-bottom: 12px;
  margin-bottom: 12px;

  .progressTitle {
    font-size: 14px;
    color: #333333;
  }

  .annotation {
    margin-top: 15px;
    color: #999999;
    font-size: 12px;
  }
}

.newsDetail:last-child {
  border: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.other {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding:43px 30px;
  .line {
    width: 1px;
    height: 39px;
    border: 1px solid #CDD3DF;
  }
  .otherInfo{
    text-align: center;
    .otherTitle{
      color: #333333;
      font-size: 18px;
      font-weight: bold;
      margin: 20px 0;
    }
    .othermess{
      font-size: 14px;
      line-height: 24px;
      color: #999999;
    }
  }
}
</style>
