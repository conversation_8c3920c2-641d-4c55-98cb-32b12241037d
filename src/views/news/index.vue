<template>
  <div class="page" style="user-select:none;">
    <div class="bg">
      <img src="@/assets/image/news.png" alt="">
    </div>
    <div class="box">
      <div class="boxLeft">
        <!-- <div class="type">
          <div @click="getChange(index, 'lingyuIndex')" :class="index == lingyuIndex ? 'active' : ''"
            v-for="(i, index) in lingyuList" :key="index">
            {{ i }}
          </div>
        </div> -->
        <div class="list">
          <div class="listInfo" @click="getRouter">
            <img src="@/assets/img/img.png" alt="">
            <div class="right">
              <div class="title">
                心理学入行必看！和你聊聊心理学与心理工作
              </div>
              <div class="mess">
                最近，很多人私信“怎么学好心理学”“如何在工作中倡导心理健康”“有什么好的心理学图书或心理课程可以推荐吗？是疫情加重了人们的心理压力？还是近几年心理咨询师从业人员越来越多？
              </div>
              <div class="time">
                2023/08/23 14:00:00
              </div>
            </div>
          </div>
          <div class="listInfo" @click="getRouter">
            <img src="@/assets/img/img.png" alt="">
            <div class="right">
              <div class="title">
                心理学入行必看！和你聊聊心理学与心理工作
              </div>
              <div class="mess">
                最近，很多人私信“怎么学好心理学”“如何在工作中倡导心理健康”“有什么好的心理学图书或心理课程可以推荐吗？是疫情加重了人们的心理压力？还是近几年心理咨询师从业人员越来越多？
              </div>
              <div class="time">
                2023/08/23 14:00:00
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EducationHome',
  components: {
  },
  data() {
    return {
      lingyuIndex: '0',
      lingyuList: ['全部', '家庭', '社交', '自我', '成长'],
      zixunIndex: '0',
      zixunList: ['全部', '视频咨询', '语音咨询', '面对面咨询'],
      list: [{
        url: require('@/assets/image/zixun.png'),
        mess: '我需要心理咨询吗？'
      }, {
        url: require('@/assets/image/zixun1.png'),
        mess: '如何选择咨询师？'
      }, {
        url: require('@/assets/image/zixun2.png'),
        mess: '第一次咨询会发生什么？'
      }]
    }
  },
  filters: {

  },
  created() {

  },
  methods: {
    getChange(index, type) {
      this[type] = index
    },
    // 路由跳转
    getRouter() {
      this.$router.push({ path: '/acti/detail' });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  display: flex;
  align-items: flex-start;

  .top {
    padding: 21px 24px;
    background: #FFFFFF;
    border-radius: 16px;
  }

  .bottom {
    margin-top: 20px;
    padding: 21px 24px;
    background: #FFFFFF;
    border-radius: 16px;

    .bottomInfo {
      border-top: 1px dashed #D5D5D5;
      margin-top: 20px;

      div {
        display: flex;
        align-items: center;
        border-radius: 2px;
        font-size: 14px;
        color: #333333;
        height: 48px;
        margin-top: 20px;

        img {
          margin-left: 50px;
          margin-right: 10px;
        }
      }

      div:nth-child(1) {
        background: rgba(125, 171, 250, 0.09);
      }

      div:nth-child(2) {
        background: rgba(250, 139, 125, 0.09);
      }

      div:nth-child(3) {
        background: rgba(255, 191, 95, 0.09);
      }
    }
  }

  .list {
    margin-top: 28px;

    .listInfo {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .infoButton {
        display: flex;
        align-items: center;

        div {
          width: 62px;
          height: 28px;
          border-radius: 14px;
          border: 1px solid #148EFE;
          text-align: center;
          line-height: 26px;
          font-size: 14px;
          color: #188FFE;
          margin-left: 20px;
          cursor: pointer;
        }
      }

      .infoCenter {
        flex: 1;
        margin-left: 20px;

        .name {
          font-size: 16px;
          color: #333333;
          margin-bottom: 15px;
        }

        .tag {
          display: flex;
          align-items: center;
          margin-bottom: 15px;

          div {
            padding: 6px 10px;
            background: rgba(176, 176, 176, 0.14);
            border-radius: 2px;
            font-size: 14px;
            color: #666666;
            margin-right: 20px;
          }
        }

        .introduce {
          font-size: 14px;
          color: #666666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

        }
      }
    }
  }

  .tab {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .tabLeft {
      font-size: 16px;
      color: #333333;
    }
  }

  .leftTop {
    border-bottom: 1px solid #EBEBEB
  }

  .leftBottom {
    margin-top: 20px;


  }

  .tabRight {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: 10px;

    div {
      margin-right: 30px;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
    }

    .active {
      color: #0486FE;
    }
  }

  .boxLeft {
    flex: 1;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 30px 26px;
  }

  .boxRight {
    width: 320px;
    border-radius: 16px;
    margin-left: 20px;
  }
}

.titile {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .leftTitle {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333333;

    span {
      display: inline-block;
      width: 4px;
      height: 20px;
      background: #0486FE;
      margin-right: 13px;
    }
  }
}

.historyInfo {
  border-top: 1px dashed #D5D5D5;
  margin-top: 20px;
  padding-top: 20px;

  .button {
    margin: 20px auto;
    width: 100px;
    height: 28px;
    border-radius: 14px;
    border: 1px solid #D5D5D5;
    text-align: center;
    line-height: 26px;
    font-size: 14px;
    color: #999999;
    cursor: pointer;
  }

  .xinde {
    border: 1px solid #35A2FF;
    color: #35A2FF;
  }

  .name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
  }

  .right {
    font-size: 14px;
    display: flex;
    align-items: center;
  }

  .queren {
    color: #35A51B;

    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #35A51B;
      border-radius: 50%;
      margin-right: 5px;
    }
  }

  .wancheng {
    color: #999999;

    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #999999;
      border-radius: 50%;
      margin-right: 5px;
    }
  }
}

.bg {
  img {
    width: 100%;
    margin-bottom: 20px;
  }
}

.type {
  display: flex;
  align-items: center;
  justify-content: center;

  >div {
    font-size: 16px;
    color: #666666;
    margin-right: 150px;
    cursor: pointer;
  }

  .active {
    color: #35A2FF;
    position: relative;
  }

  .active::after {
    content: "";
    position: absolute;
    width: 18px;
    height: 3px;
    background: #35A2FF;
    left: 0px;
    right: 0px;
    bottom: 0px;
    top: 30px;
    margin: auto;
    border-radius: 1px;
  }
}

.listInfo {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #D9D9D9;
  padding-bottom: 20px;
  margin-bottom: 20px;
  cursor: pointer;
  img {
    width: 125px;
    height: 125px;
    margin-right: 20px;
  }

  .title {
    font-size: 16px;
    color: #333333;
    font-weight: 600;
  }
  .mess{
    font-size: 14px;
    color: #666666;
    line-height: 29px;
    margin: 10px 0;
  }
  .time{
    font-size: 14px;
    color: #999999;
  }
}
.listInfo:last-child{
  border-bottom: none;
}
</style>
