<template>
  <a-modal
    title="新增"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="选择租户">
          <a-select
            mode="multiple"
            placeholder="请选择租户"
            :value="nameList"
            style="width: calc(100% - 178px);">
          </a-select>
          <span style="display: inline-block;width:170px;float: right;overflow: hidden;">
          <a-button type="primary" @click="handleSelect" icon="search" style="width: 81px">选择</a-button>
          <a-button type="primary" @click="selectReset" icon="reload" style="margin-left: 8px;width: 81px">清空</a-button>
        </span>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="选择模板">
          <a-select
            style="width: 100%"
            placeholder="请选择量表模板"
            v-model="templateId">
            <a-select-option v-for="template in psTemplates" :key="template.id">
              {{ template.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <!-- 选择多个用户支持排序 -->
        <j-select-multi-user-modal ref="selectModal" @selectFinished="selectOK"/>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JSelectMultiUserModal from '@/components/jeecgbiz/modal/JSelectMultiUserModal'
  import { findPsTemplateAll } from '@/api/api'

  export default {
    name: 'PsCategoryModal',
    components: { JSelectMultiUserModal },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        selectList: [],
        tenantIds: '',
        tenantNames: '',
        psTemplates: [],
        templateId:'',
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          name: { rules: [{ required: true, message: '请输入量表类别名称!' }] }
        },
        url: {
          add: '/psychology/psMeasureTenant/add',
          edit: '/psychology/psMeasureTenant/edit'
        }
      }
    },
    computed: {
      nameList: function() {
        var names = []
        var ids = []
        for (var a = 0; a < this.selectList.length; a++) {
          names.push(this.selectList[a].name)
          ids.push(this.selectList[a].id)
        }
        let nameStr = ''
        let idStr = ''
        if (names.length > 0) {
          nameStr = names.join(',')
          idStr = ids.join(',')
        }
        this.$emit('change', nameStr)
        this.tenantIds = idStr;
        this.tenantNames = nameStr;
        return names
      }
    },
    created() {
      this.loadPsTemplate();
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model))
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            formData.templateId = this.templateId;
            formData.tenantIds = this.tenantIds;
            formData.tenantNames = this.tenantNames;
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },
      handleSelect: function() {
        this.$refs.selectModal.add()
      },
      selectReset() {
        this.selectList = []
      },
      selectOK: function(data) {
        this.selectList = data
      },
      /**
       * 初始化模板下拉选
       */
      loadPsTemplate() {
        findPsTemplateAll({}).then((res) => {
          if (res.success) {
            this.psTemplates = res.result
          } else {
            console.log(res.message)
          }
        })
      },
    }
  }
</script>

<style scoped>

</style>