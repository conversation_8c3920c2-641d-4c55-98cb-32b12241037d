<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <!-- 主表单区域 -->
      <a-form :form="form">
        <!-- 子表单区域 -->
        <a-tabs>
          <a-tab-pane tab="量表设置">
            <div>
              <a-row type="flex" style="margin-bottom:10px" :gutter="16">
                <a-col :span="20">量表</a-col>
                <a-col :span="4">操作</a-col>
              </a-row>
              <a-row type="flex" style="margin-bottom:10px" :gutter="16"
                     v-for="(item, index) in model.psMeasureUnitPriceTemplateList" :key="index">
                <a-col :span="20">
                  <a-form-item>
                    <a-select
                      show-search
                      placeholder="请选择量表"
                      option-filter-prop="children"
                      style="width: 100%"
                      :filter-option="filterOption"
                      v-decorator="['psMeasureUnitPriceTemplateList['+index+'].measureId', {'initialValue':item.measureId, rules: [{ required: true, message: '请选择量表!' }]}]"
                    >
                      <a-select-option v-for="item in measureTree" :value="item.id" :key="item.id">{{ item.name }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="4">
                  <a-form-item>
                    <a-button @click="delRowCustom(index)" icon="minus"></a-button>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-button style="width: 98%; margin-bottom: 8px" type="dashed" icon="plus" @click="addRowCustom">新增
              </a-button>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>

  import pick from 'lodash.pick'
  import { getAction, httpAction } from '@/api/manage'

  export default {
    name: 'PsUserMeasurePriceModal',
    data() {
      return {
        title: '量表单价设置',
        visible: false,
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        validatorRules: {},
        model: {
          psMeasureUnitPriceTemplateList: [{}]
        },
        form: this.$form.createForm(this),
        confirmLoading: false,
        measureTree: [],
        url: {
          add: '/psychology/psMeasureTenant/add',
          edit: '/psychology/psMeasureTenant/edit',
          treeList: '/psychology/psMeasure/listAll',
          psMeasureUnitPrice: {
            list: '/psychology/psMeasureTenant/listAllByTenantId'
          }
        }
      }
    },
    created() {
      this.loadTreeData()
    },
    methods: {
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
      add() {
        this.edit({})
      },
      loadTreeData() {
        getAction(this.url.treeList, null).then((res) => {
          if (res.success) {
            this.measureTree = res.result
            this.loading = false
          }
        })
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.model.psMeasureUnitPriceTemplateList = [{}]
        if (this.model.tenantId) {
          let params = { tenantId: this.model.tenantId }
          getAction(this.url.psMeasureUnitPrice.list, params).then((res) => {
            if (res.success) {
              this.model.psMeasureUnitPriceTemplateList = res.result
              this.$forceUpdate()
            }
          })
        }
        this.visible = true
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let mainData = Object.assign(this.model, values)
            let formData = {
              tenantId: mainData.tenantId,
              measureIdList: mainData.psMeasureUnitPriceTemplateList.map(item => item.measureId)
            }
            httpAction(this.url.edit, formData, 'put').then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      addRowCustom() {
        this.model.psMeasureUnitPriceTemplateList.push({})
        this.$forceUpdate()
      },
      delRowCustom(index) {
        this.model.psMeasureUnitPriceTemplateList.splice(index, 1)
        this.$forceUpdate()
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>