<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">
    <!-- 主表单区域 -->
    <a-form :form="form">
      <a-row>
        <a-col :span="12" :gutter="8">
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="模板名称">
            <a-input placeholder="请输入模板名称" v-decorator="['name', {initialValue: model.name}]"/>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 子表单区域 -->
      <a-tabs>
        <a-tab-pane tab="量表模板设置">
          <div>
            <a-row type="flex" style="margin-bottom:10px" :gutter="16">
              <a-col :span="22">量表</a-col>
              <a-col :span="2">操作</a-col>
            </a-row>
            <a-row type="flex" style="margin-bottom:10px" :gutter="16"
                    v-for="(item, index) in model.psMeasureUnitPriceTemplateList" :key="index">
              <a-col :span="22">
                <a-form-item>
                  <a-select
                    show-search
                    placeholder="请选择量表"
                    option-filter-prop="children"
                    style="width: 100%"
                    :filter-option="filterOption"
                    v-decorator="['psMeasureUnitPriceTemplateList['+index+'].measureId', {'initialValue':item.measureId,rules: [{ required: true, message: '请选择量表!' }]}]"
                  >
                    <a-select-option v-for="item in measureTree" :value="item.id" :key="item.id">{{ item.name }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="2">
                <a-form-item>
                  <a-button @click="delRowCustom(index)" icon="minus"></a-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-button style="width: 98%; margin-bottom: 8px" type="dashed" icon="plus" @click="addRowCustom">新增
            </a-button>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>
</template>
<script>

  import pick from 'lodash.pick'
  import { getAction, httpAction } from '@/api/manage'

  export default {
    name: 'PsMeasureTemplateModal',
    data() {
      return {
        title: '量表单价设置',
        visible: false,
        validatorRules: {},
        confirmLoading: false,
        measureTree: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        model: {
          psMeasureUnitPriceTemplateList: [{}]
        },
        form: this.$form.createForm(this),
        url: {
          add: '/psychology/psMeasureTemplate/add',
          edit: '/psychology/psMeasureTemplate/edit',
          treeList: '/psychology/psMeasure/listAll',
          psMeasureUnitPriceTemplate: {
            list: '/psychology/psMeasureTemplate/queryPsMeasureUnitPriceTemplateByMainId'
          }
        }
      }
    },
    created() {
      this.loadTreeData()
    },
    methods: {
      filterOption(input, option) {
        return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        );
      },
      add() {
        this.edit({})
      },
      loadTreeData() {
        getAction(this.url.treeList, null).then((res) => {
          if (res.success) {
            this.measureTree = res.result
            this.loading = false
          }
        })
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.model.psMeasureUnitPriceTemplateList = [{}]
        if (this.model.id) {
          let params = { id: this.model.id }
          getAction(this.url.psMeasureUnitPriceTemplate.list, params).then((res) => {
            if (res.success) {
              this.model.psMeasureUnitPriceTemplateList = res.result
              this.$forceUpdate()
            }
          })
        }
        this.visible = true
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let mainData = Object.assign(this.model, values)
            let formData = {
              ...mainData,
              psMeasureUnitPriceTemplateList: mainData.psMeasureUnitPriceTemplateList
            }
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      addRowCustom() {
        this.model.psMeasureUnitPriceTemplateList.push({})
        this.$forceUpdate()
      },
      delRowCustom(index) {
        this.model.psMeasureUnitPriceTemplateList.splice(index, 1)
        this.$forceUpdate()
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>