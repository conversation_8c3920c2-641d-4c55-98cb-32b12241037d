<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-form-item label="租户名称">
              <a-input placeholder="请输入租户名称" v-model="queryParam.tenantName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i>
        <span>已选择</span>
        <a style="font-weight: 600">
          {{ selectedRowKeys.length }}
        </a>
        <span>项</span>
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="tenantId"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <template slot="avatarslot" slot-scope="text, record, index">
          <div class="anty-img-wrap">
            <a-avatar shape="square" :src="getAvatarView(record.avatar)" icon="user"/>
          </div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical"/>
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.tenantId)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 编辑 -->
    <measureSettingEdit ref="modalForm" @ok="modalFormOk"/>

    <!-- 表单区域 新增 -->
    <measureSettingAdd ref="adminModalForm" @ok="modalFormOk"/>

  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import measureSettingEdit from './measureSettingEdit'
  import measureSettingAdd from './measureSettingAdd'
  import { deleteAction } from '@/api/manage'


  export default {
    name: 'PsUserMeasurePriceList',
    mixins: [JeecgListMixin],
    components: {
      measureSettingEdit,
      measureSettingAdd
    },
    data() {
      return {
        description: '量表单价设置主表管理页面',
        // 表头
        columns: [
          {
            title: '租户名称',
            align: "center",
            dataIndex: 'tenantName',
          },
          {
            title: '量表模板',
            align: "center",
            dataIndex: 'templateName'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        // 请求参数
        url: {
          imgerver: window._CONFIG['staticDomainURL'],
          list: '/psychology/psMeasureTenant/list',
          delete: '/psychology/psMeasureTenant/delete',
        }
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      getAvatarView: function (avatar) {
        return this.url.imgerver + "/" + avatar;
      },
      initDictConfig() {
      },
      handleAdd: function () {
        this.$refs.adminModalForm.add();
        this.$refs.adminModalForm.title = "新增";
        this.$refs.adminModalForm.disableSubmit = false;
      },
      handleDelete: function (id) {
      if(!this.url.delete){
        this.$message.error("请设置url.delete属性!")
        return
      }
      var that = this;
      deleteAction(that.url.delete, {tenantId: id}).then((res) => {
        if (res.success) {
          //重新计算分页问题
          that.reCalculatePage(1)
          that.$message.success(res.message || res.msg);
          that.loadData();
        } else {
          that.$message.warning(res.message) || res.msg;
        }
      });
    },
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>