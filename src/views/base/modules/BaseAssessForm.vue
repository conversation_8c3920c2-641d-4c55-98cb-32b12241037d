<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
              <a-input v-model="model.title" placeholder="请输入标题"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="类别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="category">
              <a-input v-model="model.category" placeholder="请输入类别"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="标题图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="bannerGraphic">
              <j-image-upload isMultiple v-model="model.bannerGraphic"></j-image-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
              <a-form-model-item label="量表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="measureId">
                <a-select
                  style="width: 100%"
                  placeholder="请选择量表"
                  v-model="model.measureId">
                  <a-select-option v-for="measure in measures" :key="measure.id">
                    {{ measure.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <j-dict-select-tag v-model="model.status" placeholder="请选择状态" dictCode="enable_status"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description">
              <a-textarea :rows="5" v-model="model.description" placeholder="请输入描述" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction } from '@/api/manage'

  export default {
    name: 'BaseAssessForm',
    components: {},
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        measures: [],
        validatorRules: {
          title: [
            { required: true, message: '请输入标题!' }
          ],
          category: [
            { required: true, message: '请输入类别!' }
          ],
          bannerGraphic: [
            { required: true, message: '请输入标题图地址!' }
          ],
          measureId: [
            { required: true, message: '请选择量表!' }
          ]
        },
        url: {
          add: '/base/baseAssess/add',
          edit: '/base/baseAssess/edit',
          queryById: '/base/baseAssess/queryById',
          listAll: '/psychology/psMeasure/listAll'
        }
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
    },
    methods: {
      add() {
        this.edit(this.modelDefault)
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.loadPsTemplate()
        this.visible = true
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }
        })
      },
      /**
       * 初始化量表下拉选
       */
      loadPsTemplate() {
        httpAction(this.url.listAll, {}, 'get').then((res) => {
          if (res.success) {
            this.measures = res.result
          }
        })
      }
    }
  }
</script>