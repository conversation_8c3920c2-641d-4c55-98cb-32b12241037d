<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
              <a-input v-model="model.title" placeholder="请输入标题"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="标题图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="titleImg">
              <j-image-upload isMultiple v-model="model.titleImg"></j-image-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="类别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="category">
              <j-dict-select-tag v-model="model.categoryId" placeholder="请选择类别"
                                 dictCode="base_train_category,title,id"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="语音文件" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="voicePath">
              <j-upload v-model="model.voicePath" bizPath="videos"></j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <j-dict-select-tag v-model="model.status" placeholder="请选择状态" dictCode="enable_status"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description">
              <a-textarea :rows="5" v-model="model.description" placeholder="请输入描述" style="width: 100%"/>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction } from '@/api/manage'

  export default {
    name: 'BaseTrainForm',
    components: {},
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        model: {
          status:1
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        validatorRules: {
          title: [
            { required: true, message: '请输入标题!' }
          ],
          titleImg: [
            { required: true, message: '请上传标题图!' }
          ],
          categoryId: [
            { required: true, message: '请选择类别!' }
          ],
          voicePath: [
            { required: true, message: '请输入语音地址!' }
          ],
          status: [
            { required: true, message: '请输入状态!' }
          ]
        },
        url: {
          add: '/base/baseTrain/add',
          edit: '/base/baseTrain/edit',
          queryById: '/base/baseTrain/queryById'
        }
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
    },
    methods: {
      add() {
        this.edit(this.modelDefault)
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.visible = true
        let pathArr = this.model.voicePath.split("http")
        let voicePath = pathArr[pathArr.length - 1];
        this.model.voicePath = 'http' + voicePath
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                console.log(res)
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }
        })
      }
    }
  }
</script>