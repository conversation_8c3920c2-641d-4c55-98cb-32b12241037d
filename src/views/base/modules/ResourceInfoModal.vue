<template>
  <j-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title" label="标题">
          <a-input placeholder="请输入标题" v-model="model.title" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="abstracts" label="摘要">
          <a-input placeholder="请输入摘要" v-model="model.abstracts" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type" label="信息类型">
          <j-dict-select-tag v-model="model.type" placeholder="请选择信息类型"
                                 dictCode="info_type"/>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sort" label="排序">
          <a-input-number v-model="model.sort"/>
        </a-form-model-item>
        <a-form-model-item label="封面图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="photoPath">
          <j-image-upload isMultiple v-model="model.photoPath"></j-image-upload>
        </a-form-model-item>
        <a-form-model-item label="发布状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
          <j-dict-select-tag type="radio" v-model="model.status" placeholder="请输入是否发布" dictCode="yn"/>
        </a-form-model-item>
        <a-form-model-item label="是否推荐" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isRecommend">
          <j-dict-select-tag type="radio" v-model="model.isRecommend" placeholder="请输入是否推荐" dictCode="yn"/>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classification" label="分类">
          <j-dict-select-tag v-model="model.classification" placeholder="请选择分类" dictCode="article_classification"/>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sourceFrom" label="来源">
          <a-input placeholder="请输入来源" v-model="model.sourceFrom" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark" label="备注">
          <a-input placeholder="请输入备注" v-model="model.remark" />
        </a-form-model-item>
        <a-form-model-item label="文章内容" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content" style="min-height: 600px;">
          <j-editor :j-height="600" v-model="model.content" :disabled="formDisabled"/>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import moment from "moment"

  export default {
    name: "ResourceInfoModal",
    data () {
      return {
        title:"操作",
        visible: false,
        formDisabled: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        validatorRules:{
        },
        url: {
          add: "/resource/info/add",
          edit: "/resource/info/edit",
        },
      }
    },
    created () {
    },
    methods: {
      add () {
        //初始化默认值
        this.edit({});
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      close () {
        this.$emit('close');
        this.visible = false;
        this.$refs.form.clearValidate();
      },
      handleOk () {
        const that = this;
        // 触发表单验证
         this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }else{
             return false;
          }
        })
      },
      handleCancel () {
        this.close()
      },


    }
  }
</script>

<style lang="less" scoped>

</style>