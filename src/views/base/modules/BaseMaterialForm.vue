<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <j-dict-select-tag v-model="model.type" placeholder="请选择素材类型" dictCode="material_type" :disabled="formDisabled"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
              <a-input v-model="model.title" placeholder="请输入标题" :disabled="formDisabled"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="副标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subTitle">
              <a-input v-model="model.subTitle" placeholder="请输入副标题" :disabled="formDisabled"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="标题图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="titleImg">
              <j-image-upload isMultiple v-model="model.titleImg"></j-image-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="素材分类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="materialCategoryId">
              <j-dict-select-tag v-model="model.materialCategoryId" placeholder="请选择素材分类"
                                 dictCode="base_material_category,name,id" :disabled="formDisabled"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description">
              <a-input v-model="model.description" placeholder="请输入描述" :disabled="formDisabled"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col v-if="model.type == 2" :span="24">
            <a-form-model-item label="视频地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="videoPath">
              <a-textarea row="5" v-model="model.videoPath" placeholder="请输入视频地址" :disabled="formDisabled"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col v-if="model.type == 1" :span="24">
            <a-form-model-item label="文章内容" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content" style="min-height: 600px;">
              <j-editor :j-height="600" v-model="model.content" :disabled="formDisabled"/>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction } from '@/api/manage'

  export default {
    name: 'BaseMaterialForm',
    components: {},
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        validatorRules: {
          title: [{ required: true, message: '请输入标题!' }],
          type: [{ required: true, message: '请选择素材类型!' }],
          materialCategoryId: [{ required: true, message: '请选择素材分类!' }],
          description: [{ required: true, message: '请输入描述!' }],
          videoPath: [{ required: true, message: '请输入视频地址!' }]
        },
        url: {
          add: '/base/baseMaterial/add',
          edit: '/base/baseMaterial/edit',
          queryById: '/base/baseMaterial/queryById'
        }
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
    },
    methods: {
      add() {
        this.edit(this.modelDefault)
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.visible = true
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }
        })
      }
    }
  }
</script>