<template>
  <a-modal
    :title="title"
    :width="1400"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="标题">
          <a-input placeholder="请输入标题" v-decorator="['title', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="副标题">
          <a-input placeholder="请输入文章副标题" v-decorator="['subTitle', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="标题图（缩略图）">
          <j-image-upload class="avatar-uploader" bizPath="temp/image/articleTitleImg" text="上传" v-model="fileList"></j-image-upload>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="摘要(文章底部）">
          <a-textarea placeholder="请输入摘要(文章底部）" v-decorator="['description', {}]" :auto-size="{ minRows: 3, maxRows: 5 }"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="作者">
          <a-input placeholder="请输入作者" v-decorator="['author', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="文章内容">
          <j-editor :j-height="600" v-model="model.content"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="是否置顶">
          <j-dict-select-tag v-decorator="['isTop', {}]" :triggerChange="true"
                             placeholder="请选择是否置顶" dictCode="yn"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { getAction, httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JEditor from '@/components/jeecg/JEditor'
  import JImageUpload from '../../../components/jeecg/JImageUpload'

  export default {
    name: 'BaseArticleModal',
    components: {
      JImageUpload,
      JEditor
    },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        fileList:[],
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: '/base/article/add',
          edit: '/base/article/edit',
          queryById: '/base/article/queryById'
        }
      }
    },
    created() {
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'title', 'subTitle', 'titleImg', 'description', 'author', 'content',
            'type', 'isTop', 'status'))
          //时间格式化
        })
        if (this.model.id) {
          this.loadResultReportData(this.model.id)
          setTimeout(() => {
            this.fileList = record.titleImg;
          }, 5)
        }
      },
      loadResultReportData(id) {
        getAction(this.url.queryById, { id: id }).then((res) => {
          if (res.success) {
            this.model = res.result
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.fileList=[];
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            if(that.fileList != ''){
              formData.titleImg = that.fileList;
            }else{
              formData.titleImg = null;
            }
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })


          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style lang="less" scoped>
  .avatar-uploader > .ant-upload {
    width: 104px;
    height: 104px;
  }
</style>