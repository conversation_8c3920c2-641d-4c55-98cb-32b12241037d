<template>
  <j-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules">
      
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="bannerName" label="轮播图名称">
          <a-input placeholder="请输入轮播图名称" v-model="model.bannerName" />
        </a-form-model-item>
        <a-form-model-item label="标题图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="picturePath">
          <j-image-upload v-model="model.picturePath"></j-image-upload>
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="orderNum" label="排序字段">
          <a-input-number v-model="model.orderNum"/>
        </a-form-model-item>
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="showStatus" label="展示状态">-->
<!--          <a-input placeholder="请输入展示状态" v-model="model.showStatus" />-->
<!--        </a-form-model-item>-->
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="promptMessage" label="提示信息">
          <a-input placeholder="请输入提示信息" v-model="model.promptMessage" />
        </a-form-model-item>
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isJump" label="是否跳转">-->
<!--          <a-input placeholder="请输入是否跳转" v-model="model.isJump" />-->
<!--        </a-form-model-item>-->
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="jumpUrl" label="跳转链接">-->
<!--          <a-input placeholder="请输入跳转链接" v-model="model.jumpUrl" />-->
<!--        </a-form-model-item>-->
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="startTime" label="开始时间">-->
<!--          <a-date-picker showTime valueFormat='YYYY-MM-DD HH:mm:ss' v-model="model.startTime" />-->
<!--        </a-form-model-item>-->
<!--        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" prop="endTime" label="结束时间">-->
<!--          <a-date-picker showTime valueFormat='YYYY-MM-DD HH:mm:ss' v-model="model.endTime" />-->
<!--        </a-form-model-item>-->
      </a-form-model>
    </a-spin>
  </j-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import moment from "moment"

  export default {
    name: "ResourceBannerModal",
    data () {
      return {
        title:"操作",
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        validatorRules:{
        },
        url: {
          add: "/resource/banner/add",
          edit: "/resource/banner/edit",
        },
      }
    },
    created () {
    },
    methods: {
      add () {
        //初始化默认值
        this.edit({});
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      close () {
        this.$emit('close');
        this.visible = false;
        this.$refs.form.clearValidate();
      },
      handleOk () {
        const that = this;
        // 触发表单验证
         this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }else{
             return false;
          }
        })
      },
      handleCancel () {
        this.close()
      },


    }
  }
</script>

<style lang="less" scoped>

</style>