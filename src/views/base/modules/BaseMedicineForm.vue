<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="药品商品名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tradeName">
              <a-input v-model="model.tradeName" placeholder="请输入药品商品名"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="通用名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="commonName">
              <a-input v-model="model.commonName" placeholder="请输入药品通用名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="药品规格" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="specification">
              <a-input v-model="model.specification" placeholder="请输入药品规格"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="标准分类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="categoryId">
              <j-dict-select-tag v-model="model.categoryId" placeholder="请选择标准分类"
                                 dictCode="base_medicine_category,name,id"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="适应症" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="adaptation">
              <a-textarea :rows="5" v-model="model.adaptation" placeholder="请输入适应症"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="说明" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
              <a-textarea :rows="5" v-model="model.remark" placeholder="请输入说明"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction } from '@/api/manage'
  import ATextarea from 'ant-design-vue/es/input/TextArea'

  export default {
    name: 'BaseMedicineForm',
    components: { ATextarea },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        validatorRules: {
          tradeName: [{ required: true, message: '请输入药品商品名!' }],
          commonName: [{ required: true, message: '请输入药品通用名称!' }],
          specification: [{ required: true, message: '请输入药品规格!' }],
          categoryId: [{ required: true, message: '请选择标准分类!' }],
        },
        url: {
          add: '/base/baseMedicine/add',
          edit: '/base/baseMedicine/edit',
          queryById: '/base/baseMedicine/queryById'
        }
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
    },
    methods: {
      add() {
        this.edit(this.modelDefault)
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.visible = true
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }

        })
      }
    }
  }
</script>