<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="信息类型">
              <a-select v-model="queryParam.type" placeholder="请选择信息类型" allowClear style="width: 100%;">
                <a-select-option :value="1">科普</a-select-option>
                <a-select-option :value="2">新闻</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="分类">
              <j-dict-select-tag v-model="queryParam.classification" placeholder="请选择分类" dictCode="article_classification"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="标题">
              <a-input placeholder="请输入标题" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('信息发布')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
        @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
          selectedRowKeys.length
          }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource"
        :pagination="ipagination" :loading="loading" class="j-table-force-nowrap"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange">
        <template slot="type" slot-scope="text">
          <span>{{ text == '1' ? '科普' : '新闻' }}</span>
        </template>
        <template slot="status" slot-scope="text">
          <span>{{ text == '0' ? '未发布' : '已发布' }}</span>
        </template>
        <template slot="isRecommend" slot-scope="text">
          <span>{{ text == '0' ? '不推荐' : '推荐' }}</span>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt=""
               style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <resourceInfo-modal ref="modalForm" @ok="modalFormOk"></resourceInfo-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import ResourceInfoModal from './modules/ResourceInfoModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: "ResourceInfoList",
  mixins: [JeecgListMixin],
  components: {
    ResourceInfoModal
  },
  data() {
    return {
      description: '信息发布管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: "center",
          customRender: function (t, r, index) {
            return parseInt(index) + 1;
          }
        },
        {
          title: '信息类型',
          align: "center",
          dataIndex: 'type',
          scopedSlots: { customRender: 'type' }
        },
        {
          title: '摘要',
          align: "center",
          dataIndex: 'abstracts'
        },
        {
          title: '排序',
          align: "center",
          dataIndex: 'sort'
        },
        {
          title: '标题',
          align: "center",
          dataIndex: 'title'
        },
        {
          title: '封面图',
          align: 'center',
          dataIndex: 'photoPath',
          scopedSlots: { customRender: 'imgSlot' }
        },
        {
          title: '作者名称',
          align: "center",
          dataIndex: 'authorName'
        },
        {
          title: '状态',
          align: "center",
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '发布时间',
          align: "center",
          dataIndex: 'releaseTime'
        },
        {
          title: '分类',
          align: "center",
          dataIndex: 'classification_dictText'
        },
        {
          title: '是否推荐',
          align: "center",
          dataIndex: 'isRecommend',
          scopedSlots: { customRender: 'isRecommend' }
        },
        {
          title: '备注',
          align: "center",
          dataIndex: 'remark',
          ellipsis: true
        },
        {
          title: '已读数',
          align: "center",
          dataIndex: 'readCount'
        },
        {
          title: '来源',
          align: "center",
          dataIndex: 'sourceFrom'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          scopedSlots: { customRender: 'action' },
        }
      ],
      url: {
        list: "/resource/info/list",
        delete: "/resource/info/delete",
        deleteBatch: "/resource/info/deleteBatch",
        exportXlsUrl: "resource/info/exportXls",
        importExcelUrl: "resource/info/importExcel",
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  methods: {

  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>