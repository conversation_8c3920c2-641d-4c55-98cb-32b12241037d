<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="标题">
              <a-input placeholder="请输入标题" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :md="6" :sm="8">
              <a-form-item label="文章副标题">
                <a-input placeholder="请输入文章副标题" v-model="queryParam.subTitle"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="8">
              <a-form-item label="描述、摘要(文章底部）">
                <a-input placeholder="请输入描述、摘要(文章底部）" v-model="queryParam.description"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!--      <a-button type="primary" icon="download" @click="handleExportXls('科普文章表')">导出</a-button>
            <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
                      @change="handleImportExcel">
              <a-button type="primary" icon="import">导入</a-button>
            </a-upload>-->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
        <template slot="avatarslot" slot-scope="text, record, index">
          <div class="anty-img-wrap">
            <a-avatar shape="square" :src="getAvatarView(record.titleImg)" icon="user"/>
          </div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical"/>
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;" @click="hitShelf(record)">上架</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;" @click="outShelf(record)">下架</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <baseArticle-modal ref="modalForm" @ok="modalFormOk"></baseArticle-modal>
  </a-card>
</template>

<script>
  import BaseArticleModal from './modules/BaseArticleModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { getAction } from '@/api/manage'

  export default {
    name: 'BaseArticleList',
    mixins: [JeecgListMixin],
    components: {
      BaseArticleModal
    },
    data() {
      return {
        description: '科普文章表管理页面',
        // 表头
        columns: [
          {
            title: '标题',
            align: 'center',
            dataIndex: 'title'
          },
          {
            title: '标题图（缩略图）',
            align: 'center',
            dataIndex: 'titleImg',
            scopedSlots: { customRender: 'avatarslot' }
          },
          {
            title: '作者',
            align: 'center',
            dataIndex: 'author'
          },
          {
            title: '点击数',
            align: 'center',
            dataIndex: 'hits'
          },
          {
            title: '是否置顶',
            align: 'center',
            dataIndex: 'isTop_dictText'
          },
          {
            title: '答题状态',
            align: 'center',
            dataIndex: 'status_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          imgerver: window._CONFIG['staticDomainURL'],
          list: '/base/article/list',
          delete: '/base/article/delete',
          deleteBatch: '/base/article/deleteBatch',
          exportXlsUrl: '/base/article/exportXls',
          importExcelUrl: '/base/article/importExcel',
          hitOrOutShelf: '/base/article/hitOrOutShelf'
        }
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      getAvatarView: function(avatar) {
        return this.url.imgerver + '/' + avatar
      },
      /*上架*/
      hitShelf(record) {
        let that = this
        let httpurl = that.url.hitOrOutShelf
        let formData = {}
        formData.id = record.id
        formData.status = 1
        getAction(httpurl, formData).then((res) => {
          if (res.success) {
            this.loadData()
            that.$message.success('上架成功')
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*下架*/
      outShelf(record) {
        let that = this
        let httpurl = that.url.hitOrOutShelf
        let formData = {}
        formData.id = record.id
        formData.status = 2
        getAction(httpurl, formData).then((res) => {
          if (res.success) {
            this.loadData()
            that.$message.success('下架成功')
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>