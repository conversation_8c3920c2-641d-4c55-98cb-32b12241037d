<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="咨询师姓名">
              <j-input placeholder="请输入咨询师姓名" v-model="queryParam.name"></j-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!--      <a-button type="primary" icon="download" @click="handleExportXls('do_doctor')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
                @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>-->
      <!-- 高级查询区域 -->
      <!-- <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query> -->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{selectedRowKeys.length
        }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" class="j-table-force-nowrap" @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <do-doctor-modal ref="modalForm" @ok="modalFormOk"></do-doctor-modal>
  </a-card>
</template>

<script>

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DoDoctorModal from './modules/DoDoctorModal'

export default {
  name: 'DoDoctorList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    DoDoctorModal
  },
  data () {
    return {
      description: 'do_doctor管理页面',
      // 表头
      columns: [
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'name'
        },
        // {
        //   title: '院区',
        //   align: 'center',
        //   dataIndex: 'campus'
        // },
        {
          title: '组织机构',
          align: 'center',
          dataIndex: 'sysOrgCode_dictText'
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex_dictText'
        },
        {
          title: '年龄',
          align: 'center',
          dataIndex: 'age'
        },
        {
          title: '擅长',
          align: 'center',
          dataIndex: 'skill'
        },
        {
          title: '咨询对象',
          align: 'center',
          dataIndex: 'consultObject'
        },
        {
          title: '咨询须知',
          align: 'center',
          dataIndex: 'consultNotice'
        },
        // {
        //   title: '病人数量',
        //   align: 'center',
        //   dataIndex: 'patientNum'
        // },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/doc/doDoctor/list',
        delete: '/doc/doDoctor/delete',
        deleteBatch: '/doc/doDoctor/deleteBatch',
        exportXlsUrl: '/doc/doDoctor/exportXls',
        importExcelUrl: 'doc/doDoctor/importExcel'

      },
      dictOptions: {},
      superFieldList: []
    }
  },
  created () {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    initDictConfig () {
    },
    getSuperFieldList () {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'name', text: '姓名' })
      fieldList.push({ type: 'string', value: 'campus', text: '院区' })
      fieldList.push({ type: 'string', value: 'section', text: '组织机构' })
      fieldList.push({ type: 'int', value: 'sex', text: '性别（1_男,2_女）' })
      fieldList.push({ type: 'int', value: 'age', text: '年龄' })
      fieldList.push({ type: 'string', value: 'skill', text: '擅长' })
      fieldList.push({ type: 'int', value: 'patientNum', text: '病人数量' })
      this.superFieldList = fieldList
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>