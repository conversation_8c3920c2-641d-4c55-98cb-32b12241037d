<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="用户账号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="username">
              <a-input placeholder="请输入用户账号" v-model="model.username" :readOnly="!!model.id" />
            </a-form-model-item>
          </a-col>

          <template v-if="!model.id">
            <a-col :span="24">
              <a-form-model-item label="登录密码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="password">
                <a-input type="password" placeholder="请输入登录密码" v-model="model.password" />
              </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="确认密码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="confirmpassword">
                <a-input type="password" @blur="handleConfirmBlur" placeholder="请重新输入登录密码" v-model="model.confirmpassword" />
              </a-form-model-item>
            </a-col>
          </template>
          <!-- <a-col :span="24">
            <a-form-model-item label="角色分配" :labelCol="labelCol" :wrapperCol="wrapperCol" v-show="!roleDisabled" prop="selectedroles">
              <j-multi-select-tag :disabled="disableSubmit" v-model="model.selectedroles" :options="rolesOptions" placeholder="请选择角色">
              </j-multi-select-tag>
            </a-form-model-item>
          </a-col> -->
          <a-col :span="24">
            <a-form-model-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入姓名"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="头像" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload class="avatar-uploader" text="上传" v-model="model.avatar"></j-image-upload>
            </a-form-model-item>
          </a-col>
          <!--          <a-col :span="24">-->
          <!--            <a-form-model-item label="院区" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="campus">-->
          <!--              <a-input v-model="model.campus" placeholder="请输入院区"></a-input>-->
          <!--            </a-form-model-item>-->
          <!--          </a-col>-->
          <!--          <a-col :span="24">-->
          <!--            <a-form-model-item label="组织机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="section">-->
          <!--              <a-input v-model="model.section" placeholder="请输入组织机构"></a-input>-->
          <!--            </a-form-model-item>-->
          <!--          </a-col>-->
          <a-col :span="24">
            <a-form-model-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sex">
              <j-dict-select-tag type="radio" v-model="model.sex" dictCode="sex" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="年龄" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="age">
              <a-input-number :min="0" v-model="model.age" placeholder="请输入年龄" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="从业时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="workStartDate">
              <a-date-picker v-model="model.workStartDate" format="YYYY-MM-DD" placeholder="请选择从业时间" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="擅长方向" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="skill">
              <a-textarea row="5" v-model="model.skill" placeholder="请输入擅长方向"></a-textarea>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="咨询对象" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="consultObject">
              <a-textarea row="5" v-model="model.consultObject" placeholder="请输入咨询对象"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="擅长领域" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="skillSpace">
              <a-textarea row="5" v-model="model.skillSpace" placeholder="请输入擅长领域"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="咨询须知" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="consultNotice">
              <a-textarea row="5" v-model="model.consultNotice" placeholder="请输入擅长领域"></a-textarea>
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="个人资质" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description">
              <a-textarea row="5" v-model="model.description" placeholder="请输入个人资质"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction } from '@/api/manage'
import ATextarea from 'ant-design-vue/es/input/TextArea'
import ACol from 'ant-design-vue/es/grid/Col'
import { duplicateCheck, queryall } from '@/api/api'
import { formatDate } from '@/utils/util'


export default {
  name: 'DoDoctorForm',
  components: { ACol, ATextarea },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      model: {},
      rolesOptions: [],
      userId: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      roleDisabled: true,
      disableSubmit: false,
      validatorRules: {
        username: [{ required: true, message: '请输入用户账号!' },
        { validator: this.validateUsername }],
        password: [{
          required: true,
          pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,
          message: '密码由8位数字、大小写字母和特殊符号组成!'
        },
        { validator: this.validateToNextPassword, trigger: 'change' }],
        confirmpassword: [{ required: true, message: '请重新输入登录密码!' },
        { validator: this.compareToFirstPassword }],
        name: [{ required: true, message: '请输入姓名!' }],
        campus: [{ required: true, message: '请输入院区!' }],
        section: [{ required: true, message: '请输入组织机构!' }],
        sex: [{ required: true, message: '请选择性别!' }],
        age: [{ required: true, message: '请输入年龄!' }],
        skill: [{ required: true, message: '请输入擅长!' }],
        // selectedroles: [{ required: true, message: '请选择角色!' }]

      },
      url: {
        add: '/doc/doDoctor/add',
        edit: '/doc/doDoctor/edit',
        queryById: '/doc/doDoctor/queryById'
      }
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
    this.initRoleList()
  },
  methods: {
    //初始化角色字典
    initRoleList () {
      queryall().then((res) => {
        if (res.success) {
          console.log('res.result: ', res.result)
          this.rolesOptions = res.result.filter(item => ['headerTeacher', 'ordinaryTeacher', 'Consultant'].includes(item.roleCode)).map((item, index, arr) => {
            let c = { label: item.roleName, value: item.id }
            return c;
          })
          console.log('this.rolesOptions: ', this.rolesOptions)
        }
      });
    },
    add () {
      this.edit(this.modelDefault)
    },
    edit (record) {
      this.model = Object.assign({}, record)
      console.log(record.sysUserId)
      this.userId = record.sysUserId;
      this.visible = true
      if (this.model.id) {
        this.roleDisabled = true;
      } else {
        this.roleDisabled = false;
      }
    },
    submitForm () {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          if (this.model.workStartDate) {
            console.log(new Date(this.model.workStartDate).getTime());
            this.model.workStartDate = formatDate(new Date(this.model.workStartDate).getTime(), 'yyyy-MM-dd')
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    },
    handleConfirmBlur (e) {
      const value = e.target.value
      this.confirmDirty = this.confirmDirty || !!value
    },
    validateUsername (rule, value, callback) {
      var params = {
        tableName: 'sys_user',
        fieldName: 'username',
        fieldVal: value,
        dataId: this.userId
      }
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback()
        } else {
          callback('用户名已存在!')
        }
      })
    },
    validateToNextPassword (rule, value, callback) {
      const confirmpassword = this.model.confirmpassword
      if (value && confirmpassword && value !== confirmpassword) {
        callback('两次输入的密码不一样！')
      }
      if (value && this.confirmDirty) {
        this.$refs.form.validateField(['confirmpassword'])
      }
      callback()
    },
    compareToFirstPassword (rule, value, callback) {
      if (value && value !== this.model.password) {
        callback('两次输入的密码不一样！')
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped>
.avatar-uploader > .ant-upload {
  width: 104px;
  height: 104px;
}
</style>