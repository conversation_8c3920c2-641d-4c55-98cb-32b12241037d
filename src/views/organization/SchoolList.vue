<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :lg="6" :sm="8">
            <a-form-item label="学校名称">
              <j-input placeholder="请输入学校名称" v-model="queryParam.departName"></j-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8">
            <a-form-item label="所属省">
              <j-input placeholder="请输入所属省" v-model="queryParam.province"></j-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8">
            <a-form-item label="所属市">
              <j-input placeholder="请输入所属市" v-model="queryParam.city"></j-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :sm="8">
            <a-form-item label="所属区">
              <j-input placeholder="请输入所属区" v-model="queryParam.district"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- table区域-begin -->
    <div>
      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange">
        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
        </span>

      </a-table>
    </div>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'SchoolList',
  mixins: [JeecgListMixin, mixinDevice],
  data () {
    return {
      description: '学校管理',
      // 表头 学校编码、学校名称、所属机构、地址、联系人、联系电话、备注、创建时间
      columns: [
        {
          title: '学校编码',
          align: "center",
          dataIndex: 'orgCode',
          width: 120
        },
        {
          title: '学校名称',
          align: "center",
          dataIndex: 'departName',
          width: 180
        },
        {
          title: '省',
          align: "center",
          dataIndex: 'province',
          width: 120
        },
        {
          title: '市',
          align: "center",
          dataIndex: 'city',
          width: 120
        },
        {
          title: '区',
          align: "center",
          dataIndex: 'district',
          width: 120
        },
        {
          title: '地址',
          align: "center",
          dataIndex: 'address',
          width: 200
        },
        {
          title: '联系人',
          align: "center",
          dataIndex: 'contactPerson',
          width: 120
        },
        {
          title: '联系电话',
          align: "center",
          dataIndex: 'mobile',
          width: 120
        },
        {
          title: '备注',
          align: "center",
          dataIndex: 'memo',
          ellipsis: true
        },
        {
          title: '创建时间',
          align: "center",
          dataIndex: 'createTime',
          width: 180
        },
        // {
        //   title: '操作',
        //   dataIndex: 'action',
        //   align: "center",
        //   width: 147,
        //   scopedSlots: { customRender: 'action' }
        // }
      ],
      url: {
        list: "/sys/sysDepart/listSchool",

      },
      dictOptions: {},
      superFieldList: [],
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>