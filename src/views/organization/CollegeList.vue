<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :lg="6" :sm="8">
            <a-form-item label="学院名称">
              <j-input placeholder="请输入学院名称" v-model="queryParam.departName"></j-input>
            </a-form-item>
          </a-col>
          <!-- <a-col :lg="6" :sm="8">
            <a-form-item label="学校名称">
              <a-input placeholder="请输入学校名称" v-model="queryParam.patientName"></a-input>
            </a-form-item>
          </a-col> -->
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <!-- <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery" style="margin-left: 8px"></j-super-query> -->
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- v-has="'sleepDiary:add'" -->
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!--      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>-->
      <!--      <a-button type="primary" icon="download" @click="handleExportXls('睡眠日记主表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      &lt;!&ndash; 高级查询区域 &ndash;&gt;
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>-->
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered rowKey="id" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" class="j-table-force-nowrap" @change="handleTableChange" :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}">

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>

    <OrgModal ref="modalForm" :disableOrgCategory="disableOrgCategory" @ok="modalFormOk"></OrgModal>
  </a-card>
</template>

<script>

import '@assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import OrgModal from './modules/OrgModal.vue'

export default {
  name: 'CollegeList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    OrgModal
  },
  data () {
    return {
      disableOrgCategory: ['2', '3', '4', '5'],
      description: '学院管理',
      columns: [
        {
          title: '学院编码',
          align: "center",
          dataIndex: 'orgCode'
        },
        {
          title: '学院名称',
          align: "center",
          dataIndex: 'departName'
        },
        {
          title: '地址',
          align: "center",
          dataIndex: 'address'
        },
        {
          title: '备注',
          align: "center",
          dataIndex: 'memo',
          ellipsis: true
        },
        {
          title: '创建时间',
          align: "center",
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: "/sys/sysDepart/listCollege",
        // delete: "/pa/paSleepDiary/delete",
        // deleteBatch: "/pa/paSleepDiary/deleteBatch",
        // exportXlsUrl: "/pa/paSleepDiary/exportXls",
        // importExcelUrl: "pa/paSleepDiary/importExcel",

      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created () {
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    },
  },
  methods: {
    changeDateQuery () {
      if (this.queryParam.timeArray) {
        this.queryParam.startDate = this.queryParam.timeArray[0]
        this.queryParam.endDate = this.queryParam.timeArray[1]
      } else {
        this.queryParam.startDate = ''
        this.queryParam.endDate = ''
      }
    },
    handleChartDetail (record) {
      this.$refs.modalChart.edit(record);
      this.$refs.modalChart.title = "详情";
      this.$refs.modalChart.disableSubmit = true;
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>