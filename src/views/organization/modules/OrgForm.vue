<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <!-- 系/部编码、系/部名称、学院名称（下拉选择，数据来源为学院管理）、系/部介绍 -->
        <a-row>
          <!-- <a-col :span="24">
            <a-form-model-item label="系/部编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="recordTime">
              <a-input placeholder="请输入系/部编码" v-model="model.recordTime" style="width: 100%" />
            </a-form-model-item>
          </a-col> -->
          <a-col :span="24">
            <a-form-model-item label="所属组织" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="parentId">
              <j-select-depart2 v-model="model.parentId" :multi="false" :backDepart="true" :disableOrgCategory="disableOrgCategory"></j-select-depart2>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="departName">
              <a-input placeholder="请输入名称" v-model="model.departName" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
              <a-input placeholder="请输入地址" v-model="model.address" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="介绍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="memo">
              <a-textarea :rows="5" v-model="model.memo" placeholder="请输入介绍" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction } from '@api/manage'
import moment, { Moment } from 'moment';
let disableOrgCategoryDefault = ['1', '2', '3', '4', '5']
export default {
  name: 'PaSleepDiaryForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    },
    disableOrgCategory: {
      type: Array,
      default: disableOrgCategoryDefault,
      required: false
    }
  },
  data () {
    return {
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      validatorRules: {
        departName: [{ required: true, message: '请输入名称!' }],
        parentId: [{ required: true, message: '请选择所属组织!' }],
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/sys/sysDepart/add',
        edit: '/sys/sysDepart/edit',
        queryById: '/sys/sysDepart/queryById'
      }
    }
  },
  computed: {
    formDisabled () {
      return this.disabled
    }
  },
  created () {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    disabledDate () {
      return new Date() && new Date() < moment().endOf('day');
    },
    moment,
    add () {
      this.edit(this.modelDefault)
    },
    edit (record) {
      this.model = Object.assign({}, record)
      this.visible = true
    },
    submitForm () {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let orgCategory = this.disableOrgCategory[0];
          let model = Object.assign({}, this.model, { orgCategory: orgCategory })
          httpAction(httpurl, model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }

      })
    }
  }
}
</script>