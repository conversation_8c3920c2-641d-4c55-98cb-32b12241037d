<template>
  <j-modal :title="title" :width="width" :visible="visible" switchFullscreen @ok="handleOk" :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" @cancel="handleCancel" cancelText="关闭">
    <OrgForm ref="realForm" :disableOrgCategory="disableOrgCategory" @ok="submitCallback" :disabled="disableSubmit"></OrgForm>
  </j-modal>
</template>

<script>

import OrgForm from './OrgForm.vue'
export default {
  name: 'PaSleepDiaryModal',
  props: ['disableOrgCategory'],
  components: {
    OrgForm
  },
  data () {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false
    }
  },
  methods: {
    add () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add();
      })
    },
    edit (record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record);
      })
    },
    close () {
      this.$emit('close');
      this.visible = false;
    },
    handleOk () {
      this.$refs.realForm.submitForm();
    },
    submitCallback () {
      this.$emit('ok');
      this.visible = false;
    },
    handleCancel () {
      this.close()
    }
  }
}
</script>