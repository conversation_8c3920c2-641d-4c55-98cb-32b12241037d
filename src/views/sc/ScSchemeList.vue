<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item :label="`${activeKey === 1 ? '方案' : '计划'}名称`">
              <a-input :placeholder="`请输入${activeKey === 1 ? '方案' : '计划'}名称`" v-model="commonName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedSchemeRowKeys.length > 0 || selectedPlanRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-tabs :default-active-key="1" @change="handleTabChange">
        <a-tab-pane :key="1" tab="方案管理">
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            class="j-table-force-nowrap"
            :columns="schemeColumns"
            :dataSource="schemeDataSource"
            :pagination="schemeIpagination"
            :loading="schemeLoading"
            :rowSelection="{selectedRowKeys: selectedSchemeRowKeys, onChange: onSelectSchemeChange}"
            @change="handleTableChange">

            <span slot="action" slot-scope="text, record">
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical"/>
              <a @click="handleDetail(record)">详情</a>
              <a-divider type="vertical"/>
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                <a>删除</a>
              </a-popconfirm>
            </span>

          </a-table>
        </a-tab-pane>

        <a-tab-pane :key="2" tab="计划管理" force-render>
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            class="j-table-force-nowrap"
            :columns="planColumns"
            :dataSource="planDataSource"
            :pagination="planIpagination"
            :loading="planLoading"
            :rowSelection="{selectedRowKeys: selectedPlanRowKeys, onChange: onSelectPlanChange}"
            @change="handleTableChange">

            <span slot="action" slot-scope="text, record">
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical"/>
              <a @click="handleDetail(record)">详情</a>
              <a-divider type="vertical"/>
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                <a>删除</a>
              </a-popconfirm>
            </span>

          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>

    <sc-scheme-modal ref="schemeModalForm" @ok="modalFormOk" />
    <sc-plan-modal ref="planModalForm" @ok="modalFormOk" />
  </a-card>
</template>

<script>

  // import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import ScSchemeModal from './modules/ScSchemeModal'
  import ScPlanModal from './modules/ScPlanModal'
  import '@/assets/less/TableExpand.less'
  import { getAction, deleteAction } from '@/api/manage'

  export default {
    name: 'ScSchemeList',
    // mixins: [JeecgListMixin],
    components: {
      ScSchemeModal,
      ScPlanModal
    },
    data() {
      return {
        description: '学生方案管理页面',
        // 表头
        schemeColumns: [
          {
            title: '方案名称',
            align: 'center',
            dataIndex: 'title'
          },
          {
            title: '治疗天数',
            align: 'center',
            dataIndex: 'dayNum'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        schemeDataSource: [],
        schemeIpagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        schemeLoading: false,
        selectedSchemeRowKeys: [],
        selectionSchemeRows: [],

        planColumns: [
          {
            title: '计划名称',
            align: 'center',
            dataIndex: 'title'
          },
          {
            title: '治疗天数',
            align: 'center',
            dataIndex: 'dayNum'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        planDataSource: [],
        planIpagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        planLoading: false,
        selectedPlanRowKeys: [],
        selectionPlanRows: [],

        url: {
          scSchemeGroupList: '/sc/scSchemeGroup/list',
          scSchemeGroupDelete: '/sc/scSchemeGroup/delete',
          scSchemeGroupDeleteBatch: '/sc/scSchemeGroup/deleteBatch',
          schemeList: '/sc/scScheme/list',
          schemeDelete: '/sc/scScheme/delete',
          schemeDeleteBatch: '/sc/scScheme/deleteBatch',

        },
        dictOptions: {},
        superFieldList: [],
        activeKey: 1,
        commonName: ''
      }
    },
    created() {
      this.getSuperFieldList()
      this.loadData()
    },
    methods: {
      searchQuery() {
        this.loadData(true)
      },
      handleTabChange(key) {
        this.activeKey = Number(key)
        this.commonName = ''
        this.loadData()
      },
      modalFormOk() {
        // 新增/修改 成功时，重载列表
        this.loadData();
        //清空列表选中
        this.onClearSelected()
      },
      loadData(flag) {
        let param = {}
        let url = ''
        if (this.activeKey === 1) {
          this.schemeLoading = true
          param.pageNo = this.schemeIpagination.current;
          param.pageSize = this.schemeIpagination.pageSize;
          if (flag) param.title = this.commonName
          url = this.url.scSchemeGroupList
        } else if (this.activeKey === 2) {
          this.planLoading = true
          param.pageNo = this.planIpagination.current;
          param.pageSize = this.planIpagination.pageSize;
          if (flag) param.title = this.commonName
          url = this.url.schemeList
        }
        
        getAction(url, param).then(res => {
          let { result } = res
          if (this.activeKey === 1) {
            this.schemeDataSource = result.records
            this.schemeIpagination.total = result.total || 0
          } else if (this.activeKey === 2) {
            this.planDataSource = result.records
            this.planIpagination.total = result.total || 0
          }
        }).finally(() => {
          this.schemeLoading = false
          this.planLoading = false
        })
      },
      onSelectSchemeChange(selectedRowKeys, selectionRows) {
        this.selectedSchemeRowKeys = selectedRowKeys
        this.selectionSchemeRows = selectionRows
      },
      onSelectPlanChange(selectedRowKeys, selectionRows) {
        this.selectedPlanRowKeys = selectedRowKeys
        this.selectionPlanRows = selectionRows
      },
      handleTableChange(pagination) {
        if (this.activeKey === 1) {
          this.schemeIpagination = pagination;
        } else if (this.activeKey === 2) {
          this.planIpagination = pagination;
        }
        
        this.loadData();
      },
      handleEdit (record) {
        if (this.activeKey === 1) {
          this.$refs.schemeModalForm.edit(record);
          this.$refs.schemeModalForm.title = "编辑方案";
          this.$refs.schemeModalForm.disableSubmit = false;
        } else if (this.activeKey === 2) {
          this.$refs.planModalForm.edit(record);
          this.$refs.planModalForm.title = "编辑计划";
          this.$refs.planModalForm.disableSubmit = false;
        }
      },
      handleDetail(record){
        if (this.activeKey === 1) {
          this.$refs.schemeModalForm.edit(record);
          this.$refs.schemeModalForm.title="方案详情";
          this.$refs.schemeModalForm.disableSubmit = true;
        } else if (this.activeKey === 2) {
          this.$refs.planModalForm.edit(record);
          this.$refs.planModalForm.title = "计划详情";
          this.$refs.planModalForm.disableSubmit = true;
        }
      },
      handleAdd () {
        if (this.activeKey === 1) {
          this.$refs.schemeModalForm.add();
          this.$refs.schemeModalForm.title = "新增方案";
          this.$refs.schemeModalForm.disableSubmit = false;
        } else if (this.activeKey === 2) {
          this.$refs.planModalForm.add();
          this.$refs.planModalForm.title = "新增计划";
          this.$refs.planModalForm.disableSubmit = false;
        }
      },
      batchDel () {
        if (this.activeKey === 1) {
          if(!this.url.scSchemeGroupDeleteBatch){
            this.$message.error("请设置url.deleteBatch属性!")
            return
          }
          if (this.selectedSchemeRowKeys.length <= 0) {
            this.$message.warning('请选择一条记录！');
            return;
          } else {
            var ids = "";
            for (var a = 0; a < this.selectedSchemeRowKeys.length; a++) {
              ids += this.selectedSchemeRowKeys[a] + ",";
            }
            var that = this;
            this.$confirm({
              title: "确认删除",
              content: "是否删除选中数据?",
              onOk: function () {
                that.schemeLoading = true;
                deleteAction(that.url.scSchemeGroupDeleteBatch, {ids: ids}).then((res) => {
                  if (res.success) {
                    //重新计算分页问题
                    that.reCalculatePage(that.selectedSchemeRowKeys.length)
                    that.$message.success(res.message);
                    that.loadData();
                    that.onClearSelected();
                  } else {
                    that.$message.warning(res.message);
                  }
                }).finally(() => {
                  that.schemeLoading = false;
                });
              }
            });
          }
        } else {
          if(!this.url.schemeDeleteBatch){
            this.$message.error("请设置url.deleteBatch属性!")
            return
          }
          if (this.selectedSchemeRowKeys.length <= 0) {
            this.$message.warning('请选择一条记录！');
            return;
          } else {
            var ids = "";
            for (var a = 0; a < this.selectedSchemeRowKeys.length; a++) {
              ids += this.selectedSchemeRowKeys[a] + ",";
            }
            var that = this;
            this.$confirm({
              title: "确认删除",
              content: "是否删除选中数据?",
              onOk: function () {
                that.schemeLoading = true;
                deleteAction(that.url.schemeDeleteBatch, {ids: ids}).then((res) => {
                  if (res.success) {
                    //重新计算分页问题
                    that.reCalculatePage(that.selectedSchemeRowKeys.length)
                    that.$message.success(res.message);
                    that.loadData();
                    that.onClearSelected();
                  } else {
                    that.$message.warning(res.message);
                  }
                }).finally(() => {
                  that.schemeLoading = false;
                });
              }
            });
          }
        }
        
      },
      handleDelete: function (id) {
        if (this.activeKey === 1) {
          if(!this.url.scSchemeGroupDelete){
            this.$message.error("请设置url.delete属性!")
            return
          }
          deleteAction(this.url.scSchemeGroupDelete, {id: id}).then((res) => {
            if (res.success) {
              //重新计算分页问题
              this.reCalculatePage(1)
              this.$message.success(res.message || res.msg);
              this.loadData();
            } else {
              this.$message.warning(res.message) || res.msg;
            }
          });
        } else {
          if(!this.url.schemeDelete){
            this.$message.error("请设置url.delete属性!")
            return
          }
          deleteAction(this.url.schemeDelete, {id: id}).then((res) => {
            if (res.success) {
              //重新计算分页问题
              this.reCalculatePage(1)
              this.$message.success(res.message || res.msg);
              this.loadData();
            } else {
              this.$message.warning(res.message) || res.msg;
            }
          });
        }
        
      },
      onClearSelected() {
        this.selectedSchemeRowKeys = [];
        this.selectionPlanRows = [];
        this.selectedPlanRowKeys = [];
        this.selectionPlanRows = [];
      },
      reCalculatePage(count){
        if (this.activeKey === 1) {
          //总数量-count
          let total = this.schemeIpagination.total-count;
          //获取删除后的分页数
          let currentIndex=Math.ceil(total/this.schemeIpagination.pageSize);
          //删除后的分页数<所在当前页
          if(currentIndex<this.schemeIpagination.current){
            this.schemeIpagination.current=currentIndex;
          }
          console.log('currentIndex',currentIndex)
        }
        
      },
      getSuperFieldList() {
        let fieldList = []
        fieldList.push({ type: 'string', value: 'name', text: '学生姓名', dictCode: '' })
        fieldList.push({ type: 'int', value: 'dayNum', text: '治疗天数', dictCode: '' })
        fieldList.push({ type: 'string', value: 'status', text: '状态（1启用，0不启用）', dictCode: '' })
        fieldList.push({ type: 'string', value: 'remark', text: '备注', dictCode: '' })
        fieldList.push({ type: 'int', value: 'delFlag', text: '删除状态（0正常，1已删除）', dictCode: '' })
        fieldList.push({ type: 'string', value: 'tenantId', text: '租户id', dictCode: '' })
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>