<template>
  <a-spin :spinning="confirmLoading">
    <a-row>
      <a-col :span="12">
        <j-form-container :disabled="formDisabled">
          <!-- 主表单区域 -->
          <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail" labelAlign="left"
                        :labelCol="labelCol">
            <a-row>
              <a-col :span="24">
                <a-form-model-item label="计划名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
                  <a-input v-model="model.title" placeholder="请输入计划名称" @change="trimmedStr"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
                  <a-textarea :rows="5" v-model="model.remark" placeholder="当前计划的备注说明"></a-textarea>
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item label="图片选择" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="imgType">
                  <a-select v-model="model.imgType" placeholder="请选择图片">
                    <a-select-option
                      v-for="item in imgTypeList"
                      :key="item.id"
                      :value="item.id">
                      {{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </j-form-container>
        <a-table rowKey="schemeSec" :scroll="{ x: '600px' }" :columns="scSchemeSubTable.columns" :data-source="scSchemeSubTable.dataSource"
                 size="small">
          <template slot="titles" slot-scope="scope">
            <div class="title-item">
              <span>第</span>
              <a-select v-model="scope.day" :default-value="1" :disabled="disabled">
                <a-select-option v-for="item in 7" :key="item + 'day'" :value="item">{{ item }}</a-select-option>
              </a-select>
              <span>天</span>
            </div>
          </template>
          <template slot="type" slot-scope="scope">
            <span>{{ scope.type === 1 ? '文章' : scope.type === 2 ? '视频' : scope.type === 3 ? '放松训练' : '量表' }}</span>
          </template>
          <template slot="action" slot-scope="scope">
            <a @click="handleDelete(scope)">删除</a>
          </template>
        </a-table>
      </a-col>

      <a-col :span="11" :offset="1">
        <a-form-model class="search-form" ref="SearchForm" :model="searchModel">
          <a-form-model-item :label="activeKey === 1 ? '课程标题' : activeKey === 2 ? '放松训练标题' : '量表名称'" prop="title">
            <a-input v-model="searchModel.title" :placeholder="`请输入${activeKey === 1 ? '课程标题' : activeKey === 2 ? '放松训练标题' : '量表名称'}`"></a-input>
          </a-form-model-item>
          <a-button type="primary" @click="loadMaterialData" icon="search">查询</a-button>
        </a-form-model>

        <a-tabs :default-active-key="1" @change="handleTabChange">
          <a-tab-pane :key="1" tab="课程">
            <a-table
              ref="table"
              size="middle"
              bordered
              rowKey="id"
              :columns="materialColumns"
              :dataSource="materialDataSource"
              :pagination="materialIpagination"
              :loading="materialLoading"
              @change="handleTableChange"
              class="j-table-force-nowrap">
              <template slot="type" slot-scope="scope">
                {{ scope.type === 1 ? '自定义文章' : '视频' }}
              </template>
              <template slot="action" slot-scope="scope">
                <a @click="handleAdd(scope)">添加</a>
              </template>
            </a-table>
          </a-tab-pane>

          <a-tab-pane :key="2" tab="放松训练" force-render>
            <a-table
              ref="table"
              size="middle"
              bordered
              rowKey="id"
              :columns="baseTrainCloumns"
              :dataSource="baseTrainDataSource"
              :pagination="baseTrainIpagination"
              :loading="baseTrainLoading"
              @change="handleTableChange"
              class="j-table-force-nowrap">
              <template slot="titleImg" slot-scope="text">
                <span v-if="!text" style="font-size: 12px;font-style: italic;">{{ text }}</span>
                <img v-else :src="getImgView(text)" style="height: 50px" />
              </template>
              <template slot="action" slot-scope="scope">
                <a @click="handleAdd(scope)">添加</a>
              </template>
            </a-table>
          </a-tab-pane>

          <a-tab-pane :key="3" tab="量表评估">
            <a-table
              ref="table"
              size="middle"
              bordered
              rowKey="id"
              :columns="psMeasureColumns"
              :dataSource="psMeasureDataSource"
              :pagination="psMeasureIpagination"
              :loading="psMeasureLoading"
              @change="handleTableChange"
              class="j-table-force-nowrap">
              <template slot="action" slot-scope="scope">
                <a @click="handleAdd(scope)">添加</a>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </a-col>
    </a-row>
  </a-spin>
</template>

<script>

  import ARow from 'ant-design-vue/es/grid/Row'
  import ACol from 'ant-design-vue/es/grid/Col'
  import { getAction, httpAction, getFileAccessHttpUrl } from '@/api/manage'

  export default {
    name: 'ScSchemeForm',
    components: { ACol, ARow },
    // props: {
    //   disabled: {
    //     type: Boolean,
    //     default: false
    //   }
    // },
    data() {
      return {
        confirmLoading: false,
        labelCol: {
          xs: { span: 2 },
          sm: { span: 3 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol2: {
          xs: { span: 24 },
          sm: { span: 3 }
        },
        wrapperCol2: {
          xs: { span: 24 },
          sm: { span: 20 }
        },
        model: {
          imgType: 0
        },
        validatorRules: {
          title: [{ required: true, message: '请输入计划名称!' }]
        },
        searchModel: {
          title: ''
        },
        imgTypeList: [
          {
            id: 0,
            name: '默认'
          },
          {
            id: 1,
            name: '认识睡眠'
          },
          {
            id: 2,
            name: '睡眠限制'
          },
          {
            id: 3,
            name: '刺激控制'
          },
          {
            id: 4,
            name: '认知疗法'
          },
          {
            id: 5,
            name: '赶走焦虑'
          },
          {
            id: 6,
            name: '预防复发'
          },
          {
            id: 7,
            name: '整体'
          }
        ],
        // 学生计划子表
        scSchemeSubTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '标题',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'titles' }
            },
            {
              title: '名称',
              align: 'center',
              dataIndex: 'materialName',
            },
            {
              title: '类型',
              align: 'center',
              scopedSlots: { customRender: 'type' }
            },
            {
              title: '操作',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ]
        },
        url: {
          add: '/sc/scScheme/add',
          edit: '/sc/scScheme/edit',
          subSchemeList: '/sc/scScheme/queryById', // 获取方案数据
          materialList: '/base/baseMaterial/list', // 课程
          psMeasureList: '/psychology/psMeasure/list', // 量表列表
          baseTrainList: '/base/baseTrain/list', // 放松训练
        },
        /*素材列表*/
        materialColumns: [
          {
            title: '标题',
            align: 'center',
            dataIndex: 'title'
          },
          {
            title: '素材分类',
            align: 'center',
            scopedSlots: { customRender: 'type' }
          },
          {
            title: '素材分类',
            align: 'center',
            dataIndex: 'materialCategoryName'
          },
          {
            title: '操作',
            align: 'center',
            width: 100,
            scopedSlots: { customRender: 'action' }
          }
        ],
        materialDataSource: [],
        materialIpagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        materialLoading: false,

        // 放松训练
        baseTrainCloumns: [
          {
            title: '标题',
            align: 'center',
            dataIndex: 'title'
          },
          {
            title: '标题图',
            align: 'center',
            dataIndex: 'titleImg',
            scopedSlots: { customRender: 'titleImg' }
          },
          {
            title: '类别',
            align: 'center',
            dataIndex: 'categoryId_dictText'
          },
          {
            title: '操作',
            align: 'center',
            width: 100,
            scopedSlots: { customRender: 'action' }
          }
        ],
        baseTrainDataSource: [],
        baseTrainIpagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        baseTrainLoading: false,

        // 量表
        psMeasureColumns: [
          {
            title: '量表名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '操作',
            align: 'center',
            width: 100,
            scopedSlots: { customRender: 'action' }
          }
        ],
        psMeasureDataSource: [],
        psMeasureIpagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        psMeasureLoading: false,
        activeKey: 1,
        isEdit: false
      }
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      this.loadMaterialData()
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
    },
    methods: {
      trimmedStr(){
        this.model.title = this.model.title.replace(/^\s*/,"")
      },
      getImgView(text){
        if(text && text.indexOf(",")>0){
          text = text.substring(0,text.indexOf(","))
        }
        return getFileAccessHttpUrl(text)
      },
      handleTabChange(key) {
        this.searchModel.title = ''
        this.activeKey = Number(key)
        this.loadMaterialData()
      },
      add() {
        this.edit(this.modelDefault)
        this.isEdit = false
      },
      edit(record) {
        this.model = Object.assign({}, record)
        if (this.model.id) {
          this.loadSchemeSub(this.model.id)
        }
        this.isEdit = true
        if (this.formDisabled) {
          this.scSchemeSubTable.columns = this.scSchemeSubTable.columns.filter(item => item.title !== '操作')
          this.materialColumns = this.materialColumns.filter(item => item.title !== '操作')
          this.baseTrainCloumns = this.baseTrainCloumns.filter(item => item.title !== '操作')
          this.psMeasureColumns = this.psMeasureColumns.filter(item => item.title !== '操作')
        }
      },
      loadSchemeSub(id) {
        getAction(this.url.subSchemeList, { id: id }).then(res => {
          let { result } = res
          result.scSchemeSubList.forEach(item => item.type = Number(item.materialType))
          result.scSchemeTrainList.forEach(item => item.type = 3)
          result.scSchemeMeasureList.forEach(item => item.type = 4)
          this.scSchemeSubTable.dataSource = result.scSchemeSubList.concat(result.scSchemeTrainList).concat(result.scSchemeMeasureList)
          this.scSchemeSubTable.dataSource.forEach((item, index) => {
            item.schemeSec = new Date().getTime() + index
          })
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {

            if (!this.scSchemeSubTable.dataSource.length) {
              that.$message.error('请选择计划内容！')
              return
            }
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            // this.model.scSchemeSubList = this.scSchemeSubTable.dataSource
            this.model.scSchemeSubList = this.scSchemeSubTable.dataSource.filter(item => item.materialId)
            this.model.scSchemeTrainList = this.scSchemeSubTable.dataSource.filter(item => item.trainId)
            this.model.scSchemeMeasureList = this.scSchemeSubTable.dataSource.filter(item => item.measureId)

            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }
        })
      },
      addBefore() {
        this.scSchemeSubTable.dataSource = []
      },
      handleAdd(record) {
        if (this.activeKey === 1) {
          this.scSchemeSubTable.dataSource.push({
            'materialName': record.title || record.name,
            'materialId': record.id,
            'id': record.id,
            'day': 1,
            'type': record.type,
            'schemeSec': new Date().getTime()
          })
        } else if (this.activeKey === 2) {
          this.scSchemeSubTable.dataSource.push({
            'materialName': record.title || record.name,
            'trainId': record.id,
            'id': record.id,
            'day': 1,
            'type': 3,
            'schemeSec': new Date().getTime()
          })
        } else if (this.activeKey === 3) {
          this.scSchemeSubTable.dataSource.push({
            'materialName': record.title || record.name,
            'measureId': record.id,
            'id': record.id,
            'day': 1,
            'type': 4,
            'schemeSec': new Date().getTime()
          })
        }
      },
      handleDelete(record) {
        this.scSchemeSubTable.dataSource = this.scSchemeSubTable.dataSource.filter(item => item.schemeSec != record.schemeSec)
      },
      loadMaterialData() {
        let param = {}
        let url = ''
        if (this.activeKey === 1) {
          this.materialLoading = true
          param.pageNo = this.materialIpagination.current;
          param.pageSize = this.materialIpagination.pageSize;
          param.title = this.searchModel.title
          url = this.url.materialList
        } else if (this.activeKey === 2) {
          this.baseTrainLoading = true
          param.pageNo = this.baseTrainIpagination.current;
          param.pageSize = this.baseTrainIpagination.pageSize;
          param.title = this.searchModel.title
          url = this.url.baseTrainList
        } else if (this.activeKey === 3) {
          this.psMeasureLoading = true
          param.pageNo = this.psMeasureIpagination.current;
          param.pageSize = this.psMeasureIpagination.pageSize;
          param.name = this.searchModel.title
          url = this.url.psMeasureList
        }

        getAction(url, param).then(res => {
          let { result } = res
          if (this.activeKey === 1) {
            this.materialDataSource = result.records
            this.materialIpagination.total = result.total || 0
          } else if (this.activeKey === 2) {
            this.baseTrainDataSource = result.records
            this.baseTrainIpagination.total = result.total || 0
          } else if (this.activeKey === 3) {
            this.psMeasureDataSource = result.records
            this.psMeasureIpagination.total = result.total || 0
          }
        }).finally(() => {
          this.materialLoading = false
          this.baseTrainLoading = false
          this.psMeasureLoading = false
        })
      },
      sortTitle(){
        this.scSchemeSubTable.dataSource.forEach(function(item, index) {
          item.title = '第' + (index + 1) + '天课程'
        })
      },
      handleTableChange(pagination, filters, sorter) {
        if (this.activeKey === 1) {
          this.materialIpagination = pagination;
        } else if (this.activeKey === 2) {
          this.baseTrainIpagination = pagination;
        } else {
          this.psMeasureIpagination = pagination;
        }

        this.loadMaterialData();
      },
    }
  }
</script>

<style lang="scss" scoped>
.title-item {
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-select {
    width: 50px;
    margin: 0 8px;
  }
}

.search-form {
  display: flex;

  .ant-form-item {
    flex: 1;
    display: flex;
    padding-right: 20px;
  }
}
</style>