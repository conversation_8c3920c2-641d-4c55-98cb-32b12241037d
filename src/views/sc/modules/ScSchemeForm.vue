<template>
  <a-spin :spinning="confirmLoading">
    <a-row>
      <a-col :span="12">
        <j-form-container :disabled="formDisabled">
          <!-- 主表单区域 -->
          <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail" labelAlign="left"
                        :labelCol="labelCol">
            <a-row>
              <a-col :span="24">
                <a-form-model-item label="方案名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
                  <a-input v-model="model.title" placeholder="请输入方案名称" @change="trimmedStr"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
                  <a-textarea :rows="5" v-model="model.remark" placeholder="当前方案的备注说明"></a-textarea>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </j-form-container>
        <a-table rowKey="planSec" :scroll="{ x: '400px' }" :columns="scSchemeSubTable.columns" :data-source="scSchemeSubTable.dataSource"
                 size="small">
          <template slot="action" slot-scope="scope">
            <a @click="handleDelete(scope)">删除</a>
          </template>
        </a-table>
      </a-col>
      <a-col :span="11" :offset="1">
         <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :span="18">
                <a-form-item label="计划名称">
                  <a-input placeholder="计划名称" v-model="commonName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <a-table
          ref="table"
          size="middle"
          bordered
          rowKey="id"
          :columns="planColumns"
          :dataSource="planDataSource"
          :pagination="planIpagination"
          :loading="planLoading"
          @change="handleTableChange"
          class="j-table-force-nowrap">
          <template slot="action" slot-scope="scope">
            <a @click="handleAdd(scope)">添加</a>
            <a-divider type="vertical"/>
            <a @click="handleDetail(scope)">详情</a>
          </template>
        </a-table>
      </a-col>
    </a-row>

    <sc-plan-modal ref="planModalForm" />
  </a-spin>
</template>

<script>
  import ScPlanModal from './ScPlanModal'
  import ARow from 'ant-design-vue/es/grid/Row'
  import ACol from 'ant-design-vue/es/grid/Col'
  import { getAction, httpAction } from '@/api/manage'

  export default {
    name: 'ScSchemeForm',
    components: { ACol, ARow, ScPlanModal },
    // props: {
    //   disabled: {
    //     type: Boolean,
    //     default: false
    //   }
    // },
    data() {
      return {
        commonName: '',
        confirmLoading: false,
        labelCol: {
          xs: { span: 2 },
          sm: { span: 3 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol2: {
          xs: { span: 24 },
          sm: { span: 3 }
        },
        wrapperCol2: {
          xs: { span: 24 },
          sm: { span: 20 }
        },
        model: {},
        validatorRules: {
          title: [{ required: true, message: '请输入方案名称!' }]
        },
        // 学生方案子表
        scSchemeSubTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '序号',
              align: 'center',
              customRender: (text, record, index) => index + 1,
            },
            {
              title: '计划名称',
              align: 'center',
              dataIndex: 'title'
            },
            {
              title: '操作',
              align: 'center',
              width: 100,
              scopedSlots: { customRender: 'action' }
            }
          ]
        },
        url: {
          add: '/sc/scSchemeGroup/add',
          edit: '/sc/scSchemeGroup/edit',
          subSchemeGroupList: '/sc/scSchemeGroup/queryScSchemeGroupSubByMainId', // 获取方案数据
          planList: '/sc/scScheme/list', // 课程
        },
        /*计划列表*/
        planColumns: [
          {
            title: '计划名称',
            align: 'center',
            dataIndex: 'title'
          },
          {
            title: '治疗时间',
            align: 'center',
            dataIndex: 'dayNum'
          },
          {
            title: '操作',
            align: 'center',
            width: 100,
            scopedSlots: { customRender: 'action' }
          }
        ],
        planDataSource: [],
        planIpagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        planLoading: false,
        isEdit: false
      }
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    created() {
      this.loadData()
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model))
    },
    methods: {
      searchQuery() {
        this.loadData(true)
      },
      trimmedStr(){
        this.model.title = this.model.title.replace(/^\s*/,"")
      },
      // handleDetail(record){
      //   this.$refs.planModalForm.edit(record);
      //   this.$refs.planModalForm.title = "计划详情";
      //   this.$refs.planModalForm.disableSubmit = true;
      // },
      add() {
        this.edit(this.modelDefault)
        this.isEdit = false
      },
      edit(record) {
        this.model = Object.assign({}, record)
        if (this.model.id) {
          this.loadSchemeSub(this.model.id)
        }
        this.isEdit = true
        if (this.formDisabled) {
          this.scSchemeSubTable.columns = this.scSchemeSubTable.columns.filter(item => item.title !== '操作')
          this.planColumns = this.planColumns.filter(item => item.title !== '操作')
        }
      },
      loadSchemeSub(id) {
        getAction(this.url.subSchemeGroupList, { id: id }).then(res => {
          let { result } = res
          result.forEach((item, index) => {
            item.planSec = new Date().getTime() + index
          })
          this.scSchemeSubTable.dataSource = result
        })
      },
      submitForm() {
        const that = this
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {

            if (!this.scSchemeSubTable.dataSource.length) {
              that.$message.error('请选择方案内容！')
              return
            }
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            this.model.scSchemeGroupSubList = this.scSchemeSubTable.dataSource
            delete this.model.tenantId

            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
            })
          }
        })
      },
      addBefore() {
        this.scSchemeSubTable.dataSource = []
      },
      handleAdd(record) {
        this.scSchemeSubTable.dataSource.push({
          'title': record.title,
          'schemeId': record.id,
          'planSec': new Date().getTime()
        })
      },
      handleDelete(record) {
        this.scSchemeSubTable.dataSource = this.scSchemeSubTable.dataSource.filter(item => item.planSec !== record.planSec)
      },
      handleDetail(record) {
        this.$refs.planModalForm.edit(record);
        this.$refs.planModalForm.title = "计划详情";
        this.$refs.planModalForm.disableSubmit = true;
      },
      loadData(flag) {
        let param = {}
        let url = ''
        this.planLoading = true
        param.pageNo = this.planIpagination.current;
        param.pageSize = this.planIpagination.pageSize;
        if (flag) param.title = this.commonName
        url = this.url.planList

        getAction(url, param).then(res => {
          let { result } = res
          this.planDataSource = result.records
          this.planIpagination.total = result.total || 0
        }).finally(() => {
          this.planLoading = false
        })
      },
      handleTableChange(pagination) {
        this.planIpagination = pagination;
        this.loadData();
      },
    }
  }
</script>

<style lang="scss" scoped>
.title-item {
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-select {
    width: 50px;
    margin: 0 8px;
  }
}
</style>