<template>
  <div class="page" style="user-select:none;">
    <div class="bg">
      <img src="@/assets/image/relax.png" alt="">
    </div>
    <div class="box">
      <div class="boxLeft">
        <div class="list">
            <div class="shuimian">
              <div class="title">
                睡眠调整
              </div>
              <div class="mess">
                专业睡眠管理，与您一起入睡
              </div>
              <div class="num">
                累计使用人次 &nbsp;&nbsp; 891
              </div>
            </div>
            <div class="huxi">
              <div class="title">
                呼吸训练
              </div>
              <div class="mess">
                吐纳之间，感受思绪的平静与放松
              </div>
              <div class="num">
                累计使用人次 &nbsp;&nbsp; 891
              </div>
            </div>
            <div class="yinyue">
              <div class="title">
                音乐疗愈
              </div>
              <div class="mess">
                精选治愈音乐，助力身心健康
              </div>
              <div class="num">
                累计使用人次 &nbsp;&nbsp; 891
              </div>
            </div>
        </div>
        <div class="list">
          <div class="mingxiang">
            <div class="title">
              正念冥想
            </div>
            <div class="mess">
              正念内观，清净入耳，宁静入心
            </div>
            <div class="num">
              累计使用人次 &nbsp;&nbsp; 891
            </div>
          </div>
          <div class="anmo">
            <div class="title">
              大脑按摩
            </div>
            <div class="mess">
              ASMR，为你打造一个沉浸专注的空间
            </div>
            <div class="num">
              累计使用人次 &nbsp;&nbsp; 891
            </div>
          </div>
          <div class="yingshi">
            <div class="title">
              影视欣赏
            </div>
            <div class="mess">
              治愈系短片，让你感受心的成长
            </div>
            <div class="num">
              累计使用人次 &nbsp;&nbsp; 891
            </div>
          </div>
      </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EducationHome',
  components: {
  },
  data() {
    return {
    }
  },
  filters: {

  },
  created() {

  },
  methods: {
    getChange(index, type) {
      this[type] = index
    },
    // 路由跳转
    getRouter() {
      this.$router.push({ path: '/acti/detail' });
    },
  }
}
</script>

<style lang="less" scoped>
.box {
  display: flex;
  align-items: flex-start;

  .top {
    padding: 21px 24px;
    background: #FFFFFF;
    border-radius: 16px;
  }


  .tabRight {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: 10px;

    div {
      margin-right: 30px;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
    }

    .active {
      color: #0486FE;
    }
  }

  .boxLeft {
    flex: 1;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 30px 26px;
  }

  .boxRight {
    width: 320px;
    border-radius: 16px;
    margin-left: 20px;
  }
}

.bg {
  img {
    width: 100%;
    margin-bottom: 20px;
  }
}
.list{
  display: flex;
  align-items: center;
  justify-content: center;
  >div{
    width: 344px;
    height: 166px;
    margin: 10px;
    padding: 37px 24px;
  }
  .shuimian{
    background: url(../../assets/image/fangsong/one.png) no-repeat;
    background-size: 100% 100%;
  }
  .huxi{
    background: url(../../assets/image/fangsong/sex.png) no-repeat;
    background-size: 100% 100%;
  }
  .yinyue{
    background: url(../../assets/image/fangsong/three.png) no-repeat;
    background-size: 100% 100%;
  }
  .mingxiang{
    background: url(../../assets/image/fangsong/four.png) no-repeat;
    background-size: 100% 100%;
  }
  .anmo{
    background: url(../../assets/image/fangsong/five.png) no-repeat;
    background-size: 100% 100%;
  }
  .yingshi{
    background: url(../../assets/image/fangsong/two.png) no-repeat;
    background-size: 100% 100%;
  }
  .title{
    font-size: 15px;
    color: #333333;
  }
  .mess{
    color: #666666;
    font-size: 13px;
    margin-top: 20px;
  }
  .num{
    color: #666666;
    font-size: 13px;
    margin-top: 8px;
  }
}
</style>
