import store from '@/store/'

let isInit = false
let userId = ''

export const WebsocketMixin = {
  data() {
    return {
      socket: null,
      lockReconnect: false,//是否真正建立连接
      heartCheck: null
    }
  },
  mounted() {
    // 初始化websocket
    this.initWebSocket()
    this.heartCheckFun()
  },
  destroyed: function() {
    // 离开页面生命周期函数
    this.websocketOnclose()
  },
  methods: {
    initWebSocket: function() {
      // console.log("开始WebSocket连接...")
      // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https
      var userInfo = store.getters.userInfo
      if (userInfo && !isInit && userId != userInfo.id) {
        var url = `${window._CONFIG['websocketURL']}` + '/websocket/' + userInfo.id
        this.websock = new WebSocket(url)
        this.websock.onopen = this.websocketOnopen
        this.websock.onerror = this.websocketOnerror
        this.websock.onmessage = this.websocketOnmessage
        this.websock.onclose = this.websocketOnclose
        isInit = true
      }
    },
    websocketOnopen: function() {
      // console.log('WebSocket连接成功')
      //心跳检测重置
      this.heartCheck.reset().start()
    },
    websocketOnerror: function(e) {
      // console.log('WebSocket连接发生错误')
      this.reconnect()
    },
    websocketOnclose: function(e) {
      // console.log('WebSocket连接关闭')
      isInit = false
      this.reconnect()
    },
    websocketSend(text) {
      // 数据发送
      try {
        this.websock.send(text)
      } catch (err) {
        console.log('send failed (' + err.code + ')')
      }
    },
    reconnect() {
      var that = this
      if (that.lockReconnect) return
      that.lockReconnect = true
      //没连接上会一直重连，设置延迟避免请求过多 10s连接一次
      setTimeout(function() {
        that.initWebSocket()
        that.lockReconnect = false
      }, 10000)
    },
    websocketOnmessage: function(msg) {
      isInit = true
      console.log(msg)
      let strs = msg.data.split('&&')
      let that = this
      if (strs[0] == '101') {
        let type = strs[2]
        if (type == '1' || type == '0') {
          that.$router.push({
            name: 'answerPage',
            params: { userId: strs[1], type: type }
          })
        } else if (type == '2') {
          that.$router.push({
            name: 'answerPage',
            params: { resultId: strs[1], type: type }
          })
        }
      }
      //心跳检测重置
      this.heartCheck.reset().start()
    },
    pushTerminalOpenUserForm(measureIds, terminalId) {
      this.$refs.UserForm.pushTerminalLoad(measureIds.split(','), terminalId)
      this.$refs.UserForm.title = '添加测试者信息'
    },
    pushTerminalOpenUserForm2(trainIds) {
      this.$refs.UserForm2.loadMeasureIds(trainIds.split(','))
      this.$refs.UserForm2.title = '添加测试者信息'
    },
    heartCheckFun() {
      var that = this
      //心跳检测,每20s心跳一次
      that.heartCheck = {
        timeout: 30000,
        timeoutObj: null,
        serverTimeoutObj: null,
        reset: function() {
          clearTimeout(this.timeoutObj)
          //clearTimeout(this.serverTimeoutObj);
          return this
        },
        start: function() {
          // console.log("心跳检测...")
          var self = this
          this.timeoutObj = setTimeout(function() {
            //这里发送一个心跳，后端收到后，返回一个心跳消息，
            //onmessage拿到返回的心跳就说明连接正常
            that.websocketSend('HeartBeat')
          }, this.timeout)
        }
      }
    }
  }
}