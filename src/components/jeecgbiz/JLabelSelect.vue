<template>
  <a-tree-select
    :value="momVal"
    :tree-data="options"
    tree-default-expand-all
    placeholder="请选择学生标签"
    allow-clear
    :multiple="multiple"
    @change="onChange"
  />
</template>

<script>
import { getAction } from '@/api/manage'

// 树形标签选择器
export default {
  name: 'JLabelSelect',

  props: {
    multiple: {
      type: Boolean,
      default: true
    },

    defaultValue: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      momVal: [],
      options: [],
    }
  },

  created() {
    this.getList()
  },

  methods: {
    onChange(value) {

      this.momVal = value
      this.$emit('change', value, this.options)
    },

    getList() {
      getAction('/label/tree').then((res) => {
        if (res.success) {
          let result = res.result
          result.forEach(it1 => {
            it1.step = 1
            it1.key = it1.id
            it1.value = it1.id
            it1.title = it1.name
            it1.disabled = true
            if (it1.children) {
              it1.children.forEach(it2 => {
                it2.step = 2
                it2.key = it2.id
                it2.value = it2.id
                it2.title = it2.name
                if (it2.children) {
                  it2.children.forEach(it3 => {
                    it3.step = 3
                    it3.key = it3.id
                    it3.value = it3.id
                    it3.title = it3.name
                  })
                }
              })
            }
          })
          this.options = result

          if (this.defaultValue.length) {
            this.momVal = this.defaultValue.map(it => it.labelId)
          }
        }
      })
    }
  }
}
</script>
