<template>
  <div>
    <a-modal
      centered
      :title="title"
      :width="1000"
      :visible="visible"
      @ok="handleOk"
      @cancel="handleCancel"
      cancelText="关闭">
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline" @keyup.enter.native="searchQuery">
          <a-row :gutter="24">
            <a-col :span="10">
              <a-form-item label="患者姓名">
                <a-input placeholder="请输入患者姓名" v-model="queryParam.name"></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!-- table区域-begin -->
      <div>
        <a-table
          size="small"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          :rowSelection="{selectedRowKeys: selectedRowKeys, type: isRadio ? 'radio' : 'checkbox', onChange: onSelectChange}"
          @change="handleTableChange">
          <template slot="label" slot-scope="scope">
            <div class="label-item">
              <template v-for="(item, index) in scope.labels">
                <a-tag color="#FFAA00" v-if="index < 4" :key="item.labelId">
                  {{ item.labelName }}
                </a-tag>
              </template>

              <a-popover title="标签" v-if="scope.labels.length > 4">
                <template slot="content">
                  <a-tag color="cyan" v-for="item in scope.labels" :key="item.labelId + 'tag'">
                    {{ item.labelName }}
                  </a-tag>
                </template>

                <div>...</div>
              </a-popover>
            </div>
          </template>

          <template slot="imgSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt=""
                 style="max-width:80px;font-size: 12px;font-style:italic;"/>
          </template>
        </a-table>
      </div>
      <!-- table区域-end -->
    </a-modal>
  </div>
</template>

<script>
  import { filterObj } from '@/utils/util'
  import { getAction } from '@/api/manage'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'SelectUserModal',
    mixins: [JeecgListMixin],
    props: {
      isRadio: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        title: '添加已有用户',
        names: [],
        visible: false,
        placement: 'right',
        description: '',
        // 查询条件
        queryParam: {},
        // 表头
        columns: [
          {
            title: '真实姓名',
            align: 'center',
            dataIndex: 'name',
            width: 125
          },
          {
            title: '班级',
            align: 'center',
            dataIndex: 'className',
            width: 125
          },
          {
            title: '编号',
            align: 'center',
            dataIndex: 'number',
            width: 125
          },
          {
            title: '用户头像',
            align: 'center',
            dataIndex: 'avatarUrl',
            scopedSlots: { customRender: 'imgSlot' },
            width: 125
          },
          {
            title: '性别',
            align: 'center',
            dataIndex: 'sex_dictText',
            width: 125
          },
          {
            title: '手机号',
            align: 'center',
            dataIndex: 'telphone',
            width: 125
          }
        ],
        //数据集
        dataSource: [],
        // 分页参数
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        isorter: {
          column: 'createTime',
          order: 'desc'
        },
        loading: false,
        selectedRowKeys: [],
        selectedRows: [],
        url: {
          list: '/pa/paPatient/listByGroup'
        }
      }
    },
    methods: {
      searchQuery() {
        this.loadData(1)
      },
      searchReset() {
        this.queryParam = {}
        this.loadData(1)
      },
      handleCancel() {
        this.visible = false
        this.onClearSelected()
      },
      setRowKeys(value) {
        this.selectedRowKeys = value
      },
      handleOk() {
        let selectedRowKeys = this.selectedRowKeys
        let selectedRows = this.selectedRows
        this.$emit('selectFinished', selectedRows)
        this.selectedRowKeys = [],
        this.selectedRows = [],
        this.visible = false
      },
      add() {
        this.visible = true
      },
      getQueryParams() {
        var param = Object.assign({}, this.queryParam, this.isorter)
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        return filterObj(param)
      },
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      },
      onClearSelected() {
        this.selectedRowKeys = []
        this.selectedRows = []
      },
      handleTableChange(pagination, filters, sorter) {
        if (Object.keys(sorter).length > 0) {
          this.isorter.column = sorter.field
          this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        }
        this.ipagination = pagination
        this.loadData()
      }
    }
  }
</script>
<style lang="less" scoped>
  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }

  .ant-tag {
    margin: 4px !important;
  }

  .label-item {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>