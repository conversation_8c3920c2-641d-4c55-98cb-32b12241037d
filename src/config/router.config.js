import { UserLayout, TabLayout, RouteView, BlankLayout, PageView } from '@/components/layouts'

/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export const asyncRouterMap = [

  {
    path: '/',
    name: 'dashboard',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/analysis',
    children: [
    ]
  },
  {
    path: '*', redirect: '/404', hidden: true
  }
]

/**
 * 基础路由
 * @type { *[] }
 */  
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      // {
      //   path: 'login',
      //   name: 'login',
      //   component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      // },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/register')
      },
    ]
  },
  {
    path: '/user/login',
    name: 'login',
    component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
  },
  {
    path: '/test',
    component: BlankLayout,
    redirect: '/test/home',
    children: [
      {
        path: 'home',
        name: 'TestHome',
        component: () => import('@/views/Home')
      }
    ]
  },
  {
    path: '/screen/screen',
    name: 'screen',
    component: () => import('@/views/screen/screen')
  },
  {
    path: '/psychology/answer',
    name: 'answerPage',
    component: () => import('@/views/dashboard/AnswerPage')
  },
  {
    path: '/user/register',
    name: 'register',
    component: () => import('@/views/user/register')
  },
  {
    path: '/user/password',
    name: 'userPassword',
    component: () => import('@/views/user/password')
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  },

]
