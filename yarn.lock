# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ant-design/colors@^3.1.0":
  version "3.2.2"
  resolved "https://registry.npm.taobao.org/@ant-design/colors/download/@ant-design/colors-3.2.2.tgz"
  integrity sha1-WtQ9YZ6RHzSI66wwPWBuZqhCOQM=
  dependencies:
    tinycolor2 "^1.4.1"

"@ant-design/icons-vue@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/@ant-design/icons-vue/download/@ant-design/icons-vue-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40ant-design%2Ficons-vue%2Fdownload%2F%40ant-design%2Ficons-vue-2.0.0.tgz"
  integrity sha1-A1f1AQpATp80qHpLQbKgjfaR284=
  dependencies:
    "@ant-design/colors" "^3.1.0"
    babel-runtime "^6.26.0"

"@ant-design/icons@^2.0.0", "@ant-design/icons@^2.1.1":
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/@ant-design/icons/download/@ant-design/icons-2.1.1.tgz?cache=0&sync_timestamp=1612952243741&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40ant-design%2Ficons%2Fdownload%2F%40ant-design%2Ficons-2.1.1.tgz"
  integrity sha1-e5wI3/1PXUHbZn2dvl4BB9C9mko=

"@antv/adjust@~0.1.0":
  version "0.1.1"
  resolved "https://registry.npmjs.org/@antv/adjust/-/adjust-0.1.1.tgz"
  integrity sha512-9FaMOyBlM4AgoRL0b5o0VhEKAYkexBNUrxV8XmpHU/9NBPJONBOB/NZUlQDqxtLItrt91tCfbAuMQmF529UX2Q==
  dependencies:
    "@antv/util" "~1.3.1"

"@antv/attr@~0.1.2":
  version "0.1.2"
  resolved "https://registry.npmjs.org/@antv/attr/-/attr-0.1.2.tgz"
  integrity sha512-QXjP+T2I+pJQcwZx1oCA4tipG43vgeCeKcGGKahlcxb71OBAzjJZm1QbF4frKXcnOqRkxVXtCr70X9TRair3Ew==
  dependencies:
    "@antv/util" "~1.3.1"

"@antv/component@~0.3.3":
  version "0.3.9"
  resolved "https://registry.npmjs.org/@antv/component/-/component-0.3.9.tgz"
  integrity sha512-Knh/Nq0S8UkTfZj4SL7XizagTfXYqjFAYIqFtOmUaKpRMgccUi7p1oA7fJdNPGktkndljy6fUmCWocEeBXRS2g==
  dependencies:
    "@antv/attr" "~0.1.2"
    "@antv/g" "~3.3.5"
    "@antv/util" "~1.3.1"
    wolfy87-eventemitter "~5.1.0"

"@antv/coord@~0.1.0":
  version "0.1.0"
  resolved "https://registry.npmjs.org/@antv/coord/-/coord-0.1.0.tgz"
  integrity sha512-W1R8h3Jfb3AfMBVfCreFPMVetgEYuwHBIGn0+d3EgYXe2ckOF8XWjkpGF1fZhOMHREMr+Gt27NGiQh8yBdLUgg==
  dependencies:
    "@antv/util" "~1.3.1"

"@antv/data-set@^0.11.4":
  version "0.11.4"
  resolved "https://registry.npmjs.org/@antv/data-set/-/data-set-0.11.4.tgz"
  integrity sha512-rS0xMvclsv59wh2yDmCshC55/HvXBbwpEmoTr+ErSY4SKGKSTOwXjZ90W/fJMSfacMPOGDbG7R77kCP8cSTu5w==
  dependencies:
    "@antv/hierarchy" "^0.6.0"
    "@antv/util" "^2.0.0"
    d3-composite-projections "^1.2.0"
    d3-dsv "^1.0.5"
    d3-geo "~1.6.4"
    d3-geo-projection "~2.1.2"
    d3-hexjson "^1.0.1"
    d3-hierarchy "^1.1.5"
    d3-sankey "^0.9.1"
    d3-voronoi "^1.1.2"
    dagre "^0.8.2"
    point-at-length "^1.0.2"
    regression "^2.0.0"
    simple-statistics "^6.1.0"
    topojson-client "^3.0.0"
    wolfy87-eventemitter "^5.1.0"

"@antv/g@~3.3.5":
  version "3.3.6"
  resolved "https://registry.npmjs.org/@antv/g/-/g-3.3.6.tgz"
  integrity sha512-2GtyTz++s0BbN6s0ZL2/nrqGYCkd52pVoNH92YkrTdTOvpO6Z4DNoo6jGVgZdPX6Nzwli6yduC8MinVAhE8X6g==
  dependencies:
    "@antv/gl-matrix" "~2.7.1"
    "@antv/util" "~1.3.1"
    d3-ease "~1.0.3"
    d3-interpolate "~1.1.5"
    d3-timer "~1.0.6"
    wolfy87-eventemitter "~5.1.0"

"@antv/g@~3.4.10":
  version "3.4.10"
  resolved "https://registry.npmjs.org/@antv/g/-/g-3.4.10.tgz"
  integrity sha512-pKy/L1SyRBsXuujdkggqrdBA0/ciAgHiArYBdIJsxHRxCneUP01wGwHdGfDayh2+S0gcSBHynjhoEahsaZaLkw==
  dependencies:
    "@antv/gl-matrix" "~2.7.1"
    "@antv/util" "~1.3.1"
    d3-ease "~1.0.3"
    d3-interpolate "~1.1.5"
    d3-timer "~1.0.6"
    detect-browser "^5.1.0"

"@antv/g2-brush@^0.0.2":
  version "0.0.2"
  resolved "https://registry.npmjs.org/@antv/g2-brush/-/g2-brush-0.0.2.tgz"
  integrity sha512-7O9szwem19nmEgReXhFB8kVLRaz8J5MHvrzDSDY36YaBOaHSWRGHnvYt2KkkPqgWtHtLY1srssk4X/UmP5govA==

"@antv/g2-plugin-slider@^2.1.0":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@antv/g2-plugin-slider/-/g2-plugin-slider-2.1.1.tgz"
  integrity sha512-nB678VEGG3FkrvkDDFADAKjLQIeXzITEYqey5oeOpbf0vT5jOa55lQDyJDZ79cK8PmU/Hz6VPeSb3CNQBA+/FQ==

"@antv/g2@>=3.2.8", "@antv/g2@~3.5.3":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@antv/g2/-/g2-3.5.17.tgz"
  integrity sha512-gOjfA6pwXYEC5mrLbvg1kA3jZI5J5T2kQeGse+iBBsNc1Vje7zs9G+BleUaI4MLXSnqwhsj/ohfkP7d+h4ArNg==
  dependencies:
    "@antv/adjust" "~0.1.0"
    "@antv/attr" "~0.1.2"
    "@antv/component" "~0.3.3"
    "@antv/coord" "~0.1.0"
    "@antv/g" "~3.4.10"
    "@antv/scale" "~0.1.1"
    "@antv/util" "~1.3.1"
    venn.js "~0.2.20"
    wolfy87-eventemitter "~5.1.0"

"@antv/gl-matrix@^2.7.1", "@antv/gl-matrix@~2.7.1":
  version "2.7.1"
  resolved "https://registry.npmjs.org/@antv/gl-matrix/-/gl-matrix-2.7.1.tgz"
  integrity sha512-oOWcVNlpELIKi9x+Mm1Vwbz8pXfkbJKykoCIOJ/dNK79hSIANbpXJ5d3Rra9/wZqK6MC961B7sybFhPlLraT3Q==

"@antv/hierarchy@^0.6.0":
  version "0.6.4"
  resolved "https://registry.npmjs.org/@antv/hierarchy/-/hierarchy-0.6.4.tgz"
  integrity sha512-KPQ3Jc+bMObHPXUdj3ZYwcx74BhaNsZHPbNzXnNj6OQusPXr/bdiwEKnKlVDLuW2nUfepfa4zfXS7JirxDKycw==
  dependencies:
    "@antv/util" "^2.0.7"

"@antv/scale@~0.1.1":
  version "0.1.5"
  resolved "https://registry.npmjs.org/@antv/scale/-/scale-0.1.5.tgz"
  integrity sha512-7RAu4iH5+Hk21h6+aBMiDTfmLf4IibK2SWjx/+E4f4AXRpqucO+8u7IbZdFkakAWxvqhJtN3oePJuTKqOMcmlg==
  dependencies:
    "@antv/util" "~1.3.1"
    fecha "~2.3.3"

"@antv/util@^2.0.0", "@antv/util@^2.0.7":
  version "2.0.9"
  resolved "https://registry.npmjs.org/@antv/util/-/util-2.0.9.tgz"
  integrity sha512-JblWzne7msAPDdxkUhEk8zAz0Wd6igKwqymGbvIeyOydGrhBhGjA3nEayFj4IlG+XixCvGFKsCB4yuFS4glRIA==
  dependencies:
    tslib "^1.10.0"

"@antv/util@~1.3.1":
  version "1.3.1"
  resolved "https://registry.npmjs.org/@antv/util/-/util-1.3.1.tgz"
  integrity sha512-cbUta0hIJrKEaW3eKoGarz3Ita+9qUPF2YzTj8A6wds/nNiy20G26ztIWHU+5ThLc13B1n5Ik52LbaCaeg9enA==
  dependencies:
    "@antv/gl-matrix" "^2.7.1"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz"
  integrity sha512-vG6SvB6oYEhvgisZNFRmRCUkLz11c7rp+tbNTynGqc6mS1d5ATd/sGyV6W0KZZnXRKMTzZDRgQT3Ou9jhpAfUg==
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/core/-/core-7.10.4.tgz"
  integrity sha512-3A0tS0HWpy4XujGc7QtOIHTeNwUgWaZc/WuS5YQrfhU67jnVmsD6OGPc1AKHH0LJHQICGncy3+YUjIhVlfDdcA==
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.10.4"
    "@babel/helper-module-transforms" "^7.10.4"
    "@babel/helpers" "^7.10.4"
    "@babel/parser" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.1"
    json5 "^2.1.2"
    lodash "^4.17.13"
    resolve "^1.3.2"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/generator@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.10.4.tgz"
  integrity sha512-toLIHUIAgcQygFZRAQcsLQV3CBuX6yOIru1kJk/qqqvcRmZrYe6WavZTSG+bB8MxhnL9YPf+pKQfuiP161q7ng==
  dependencies:
    "@babel/types" "^7.10.4"
    jsesc "^2.5.1"
    lodash "^4.17.13"
    source-map "^0.5.0"

"@babel/helper-annotate-as-pure@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.10.4.tgz"
  integrity sha512-XQlqKQP4vXFB7BN8fEEerrmYvHp3fK/rBkRFz9jaJbzK0B1DSfej9Kc7ZzE8Z/OnId1jpJdNAZ3BFQjWG68rcA==
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.10.4.tgz"
  integrity sha512-L0zGlFrGWZK4PbT8AszSfLTM5sDU1+Az/En9VrdT8/LmEiJt4zXt+Jve9DCAnQcbqDhCI+29y/L93mrDzddCcg==
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-create-class-features-plugin@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.10.4.tgz"
  integrity sha512-9raUiOsXPxzzLjCXeosApJItoMnX3uyT4QdM2UldffuGApNrF8e938MwNpDCK9CPoyxrEoCgT+hObJc3mZa6lQ==
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-member-expression-to-functions" "^7.10.4"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"

"@babel/helper-create-regexp-features-plugin@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.10.4.tgz"
  integrity sha512-2/hu58IEPKeoLF45DBwx3XFqsbCXmkdAay4spVr2x0jYgRxrSNp+ePwvSsy9g6YSaNDcKIQVPXk1Ov8S2edk2g==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-regex" "^7.10.4"
    regexpu-core "^4.7.0"

"@babel/helper-define-map@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-define-map/-/helper-define-map-7.10.4.tgz"
  integrity sha512-nIij0oKErfCnLUCWaCaHW0Bmtl2RO9cN7+u2QT8yqTywgALKlyUVOvHDElh+b5DwVC6YB1FOYFOTWcN/+41EDA==
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/types" "^7.10.4"
    lodash "^4.17.13"

"@babel/helper-explode-assignable-expression@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.10.4.tgz"
  integrity sha512-4K71RyRQNPRrR85sr5QY4X3VwG4wtVoXZB9+L3r1Gp38DhELyHCtovqydRi7c1Ovb17eRGiQ/FD5s8JdU0Uy5A==
  dependencies:
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-function-name@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.10.4.tgz"
  integrity sha512-YdaSyz1n8gY44EmN7x44zBn9zQ1Ry2Y+3GTA+3vH6Mizke1Vw0aWDM66FOYEPw8//qKkmqOckrGgTYa+6sceqQ==
  dependencies:
    "@babel/helper-get-function-arity" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-get-function-arity@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-get-function-arity/-/helper-get-function-arity-7.10.4.tgz"
  integrity sha512-EkN3YDB+SRDgiIUnNgcmiD361ti+AVbL3f3Henf6dqqUyr5dMsorno0lJWJuLhDhkI5sYEpgj6y9kB8AOU1I2A==
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-hoist-variables@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.10.4.tgz"
  integrity sha512-wljroF5PgCk2juF69kanHVs6vrLwIPNp6DLD+Lrl3hoQ3PpPPikaDRNFA+0t81NOoMt2DL6WW/mdU8k4k6ZzuA==
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-member-expression-to-functions@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.10.4.tgz"
  integrity sha512-m5j85pK/KZhuSdM/8cHUABQTAslV47OjfIB9Cc7P+PvlAoBzdb79BGNfw8RhT5Mq3p+xGd0ZfAKixbrUZx0C7A==
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.10.4.tgz"
  integrity sha512-nEQJHqYavI217oD9+s5MUBzk6x1IlvoS9WTPfgG43CbMEeStE0v+r+TucWdx8KFGowPGvyOkDT9+7DHedIDnVw==
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-module-transforms@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.10.4.tgz"
  integrity sha512-Er2FQX0oa3nV7eM1o0tNCTx7izmQtwAQsIiaLRWtavAAEcskb0XJ5OjJbVrYXWOTr8om921Scabn4/tzlx7j1Q==
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"
    "@babel/helper-simple-access" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"
    lodash "^4.17.13"

"@babel/helper-optimise-call-expression@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.10.4.tgz"
  integrity sha512-n3UGKY4VXwXThEiKrgRAoVPBMqeoPgHVqiHZOanAJCG9nQUL2pLRQirUzl0ioKclHGpGqRgIOkgcIJaIWLpygg==
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.8.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.10.4.tgz"
  integrity sha512-O4KCvQA6lLiMU9l2eawBPMf1xPP8xPfB3iEQw150hOVTqj/rfXz0ThTb4HEzqQfs2Bmo5Ay8BzxfzVtBrr9dVg==

"@babel/helper-regex@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-regex/-/helper-regex-7.10.4.tgz"
  integrity sha512-inWpnHGgtg5NOF0eyHlC0/74/VkdRITY9dtTpB2PrxKKn+AkVMRiZz/Adrx+Ssg+MLDesi2zohBW6MVq6b4pOQ==
  dependencies:
    lodash "^4.17.13"

"@babel/helper-remap-async-to-generator@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.10.4.tgz"
  integrity sha512-86Lsr6NNw3qTNl+TBcF1oRZMaVzJtbWTyTko+CQL/tvNvcGYEFKbLXDPxtW0HKk3McNOk4KzY55itGWCAGK5tg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-wrap-function" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-replace-supers@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.10.4.tgz"
  integrity sha512-sPxZfFXocEymYTdVK1UNmFPBN+Hv5mJkLPsYWwGBxZAxaWfFu+xqp7b6qWD0yjNuNL2VKc6L5M18tOXUP7NU0A==
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.10.4"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-simple-access@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.10.4.tgz"
  integrity sha512-0fMy72ej/VEvF8ULmX6yb5MtHG4uH4Dbd6I/aHDb/JVg0bbivwt9Wg+h3uMvX+QSFtwr5MeItvazbrc4jtRAXw==
  dependencies:
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-split-export-declaration@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.10.4.tgz"
  integrity sha512-pySBTeoUff56fL5CBU2hWm9TesA4r/rOkI9DyJLvvgz09MB9YtfIYe3iBriVaYNaPe+Alua0vBIOVOLs2buWhg==
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-validator-identifier@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.10.4.tgz"
  integrity sha512-3U9y+43hz7ZM+rzG24Qe2mufW5KhvFg/NhnNph+i9mgCtdTCtMJuI1TMkrIUiK7Ix4PYlRF9I5dhqaLYA/ADXw==

"@babel/helper-wrap-function@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.10.4.tgz"
  integrity sha512-6py45WvEF0MhiLrdxtRjKjufwLL1/ob2qDJgg5JgNdojBAZSAKnAjkyOCNug6n+OBl4VW76XjvgSFTdaMcW0Ug==
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helpers@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.10.4.tgz"
  integrity sha512-L2gX/XeUONeEbI78dXSrJzGdz4GQ+ZTA/aazfUsFaWjSe95kiCuOZ5HsXvkiw3iwF+mFHSRUfJU8t6YavocdXA==
  dependencies:
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/highlight@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/highlight/-/highlight-7.10.4.tgz"
  integrity sha512-i6rgnR/YgPEQzZZnbTHHuZdlE8qyoBNalD6F+q4vAFlcMEcqmkoG+mPqJYJCo63qPf74+Y1UZsl3l6f7/RIkmA==
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.10.4", "@babel/parser@^7.7.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.10.4.tgz"
  integrity sha512-8jHII4hf+YVDsskTF6WuMB3X4Eh+PsUkC2ljq22so5rHvH+T8BzyL94VOdyFLNR8tBSVXOTbNHOKpR4TfRxVtA==

"@babel/plugin-proposal-async-generator-functions@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.10.4.tgz"
  integrity sha512-MJbxGSmejEFVOANAezdO39SObkURO5o/8b6fSH6D1pi9RZQt+ldppKPXfqgUWpSQ9asM6xaSaSJIaeWMDRP0Zg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-remap-async-to-generator" "^7.10.4"
    "@babel/plugin-syntax-async-generators" "^7.8.0"

"@babel/plugin-proposal-class-properties@^7.0.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.10.4.tgz"
  integrity sha512-vhwkEROxzcHGNu2mzUC0OFFNXdZ4M23ib8aRRcJSsW8BZK9pQMD7QB7csl97NBbgGZO7ZyHUyKDnxzOaP4IrCg==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-proposal-decorators@^7.1.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.10.4.tgz"
  integrity sha512-JHTWjQngOPv+ZQQqOGv2x6sCCr4IYWy7S1/VH6BE9ZfkoLrdQ2GpEP3tfb5M++G9PwvqjhY8VC/C3tXm+/eHvA==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-decorators" "^7.10.4"

"@babel/plugin-proposal-json-strings@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.10.4.tgz"
  integrity sha512-fCL7QF0Jo83uy1K0P2YXrfX11tj3lkpN7l4dMv9Y9VkowkhkQDwFHFd8IiwyK5MZjE8UpbgokkgtcReH88Abaw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.0"

"@babel/plugin-proposal-object-rest-spread@^7.3.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.10.4.tgz"
  integrity sha512-6vh4SqRuLLarjgeOf4EaROJAHjvu9Gl+/346PbDH9yWbJyfnJ/ah3jmYKYtswEyCoWZiidvVHjHshd4WgjB9BA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.0"
    "@babel/plugin-transform-parameters" "^7.10.4"

"@babel/plugin-proposal-optional-catch-binding@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.10.4.tgz"
  integrity sha512-LflT6nPh+GK2MnFiKDyLiqSqVHkQnVf7hdoAvyTnnKj9xB3docGRsdPuxp6qqqW19ifK3xgc9U5/FwrSaCNX5g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.0"

"@babel/plugin-proposal-unicode-property-regex@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.10.4.tgz"
  integrity sha512-H+3fOgPnEXFL9zGYtKQe4IDOPKYlZdF1kqFDQRRb8PK4B8af1vAGK04tF5iQAAsui+mHNBQSAtd2/ndEDe9wuA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-async-generators@^7.2.0", "@babel/plugin-syntax-async-generators@^7.8.0":
  version "7.8.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-decorators@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.10.4.tgz"
  integrity sha512-2NaoC6fAk2VMdhY1eerkfHV+lVYC1u8b+jmRJISqANCJlTxYy19HGdIkkQtix2UtkcPuPu+IlDgrVseZnU03bw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-dynamic-import@^7.0.0":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-json-strings@^7.2.0", "@babel/plugin-syntax-json-strings@^7.8.0":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.10.4.tgz"
  integrity sha512-KCg9mio9jwiARCB7WAcQ7Y1q+qicILjoK8LP/VkPkEKaf5dkaZZK1EcTe91a3JJlZ3qy6L5s9X52boEYi8DM9g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.2.0", "@babel/plugin-syntax-object-rest-spread@^7.8.0":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.2.0", "@babel/plugin-syntax-optional-catch-binding@^7.8.0":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-transform-arrow-functions@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.10.4.tgz"
  integrity sha512-9J/oD1jV0ZCBcgnoFWFq1vJd4msoKb/TCpGNFyyLt0zABdcvgK3aYikZ8HjzB14c26bc7E3Q1yugpwGy2aTPNA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-async-to-generator@^7.3.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.10.4.tgz"
  integrity sha512-F6nREOan7J5UXTLsDsZG3DXmZSVofr2tGNwfdrVwkDWHfQckbQXnXSPfD7iO+c/2HGqycwyLST3DnZ16n+cBJQ==
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-remap-async-to-generator" "^7.10.4"

"@babel/plugin-transform-block-scoped-functions@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.10.4.tgz"
  integrity sha512-WzXDarQXYYfjaV1szJvN3AD7rZgZzC1JtjJZ8dMHUyiK8mxPRahynp14zzNjU3VkPqPsO38CzxiWO1c9ARZ8JA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-block-scoping@^7.3.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.10.4.tgz"
  integrity sha512-J3b5CluMg3hPUii2onJDRiaVbPtKFPLEaV5dOPY5OeAbDi1iU/UbbFFTgwb7WnanaDy7bjU35kc26W3eM5Qa0A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    lodash "^4.17.13"

"@babel/plugin-transform-classes@^7.3.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.10.4.tgz"
  integrity sha512-2oZ9qLjt161dn1ZE0Ms66xBncQH4In8Sqw1YWgBUZuGVJJS5c0OFZXL6dP2MRHrkU/eKhWg8CzFJhRQl50rQxA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-define-map" "^7.10.4"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.10.4.tgz"
  integrity sha512-JFwVDXcP/hM/TbyzGq3l/XWGut7p46Z3QvqFMXTfk6/09m7xZHJUN9xHfsv7vqqD4YnfI5ueYdSJtXqqBLyjBw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-destructuring@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.10.4.tgz"
  integrity sha512-+WmfvyfsyF603iPa6825mq6Qrb7uLjTOsa3XOFzlYcYDHSS4QmpOWOL0NNBY5qMbvrcf3tq0Cw+v4lxswOBpgA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-dotall-regex@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.10.4.tgz"
  integrity sha512-ZEAVvUTCMlMFAbASYSVQoxIbHm2OkG2MseW6bV2JjIygOjdVv8tuxrCTzj1+Rynh7ODb8GivUy7dzEXzEhuPaA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-duplicate-keys@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.10.4.tgz"
  integrity sha512-GL0/fJnmgMclHiBTTWXNlYjYsA7rDrtsazHG6mglaGSTh0KsrW04qml+Bbz9FL0LcJIRwBWL5ZqlNHKTkU3xAA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-exponentiation-operator@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.10.4.tgz"
  integrity sha512-S5HgLVgkBcRdyQAHbKj+7KyuWx8C6t5oETmUuwz1pt3WTWJhsUV0WIIXuVvfXMxl/QQyHKlSCNNtaIamG8fysw==
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-for-of@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.10.4.tgz"
  integrity sha512-ItdQfAzu9AlEqmusA/65TqJ79eRcgGmpPPFvBnGILXZH975G0LNjP1yjHvGgfuCxqrPPueXOPe+FsvxmxKiHHQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-function-name@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.10.4.tgz"
  integrity sha512-OcDCq2y5+E0dVD5MagT5X+yTRbcvFjDI2ZVAottGH6tzqjx/LKpgkUepu3hp/u4tZBzxxpNGwLsAvGBvQ2mJzg==
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-literals@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.10.4.tgz"
  integrity sha512-Xd/dFSTEVuUWnyZiMu76/InZxLTYilOSr1UlHV+p115Z/Le2Fi1KXkJUYz0b42DfndostYlPub3m8ZTQlMaiqQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-modules-amd@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.10.4.tgz"
  integrity sha512-3Fw+H3WLUrTlzi3zMiZWp3AR4xadAEMv6XRCYnd5jAlLM61Rn+CRJaZMaNvIpcJpQ3vs1kyifYvEVPFfoSkKOA==
  dependencies:
    "@babel/helper-module-transforms" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.10.4.tgz"
  integrity sha512-Xj7Uq5o80HDLlW64rVfDBhao6OX89HKUmb+9vWYaLXBZOma4gA6tw4Ni1O5qVDoZWUV0fxMYA0aYzOawz0l+1w==
  dependencies:
    "@babel/helper-module-transforms" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-simple-access" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.3.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.10.4.tgz"
  integrity sha512-Tb28LlfxrTiOTGtZFsvkjpyjCl9IoaRI52AEU/VIwOwvDQWtbNJsAqTXzh+5R7i74e/OZHH2c2w2fsOqAfnQYQ==
  dependencies:
    "@babel/helper-hoist-variables" "^7.10.4"
    "@babel/helper-module-transforms" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.10.4.tgz"
  integrity sha512-mohW5q3uAEt8T45YT7Qc5ws6mWgJAaL/8BfWD9Dodo1A3RKWli8wTS+WiQ/knF+tXlPirW/1/MqzzGfCExKECA==
  dependencies:
    "@babel/helper-module-transforms" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-named-capturing-groups-regex@^7.3.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.10.4.tgz"
  integrity sha512-V6LuOnD31kTkxQPhKiVYzYC/Jgdq53irJC/xBSmqcNcqFGV+PER4l6rU5SH2Vl7bH9mLDHcc0+l9HUOe4RNGKA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"

"@babel/plugin-transform-new-target@^7.0.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.10.4.tgz"
  integrity sha512-YXwWUDAH/J6dlfwqlWsztI2Puz1NtUAubXhOPLQ5gjR/qmQ5U96DY4FQO8At33JN4XPBhrjB8I4eMmLROjjLjw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-object-super@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.10.4.tgz"
  integrity sha512-5iTw0JkdRdJvr7sY0vHqTpnruUpTea32JHmq/atIWqsnNussbRzjEDyWep8UNztt1B5IusBYg8Irb0bLbiEBCQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"

"@babel/plugin-transform-parameters@^7.10.4", "@babel/plugin-transform-parameters@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.10.4.tgz"
  integrity sha512-RurVtZ/D5nYfEg0iVERXYKEgDFeesHrHfx8RT05Sq57ucj2eOYAP6eu5fynL4Adju4I/mP/I6SO0DqNWAXjfLQ==
  dependencies:
    "@babel/helper-get-function-arity" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-regenerator@^7.3.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.10.4.tgz"
  integrity sha512-3thAHwtor39A7C04XucbMg17RcZ3Qppfxr22wYzZNcVIkPHfpM9J0SO8zuCV6SZa265kxBJSrfKTvDCYqBFXGw==
  dependencies:
    regenerator-transform "^0.14.2"

"@babel/plugin-transform-runtime@^7.4.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.10.4.tgz"
  integrity sha512-8ULlGv8p+Vuxu+kz2Y1dk6MYS2b/Dki+NO6/0ZlfSj5tMalfDL7jI/o/2a+rrWLqSXvnadEqc2WguB4gdQIxZw==
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    resolve "^1.8.1"
    semver "^5.5.1"

"@babel/plugin-transform-shorthand-properties@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.10.4.tgz"
  integrity sha512-AC2K/t7o07KeTIxMoHneyX90v3zkm5cjHJEokrPEAGEy3UCp8sLKfnfOIGdZ194fyN4wfX/zZUWT9trJZ0qc+Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-spread@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.10.4.tgz"
  integrity sha512-1e/51G/Ni+7uH5gktbWv+eCED9pP8ZpRhZB3jOaI3mmzfvJTWHkuyYTv0Z5PYtyM+Tr2Ccr9kUdQxn60fI5WuQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-sticky-regex@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.10.4.tgz"
  integrity sha512-Ddy3QZfIbEV0VYcVtFDCjeE4xwVTJWTmUtorAJkn6u/92Z/nWJNV+mILyqHKrUxXYKA2EoCilgoPePymKL4DvQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-regex" "^7.10.4"

"@babel/plugin-transform-template-literals@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.10.4.tgz"
  integrity sha512-4NErciJkAYe+xI5cqfS8pV/0ntlY5N5Ske/4ImxAVX7mk9Rxt2bwDTGv1Msc2BRJvWQcmYEC+yoMLdX22aE4VQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-typeof-symbol@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.10.4.tgz"
  integrity sha512-QqNgYwuuW0y0H+kUE/GWSR45t/ccRhe14Fs/4ZRouNNQsyd4o3PG4OtHiIrepbM2WKUBDAXKCAK/Lk4VhzTaGA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-unicode-regex@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.10.4.tgz"
  integrity sha512-wNfsc4s8N2qnIwpO/WP2ZiSyjfpTamT2C9V9FDH/Ljub9zw6P3SjkXcFmc0RQUt96k2fmIvtla2MMjgTwIAC+A==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/polyfill@^7.2.5":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/polyfill/-/polyfill-7.10.4.tgz"
  integrity sha512-8BYcnVqQ5kMD2HXoHInBH7H1b/uP3KdnwCYXOqFnXqguOyuu443WXusbIUbWEfY3Z0Txk0M1uG/8YuAMhNl6zg==
  dependencies:
    core-js "^2.6.5"
    regenerator-runtime "^0.13.4"

"@babel/preset-env@^7.0.0 < 7.4.0":
  version "7.3.4"
  resolved "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.3.4.tgz"
  integrity sha512-2mwqfYMK8weA0g0uBKOt4FE3iEodiHy9/CW0b+nWXcbL+pGzLx8ESYc+j9IIxr6LTDHWKgPm71i9smo02bw+gA==
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-async-generator-functions" "^7.2.0"
    "@babel/plugin-proposal-json-strings" "^7.2.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.3.4"
    "@babel/plugin-proposal-optional-catch-binding" "^7.2.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.2.0"
    "@babel/plugin-syntax-async-generators" "^7.2.0"
    "@babel/plugin-syntax-json-strings" "^7.2.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.2.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.2.0"
    "@babel/plugin-transform-arrow-functions" "^7.2.0"
    "@babel/plugin-transform-async-to-generator" "^7.3.4"
    "@babel/plugin-transform-block-scoped-functions" "^7.2.0"
    "@babel/plugin-transform-block-scoping" "^7.3.4"
    "@babel/plugin-transform-classes" "^7.3.4"
    "@babel/plugin-transform-computed-properties" "^7.2.0"
    "@babel/plugin-transform-destructuring" "^7.2.0"
    "@babel/plugin-transform-dotall-regex" "^7.2.0"
    "@babel/plugin-transform-duplicate-keys" "^7.2.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.2.0"
    "@babel/plugin-transform-for-of" "^7.2.0"
    "@babel/plugin-transform-function-name" "^7.2.0"
    "@babel/plugin-transform-literals" "^7.2.0"
    "@babel/plugin-transform-modules-amd" "^7.2.0"
    "@babel/plugin-transform-modules-commonjs" "^7.2.0"
    "@babel/plugin-transform-modules-systemjs" "^7.3.4"
    "@babel/plugin-transform-modules-umd" "^7.2.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.3.0"
    "@babel/plugin-transform-new-target" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.2.0"
    "@babel/plugin-transform-parameters" "^7.2.0"
    "@babel/plugin-transform-regenerator" "^7.3.4"
    "@babel/plugin-transform-shorthand-properties" "^7.2.0"
    "@babel/plugin-transform-spread" "^7.2.0"
    "@babel/plugin-transform-sticky-regex" "^7.2.0"
    "@babel/plugin-transform-template-literals" "^7.2.0"
    "@babel/plugin-transform-typeof-symbol" "^7.2.0"
    "@babel/plugin-transform-unicode-regex" "^7.2.0"
    browserslist "^4.3.4"
    invariant "^2.2.2"
    js-levenshtein "^1.1.3"
    semver "^5.3.0"

"@babel/runtime-corejs2@^7.2.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/runtime-corejs2/-/runtime-corejs2-7.10.4.tgz"
  integrity sha512-9sArmpZDQsnR1yyAcU51DxQrntWxt0LUKjPp3pIyo7kVLfaqKt8muppcT87QmFkXV5H50qXAF8JWOjk0jaXRYA==
  dependencies:
    core-js "^2.6.5"
    regenerator-runtime "^0.13.4"

"@babel/runtime-corejs3@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/runtime-corejs3/-/runtime-corejs3-7.10.4.tgz"
  integrity sha512-BFlgP2SoLO9HJX9WBwN67gHWMBhDX/eDz64Jajd6mR/UAUzqrNMm99d4qHnVaKscAElZoFiPv+JpR/Siud5lXw==
  dependencies:
    core-js-pure "^3.0.0"
    regenerator-runtime "^0.13.4"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.8.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.10.4.tgz"
  integrity sha512-UpTN5yUJr9b4EX2CnGNWIvER7Ab83ibv0pcvvHc4UOdrBI5jb8bj+32cCwPX6xu0mt2daFNjYhoi+X7beH0RSw==
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/template/-/template-7.10.4.tgz"
  integrity sha512-ZCjD27cGJFUB6nmCB1Enki3r+L5kJveX9pq1SvAUKoICy6CZ9yD8xO086YXdYhvNjBdnekm4ZnaP5yC8Cs/1tA==
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/parser" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/traverse@^7.10.4", "@babel/traverse@^7.7.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.10.4.tgz"
  integrity sha512-aSy7p5THgSYm4YyxNGz6jZpXf+Ok40QF3aA2LyIONkDHpAcJzDUqlCKXv6peqYUs2gmic849C/t2HKw2a2K20Q==
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.10.4"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"
    "@babel/parser" "^7.10.4"
    "@babel/types" "^7.10.4"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.13"

"@babel/types@^7.10.4", "@babel/types@^7.7.0":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.10.4.tgz"
  integrity sha512-UTCFOxC3FsFHb7lkRMVvgLzaRVamXuAs2Tz4wajva4WxtVY82eZeaUBtC2Zt95FU9TiznuC0Zk35tsim8jeVpg==
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    lodash "^4.17.13"
    to-fast-properties "^2.0.0"

"@discoveryjs/json-ext@^0.5.0":
  version "0.5.7"
  resolved "https://registry.npmmirror.com/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz"
  integrity sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "https://registry.npmjs.org/@hapi/address/-/address-2.1.4.tgz"
  integrity sha512-QD1PhQk+s31P1ixsX0H0Suoupp3VMXzIVMSwobR3F3MSUO2YCV0B7xqLcUw/Bh8yuvd3LhpyqLQWTNcRmp6IdQ==

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "https://registry.npmjs.org/@hapi/bourne/-/bourne-1.3.2.tgz"
  integrity sha512-1dVNHT76Uu5N3eJNTYcvxee+jzX4Z9lfciqRRHCU27ihbUcYi+iSc2iml5Ke1LXe1SyJCLA0+14Jh4tXJgOppA==

"@hapi/hoek@^8.3.0", "@hapi/hoek@8.x.x":
  version "8.5.1"
  resolved "https://registry.npmjs.org/@hapi/hoek/-/hoek-8.5.1.tgz"
  integrity sha512-yN7kbciD87WzLGc5539Tn0sApjyiGHAJgKvG9W8C7O+6c7qmoQMfVs0W4bX17eqz6C78QJqqFrtgdK5EWf6Qow==

"@hapi/joi@^15.0.1":
  version "15.1.1"
  resolved "https://registry.npmjs.org/@hapi/joi/-/joi-15.1.1.tgz"
  integrity sha512-entf8ZMOK8sc+8YfeOlM8pCfg3b5+WZIKBfUaaJT8UsjAAPjartzxIYm3TIbjvA4u+u++KbcXD38k682nVHDAQ==
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "https://registry.npmjs.org/@hapi/topo/-/topo-3.1.6.tgz"
  integrity sha512-tAag0jEcjwH+P2quUfipd7liWCNX2F8NvYjQp2wtInsZxnMlypdw0FtAOLxtvvkO+GSRRbmNi8m/5y42PQJYCQ==
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  version "1.0.6"
  resolved "https://registry.npmjs.org/@intervolga/optimize-cssnano-plugin/-/optimize-cssnano-plugin-1.0.6.tgz"
  integrity sha512-zN69TnSr0viRSU6cEDIcuPcP67QcpQ6uHACg58FiN9PDrU6SLyGW3MR4tiISbYxy1kDWAVPwD+XwQTWE5cigAA==
  dependencies:
    cssnano "^4.0.0"
    cssnano-preset-default "^4.0.0"
    postcss "^7.0.0"

"@jeecg/antd-online-mini@2.4.3-RC":
  version "2.4.3-RC"
  resolved "https://registry.npmjs.org/@jeecg/antd-online-mini/-/antd-online-mini-2.4.3-RC.tgz"
  integrity sha512-U/0W++7Og6SmqKqnDc0VFLDeEYmXCH4bMN+tUtAVLqwTxyln12cBSD7zD5zja1dWOxyrvt6pYC5cnGpH7CUVVQ==

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "https://registry.npmjs.org/@mrmlnc/readdir-enhanced/-/readdir-enhanced-2.2.1.tgz"
  integrity sha512-bPHp6Ji8b41szTOcaP63VlnbbO5Ny6dwAATtY6JTjh5N2OLrb5Qk/Th5cRkRQhkWCt+EJsYrNB0MiL+Gpn6e3g==
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-1.1.3.tgz"
  integrity sha512-shAmDyaQC4H92APFoIaVDHCx5bStIocgvbwQyxPRrbUY20V1EYTbSDchWbuwlMG3V17cprZhA6+78JfB+3DTPw==

"@simonwep/pickr@~1.7.0":
  version "1.7.4"
  resolved "https://registry.npm.taobao.org/@simonwep/pickr/download/@simonwep/pickr-1.7.4.tgz"
  integrity sha1-sU/NlFiQOIuHDNbbTWx41THyUUE=
  dependencies:
    core-js "^3.6.5"
    nanopop "^2.1.0"

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  version "1.7.1"
  resolved "https://registry.npmjs.org/@soda/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.7.1.tgz"
  integrity sha512-cWKrGaFX+rfbMrAxVv56DzhPNqOJPZuNIS2HGMELtgGzb+vsMzyig9mml5gZ/hr2BGtSLV+dP2LUEuAL8aG2mQ==
  dependencies:
    chalk "^1.1.3"
    error-stack-parser "^2.0.0"
    string-width "^2.0.0"

"@tinymce/tinymce-vue@^2.0.0":
  version "2.1.0"
  resolved "https://registry.npmjs.org/@tinymce/tinymce-vue/-/tinymce-vue-2.1.0.tgz"
  integrity sha512-lDIpeLbkaobS/f00wWaOhGJdiZLdtL0dEDYB4JvqgVeAAoaDFG2PvXXP/kN49xpHpUe8vOdt7xFaN48nrPmsbQ==
  dependencies:
    vue "^2.5.17"

"@toast-ui/editor@^2.1.2":
  version "2.2.0"
  resolved "https://registry.npmjs.org/@toast-ui/editor/-/editor-2.2.0.tgz"
  integrity sha512-WiqrY7OeCOS08NlznJobCwtxOWJC/5my8QefHCKTZyX9/70kkojcnyQ8aoiQQ5kIfGUJ6dKt6/JuKD5OOib+bQ==
  dependencies:
    "@types/codemirror" "0.0.71"
    codemirror "^5.48.4"

"@types/anymatch@*":
  version "1.3.1"
  resolved "https://registry.npmjs.org/@types/anymatch/-/anymatch-1.3.1.tgz"
  integrity sha512-/+CRPXpBDpo2RK9C68N3b2cOvO0Cf5B9aPijHsoDQTHivnGSObdOF2BRQOYjojWTDy6nQvMjmqRXIxH55VjxxA==

"@types/codemirror@0.0.71":
  version "0.0.71"
  resolved "https://registry.npmjs.org/@types/codemirror/-/codemirror-0.0.71.tgz"
  integrity sha512-b2oEEnno1LIGKMR7uBEsr40al1UijF1HEpRn0+Yf1xOLl24iQgB7DBpZVMM7y54G5wCNoclDrRO65E6KHPNO2w==
  dependencies:
    "@types/tern" "*"

"@types/color-name@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@types/color-name/-/color-name-1.1.1.tgz"
  integrity sha512-rr+OQyAjxze7GgWrSaJwydHStIhHq2lvY3BOC2Mj7KnzI7XK0Uw1TOOdI9lDoajEbSWLiYgoo4f1R51erQfhPQ==

"@types/d3-format@*":
  version "1.3.1"
  resolved "https://registry.npmjs.org/@types/d3-format/-/d3-format-1.3.1.tgz"
  integrity sha512-KAWvReOKMDreaAwOjdfQMm0HjcUMlQG47GwqdVKgmm20vTd2pucj0a70c3gUSHrnsmo6H2AMrkBsZU2UhJLq8A==

"@types/estree@*":
  version "0.0.45"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-0.0.45.tgz"
  integrity sha512-jnqIUKDUqJbDIUxm0Uj7bnlMnRm1T/eZ9N+AVMqhPgzrba2GhGG5o/jCTwmdPK709nEZsGoMzXEDUjcXHa3W0g==

"@types/glob@^7.1.1":
  version "7.1.3"
  resolved "https://registry.npmjs.org/@types/glob/-/glob-7.1.3.tgz"
  integrity sha512-SEYeGAIQIQX8NN6LDKprLjbrd5dARM5EXsd8GI/A5l0apYI1fGMWgPHSe4ZKL4eozlAyI+doUE9XbYS4xCkQ1w==
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/html-minifier-terser@^5.0.0":
  version "5.1.0"
  resolved "https://registry.npmjs.org/@types/html-minifier-terser/-/html-minifier-terser-5.1.0.tgz"
  integrity sha512-iYCgjm1dGPRuo12+BStjd1HiVQqhlRhWDOQigNxn023HcjnhsiFz9pc6CzJj4HwDCSQca9bxTL4PxJDbkdm3PA==

"@types/json-schema@^7.0.4", "@types/json-schema@^7.0.5":
  version "7.0.5"
  resolved "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.5.tgz"
  integrity sha512-7+2BITlgjgDhH0vvwZU/HZJVyk+2XUlvxXe8dFMedNX/aMkaOq++rMAFXc0tM7ij15QaWlbdQASBR9dihi+bDQ==

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/lodash@*":
  version "4.14.157"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.14.157.tgz"
  integrity sha512-Ft5BNFmv2pHDgxV5JDsndOWTRJ+56zte0ZpYLowp03tW+K+t8u8YMOzAnpuqPgzX6WO1XpDIUm7u04M8vdDiVQ==

"@types/minimatch@*":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@types/minimatch/-/minimatch-3.0.3.tgz"
  integrity sha512-tHq6qdbT9U1IRSGf14CL0pUlULksvY9OZ+5eEgl1N7t+OA3tGvNpxJCzuKQlsNgCVwbAs670L1vcVQi8j9HjnA==

"@types/node@*":
  version "14.0.20"
  resolved "https://registry.npmjs.org/@types/node/-/node-14.0.20.tgz"
  integrity sha512-MRn/NP3dee8yL5QhbSA6riuwkS+UOcsPUMOIOG3KMUQpuor/2TopdRBu8QaaB4fGU+gz/bzyDWt0FtUbeJ8H1A==

"@types/node@^8.0.53":
  version "8.10.61"
  resolved "https://registry.npmjs.org/@types/node/-/node-8.10.61.tgz"
  integrity sha512-l+zSbvT8TPRaCxL1l9cwHCb0tSqGAGcjPJFItGGYat5oCTiq1uQQKYg5m7AF1mgnEBzFXGLJ2LRmNjtreRX76Q==

"@types/normalize-package-data@^2.4.0":
  version "2.4.0"
  resolved "https://registry.npmjs.org/@types/normalize-package-data/-/normalize-package-data-2.4.0.tgz"
  integrity sha512-f5j5b/Gf71L+dbqxIpQ4Z2WlmI/mPJ0fOkGGmFgtb6sAu97EPczzbS3/tJKxmcYDj55OX6ssqwDAWOHIYDRDGA==

"@types/q@^1.5.1":
  version "1.5.4"
  resolved "https://registry.npmjs.org/@types/q/-/q-1.5.4.tgz"
  integrity sha512-1HcDas8SEj4z1Wc696tH56G8OlRaH/sqZOynNNB+HF0WOeXPaxTtbYzJY2oEfiUxjSKjhCKr+MvR7dCHcEelug==

"@types/source-list-map@*":
  version "0.1.2"
  resolved "https://registry.npmjs.org/@types/source-list-map/-/source-list-map-0.1.2.tgz"
  integrity sha512-K5K+yml8LTo9bWJI/rECfIPrGgxdpeNbj+d53lwN4QjW1MCwlkhUms+gtdzigTeUyBr09+u8BwOIY3MXvHdcsA==

"@types/tapable@*", "@types/tapable@^1.0.5":
  version "1.0.6"
  resolved "https://registry.npmjs.org/@types/tapable/-/tapable-1.0.6.tgz"
  integrity sha512-W+bw9ds02rAQaMvaLYxAbJ6cvguW/iJXNT6lTssS1ps6QdrMKttqEAMEG/b5CR8TZl3/L7/lH0ZV5nNR1LXikA==

"@types/tern@*":
  version "0.23.3"
  resolved "https://registry.npmjs.org/@types/tern/-/tern-0.23.3.tgz"
  integrity sha512-imDtS4TAoTcXk0g7u4kkWqedB3E4qpjXzCpD2LU5M5NAXHzCDsypyvXSaG7mM8DKYkCRa7tFp4tS/lp/Wo7Q3w==
  dependencies:
    "@types/estree" "*"

"@types/uglify-js@*":
  version "3.9.3"
  resolved "https://registry.npmjs.org/@types/uglify-js/-/uglify-js-3.9.3.tgz"
  integrity sha512-KswB5C7Kwduwjj04Ykz+AjvPcfgv/37Za24O2EDzYNbwyzOo8+ydtvzUfZ5UMguiVu29Gx44l1A6VsPPcmYu9w==
  dependencies:
    source-map "^0.6.1"

"@types/webpack-sources@*":
  version "1.4.0"
  resolved "https://registry.npmjs.org/@types/webpack-sources/-/webpack-sources-1.4.0.tgz"
  integrity sha512-c88dKrpSle9BtTqR6ifdaxu1Lvjsl3C5OsfvuUbUwdXymshv1TkufUAXBajCCUM/f/TmnkZC/Esb03MinzSiXQ==
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    source-map "^0.7.3"

"@types/webpack@^4.41.8":
  version "4.41.21"
  resolved "https://registry.npmjs.org/@types/webpack/-/webpack-4.41.21.tgz"
  integrity sha512-2j9WVnNrr/8PLAB5csW44xzQSJwS26aOnICsP3pSGCEdsu6KYtfQ6QJsVUKHWRnm1bL7HziJsfh5fHqth87yKA==
  dependencies:
    "@types/anymatch" "*"
    "@types/node" "*"
    "@types/tapable" "*"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    source-map "^0.6.0"

"@vue/babel-helper-vue-jsx-merge-props@^1.0.0":
  version "1.0.0"
  resolved "https://registry.npmjs.org/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.0.0.tgz"
  integrity sha512-6tyf5Cqm4m6v7buITuwS+jHzPlIPxbFzEhXR5JGZpbrvOcp1hiQKckd305/3C7C36wFekNTQSxAtgeM0j0yoUw==

"@vue/babel-plugin-transform-vue-jsx@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.1.2.tgz"
  integrity sha512-YfdaoSMvD1nj7+DsrwfTvTnhDXI7bsuh+Y5qWwvQXlD24uLgnsoww3qbiZvWf/EoviZMrvqkqN4CBw0W3BWUTQ==
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    html-tags "^2.0.0"
    lodash.kebabcase "^4.1.1"
    svg-tags "^1.0.0"

"@vue/babel-preset-app@^3.12.1":
  version "3.12.1"
  resolved "https://registry.npmjs.org/@vue/babel-preset-app/-/babel-preset-app-3.12.1.tgz"
  integrity sha512-Zjy5jQaikV1Pz+ri0YgXFS7q4/5wCxB5tRkDOEIt5+4105u0Feb/pvH20nVL6nx9GyXrECFfcm7Yxr/z++OaPQ==
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-decorators" "^7.1.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.4.0"
    "@babel/preset-env" "^7.0.0 < 7.4.0"
    "@babel/runtime" "^7.0.0"
    "@babel/runtime-corejs2" "^7.2.0"
    "@vue/babel-preset-jsx" "^1.0.0"
    babel-plugin-dynamic-import-node "^2.2.0"
    babel-plugin-module-resolver "3.2.0"
    core-js "^2.6.5"

"@vue/babel-preset-jsx@^1.0.0":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@vue/babel-preset-jsx/-/babel-preset-jsx-1.1.2.tgz"
  integrity sha512-zDpVnFpeC9YXmvGIDSsKNdL7qCG2rA3gjywLYHPCKDT10erjxF4U+6ay9X6TW5fl4GsDlJp9bVfAVQAAVzxxvQ==
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    "@vue/babel-sugar-functional-vue" "^1.1.2"
    "@vue/babel-sugar-inject-h" "^1.1.2"
    "@vue/babel-sugar-v-model" "^1.1.2"
    "@vue/babel-sugar-v-on" "^1.1.2"

"@vue/babel-sugar-functional-vue@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.1.2.tgz"
  integrity sha512-YhmdJQSVEFF5ETJXzrMpj0nkCXEa39TvVxJTuVjzvP2rgKhdMmQzlJuMv/HpadhZaRVMCCF3AEjjJcK5q/cYzQ==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.1.2.tgz"
  integrity sha512-VRSENdTvD5htpnVp7i7DNuChR5rVMcORdXjvv5HVvpdKHzDZAYiLSD+GhnhxLm3/dMuk8pSzV+k28ECkiN5m8w==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.1.2.tgz"
  integrity sha512-vLXPvNq8vDtt0u9LqFdpGM9W9IWDmCmCyJXuozlq4F4UYVleXJ2Fa+3JsnTZNJcG+pLjjfnEGHci2339Kj5sGg==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    camelcase "^5.0.0"
    html-tags "^2.0.0"
    svg-tags "^1.0.0"

"@vue/babel-sugar-v-on@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.1.2.tgz"
  integrity sha512-T8ZCwC8Jp2uRtcZ88YwZtZXe7eQrJcfRq0uTFy6ShbwYJyz5qWskRFoVsdTi9o0WEhmQXxhQUewodOSCUPVmsQ==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    camelcase "^5.0.0"

"@vue/cli-overlay@^3.12.1":
  version "3.12.1"
  resolved "https://registry.npmjs.org/@vue/cli-overlay/-/cli-overlay-3.12.1.tgz"
  integrity sha512-Bym92EN+lj+cNRN2ozbYyH+V8DMXWGbCDUk+hiJ4EYDBZfBkZKvalk1/mOBFwyxiopnnbOEBAAhL/UuMQ1xARg==

"@vue/cli-plugin-babel@^3.3.0":
  version "3.12.1"
  resolved "https://registry.npmjs.org/@vue/cli-plugin-babel/-/cli-plugin-babel-3.12.1.tgz"
  integrity sha512-Zetvz8PikLCGomeKOKu8pC9YQ7cfxs7pGpvEOzaxGdhMnebhjAYR6i6dOB57A6N5lhxQksXCtYTv26QgfiIpdg==
  dependencies:
    "@babel/core" "^7.0.0"
    "@vue/babel-preset-app" "^3.12.1"
    "@vue/cli-shared-utils" "^3.12.1"
    babel-loader "^8.0.5"
    webpack "^4.0.0"

"@vue/cli-plugin-eslint@^3.3.0":
  version "3.12.1"
  resolved "https://registry.npmjs.org/@vue/cli-plugin-eslint/-/cli-plugin-eslint-3.12.1.tgz"
  integrity sha512-tVTZlEZsy3sQbO4LLWFK11yzlWwqVAqaM+IY+BeWHITBzEJKh2KmouG+x6x/reXiU3qROsMJ4Ej3Hs8buSMWyQ==
  dependencies:
    "@vue/cli-shared-utils" "^3.12.1"
    babel-eslint "^10.0.1"
    eslint-loader "^2.1.2"
    globby "^9.2.0"
    webpack "^4.0.0"
    yorkie "^2.0.0"
  optionalDependencies:
    eslint "^4.19.1"
    eslint-plugin-vue "^4.7.1"

"@vue/cli-service@^3.3.0":
  version "3.12.1"
  resolved "https://registry.npmjs.org/@vue/cli-service/-/cli-service-3.12.1.tgz"
  integrity sha512-PDxNrTGnSKzeV1ruFlsRIAO8JcPizwT0EJXq9GeyooU+p+sOkv7aKkCBJQVYNjZapD1NOGWx6CvAAC/wAW+gew==
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@vue/cli-overlay" "^3.12.1"
    "@vue/cli-shared-utils" "^3.12.1"
    "@vue/component-compiler-utils" "^3.0.0"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    acorn "^6.1.1"
    acorn-walk "^6.1.1"
    address "^1.0.3"
    autoprefixer "^9.5.1"
    browserslist "^4.5.4"
    cache-loader "^2.0.1"
    case-sensitive-paths-webpack-plugin "^2.2.0"
    chalk "^2.4.2"
    cli-highlight "^2.1.0"
    clipboardy "^2.0.0"
    cliui "^5.0.0"
    copy-webpack-plugin "^4.6.0"
    css-loader "^1.0.1"
    cssnano "^4.1.10"
    current-script-polyfill "^1.0.0"
    debug "^4.1.1"
    default-gateway "^5.0.2"
    dotenv "^7.0.0"
    dotenv-expand "^5.1.0"
    escape-string-regexp "^1.0.5"
    file-loader "^3.0.1"
    fs-extra "^7.0.1"
    globby "^9.2.0"
    hash-sum "^1.0.2"
    html-webpack-plugin "^3.2.0"
    launch-editor-middleware "^2.2.1"
    lodash.defaultsdeep "^4.6.1"
    lodash.mapvalues "^4.6.0"
    lodash.transform "^4.6.0"
    mini-css-extract-plugin "^0.8.0"
    minimist "^1.2.0"
    ora "^3.4.0"
    portfinder "^1.0.20"
    postcss-loader "^3.0.0"
    read-pkg "^5.0.0"
    semver "^6.0.0"
    slash "^2.0.0"
    source-map-url "^0.4.0"
    ssri "^6.0.1"
    string.prototype.padend "^3.0.0"
    terser-webpack-plugin "^1.2.3"
    thread-loader "^2.1.2"
    url-loader "^1.1.2"
    vue-loader "^15.7.0"
    webpack "^4.0.0"
    webpack-bundle-analyzer "^3.3.0"
    webpack-chain "^4.11.0"
    webpack-dev-server "^3.4.1"
    webpack-merge "^4.2.1"

"@vue/cli-shared-utils@^3.12.1":
  version "3.12.1"
  resolved "https://registry.npmjs.org/@vue/cli-shared-utils/-/cli-shared-utils-3.12.1.tgz"
  integrity sha512-jFblzRFjutGwu5utOKdVlPlsbA1lBUNNQlAThzNqej+JtTKJjnvjlhjKX0Gq0oOny5FjKWhoyfQ74p9h1qE6JQ==
  dependencies:
    "@hapi/joi" "^15.0.1"
    chalk "^2.4.1"
    execa "^1.0.0"
    launch-editor "^2.2.1"
    lru-cache "^5.1.1"
    node-ipc "^9.1.1"
    open "^6.3.0"
    ora "^3.4.0"
    request "^2.87.0"
    request-promise-native "^1.0.7"
    semver "^6.0.0"
    string.prototype.padstart "^3.0.0"

"@vue/component-compiler-utils@^3.0.0", "@vue/component-compiler-utils@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@vue/component-compiler-utils/-/component-compiler-utils-3.1.2.tgz"
  integrity sha512-QLq9z8m79mCinpaEeSURhnNCN6djxpHw0lpP/bodMlt5kALfONpryMthvnrQOlTcIKoF+VoPi+lPHUYeDFPXug==
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.14"
    postcss-selector-parser "^6.0.2"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"
  optionalDependencies:
    prettier "^1.18.2"

"@vue/eslint-config-standard@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmjs.org/@vue/eslint-config-standard/-/eslint-config-standard-4.0.0.tgz"
  integrity sha512-bQghq1cw1BuMRHNhr3tRpAJx1tpGy0QtajQX873kLtA9YVuOIoXR7nAWnTN09bBHnSUh2N288vMsqPi2fI4Hzg==
  dependencies:
    eslint-config-standard "^12.0.0"
    eslint-plugin-import "^2.14.0"
    eslint-plugin-node "^8.0.0"
    eslint-plugin-promise "^4.0.1"
    eslint-plugin-standard "^4.0.0"

"@vue/preload-webpack-plugin@^1.1.0":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@vue/preload-webpack-plugin/-/preload-webpack-plugin-1.1.1.tgz"
  integrity sha512-8VCoJeeH8tCkzhkpfOkt+abALQkS11OIHhte5MBzYaKMTqK0A3ZAKEUVAffsOklhEv7t0yrQt696Opnu9oAx+w==

"@vue/web-component-wrapper@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@vue/web-component-wrapper/-/web-component-wrapper-1.2.0.tgz"
  integrity sha512-Xn/+vdm9CjuC9p3Ae+lTClNutrVhsXpzxvoTXXtoys6kVRX9FkueSUAqSWAyZntmVLlR4DosBV4pH8y5Z/HbUw==

"@webassemblyjs/ast@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.9.0.tgz"
  integrity sha512-C6wW5L+b7ogSDVqymbkkvuW9kruN//YisMED04xzeBBqjHa2FYnmvOlS6Xj68xWQRgWvI9cIglsjFowH/RJyEA==
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.9.0.tgz"
  integrity sha512-TG5qcFsS8QB4g4MhrxK5TqfdNe7Ey/7YL/xN+36rRjl/BlGE/NcBvJcqsRgCP6Z92mRE+7N50pRIi8SmKUbcQA==

"@webassemblyjs/helper-api-error@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.0.tgz"
  integrity sha512-NcMLjoFMXpsASZFxJ5h2HZRcEhDkvnNFOAKneP5RbKRzaWJN36NC4jqQHKwStIhGXu5mUWlUUk7ygdtrO8lbmw==

"@webassemblyjs/helper-buffer@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.9.0.tgz"
  integrity sha512-qZol43oqhq6yBPx7YM3m9Bv7WMV9Eevj6kMi6InKOuZxhw+q9hOkvq5e/PpKSiLfyetpaBnogSbNCfBwyB00CA==

"@webassemblyjs/helper-code-frame@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.9.0.tgz"
  integrity sha512-ERCYdJBkD9Vu4vtjUYe8LZruWuNIToYq/ME22igL+2vj2dQ2OOujIZr3MEFvfEaqKoVqpsFKAGsRdBSBjrIvZA==
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz"
  integrity sha512-OPRowhGbshCb5PxJ8LocpdX9Kl0uB4XsAjl6jH/dWKlk/mzsANvhwbiULsaiqT5GZGT9qinTICdj6PLuM5gslw==

"@webassemblyjs/helper-module-context@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-module-context/-/helper-module-context-1.9.0.tgz"
  integrity sha512-MJCW8iGC08tMk2enck1aPW+BE5Cw8/7ph/VGZxwyvGbJwjktKkDK7vy7gAmMDx88D7mhDTCNKAW5tED+gZ0W8g==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.9.0.tgz"
  integrity sha512-R7FStIzyNcd7xKxCZH5lE0Bqy+hGTwS3LJjuv1ZVxd9O7eHCedSdrId/hMOd20I+v8wDXEn+bjfKDLzTepoaUw==

"@webassemblyjs/helper-wasm-section@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.0.tgz"
  integrity sha512-XnMB8l3ek4tvrKUUku+IVaXNHz2YsJyOOmz+MMkZvh8h1uSJpSen6vYnw3IoQ7WwEuAhL8Efjms1ZWjqh2agvw==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.9.0.tgz"
  integrity sha512-dcX8JuYU/gvymzIHc9DgxTzUUTLexWwt8uCTWP3otys596io0L5aW02Gb1RjYpx2+0Jus1h4ZFqjla7umFniTg==
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.9.0.tgz"
  integrity sha512-ENVzM5VwV1ojs9jam6vPys97B/S65YQtv/aanqnU7D8aSoHFX8GyhGg0CMfyKNIHBuAVjy3tlzd5QMMINa7wpw==
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.9.0.tgz"
  integrity sha512-GZbQlWtopBTP0u7cHrEx+73yZKrQoBMpwkGEIqlacljhXCkVM1kMQge/Mf+csMJAjEdSwhOyLAS0AoR3AG5P8w==

"@webassemblyjs/wasm-edit@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz"
  integrity sha512-FgHzBm80uwz5M8WKnMTn6j/sVbqilPdQXTWraSjBwFXSYGirpkSWE2R9Qvz9tNiTKQvoKILpCuTjBKzOIm0nxw==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.0.tgz"
  integrity sha512-cPE3o44YzOOHvlsb4+E9qSqjc9Qf9Na1OO/BHFy4OI91XDE14MjFN4lTMezzaIWdPqHnsTodGGNP+iRSYfGkjA==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.0.tgz"
  integrity sha512-Qkjgm6Anhm+OMbIL0iokO7meajkzQD71ioelnfPEj6r4eOFuqm4YC3VBPqXjFyyNwowzbMD+hizmprP/Fwkl2A==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz"
  integrity sha512-9+wkMowR2AmdSWQzsPEjFU7njh8HTO5MqO8vjwEHuM+AMHioNqSBONRdr0NQQ3dVQrzp0s8lTcYqzUdb7YgELA==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/wast-parser/-/wast-parser-1.9.0.tgz"
  integrity sha512-qsqSAP3QQ3LyZjNC/0jBJ/ToSxfYJ8kYyuiGvtn/8MK89VrNEfwj7BPQzJVHi0jGTRK2dGdJ5PRqhtjzoww+bw==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz"
  integrity sha512-2J0nE95rHXHyQ24cWjMKJ1tqB/ds8z/cyeOZxJhcb+rW+SQASVjuznUSmdz5GpVJTzU8JkhYut0D3siFDD6wsA==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webpack-cli/configtest@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmmirror.com/@webpack-cli/configtest/-/configtest-1.2.0.tgz"
  integrity sha512-4FB8Tj6xyVkyqjj1OaTqCjXYULB9FMkqQ8yGrZjRDrYh0nOE+7Lhs45WioWQQMV+ceFlE368Ukhe6xdvJM9Egg==

"@webpack-cli/info@^1.5.0":
  version "1.5.0"
  resolved "https://registry.npmmirror.com/@webpack-cli/info/-/info-1.5.0.tgz"
  integrity sha512-e8tSXZpw2hPl2uMJY6fsMswaok5FdlGNRTktvFk2sD8RjH0hE2+XistawJx1vmKteh4NmGmNUrp+Tb2w+udPcQ==
  dependencies:
    envinfo "^7.7.3"

"@webpack-cli/serve@^1.7.0":
  version "1.7.0"
  resolved "https://registry.npmmirror.com/@webpack-cli/serve/-/serve-1.7.0.tgz"
  integrity sha512-oxnCNGj88fL+xzV+dacXs44HcDwf1ovs3AuEzvP7mqXw7fQntqIhQ1BRmynh4qEKQSSSRSWVyXRjmTbZIX9V2Q==

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  integrity sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  integrity sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==

abbrev@1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/abbrev/-/abbrev-1.1.1.tgz"
  integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==

abs-svg-path@~0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/abs-svg-path/-/abs-svg-path-0.1.1.tgz"
  integrity sha1-32Acjo0roQ1KdtYl4japo5wnI78=

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz"
  integrity sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-dynamic-import@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/acorn-dynamic-import/-/acorn-dynamic-import-2.0.2.tgz"
  integrity sha512-GKp5tQ8h0KMPWIYGRHHXI1s5tUpZixZ3IHF2jAu42wSCf6In/G873s6/y4DdKdhWvzhu1T6mE1JgvnhAKqyYYQ==
  dependencies:
    acorn "^4.0.3"

acorn-jsx@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-3.0.1.tgz"
  integrity sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s=
  dependencies:
    acorn "^3.0.4"

acorn-jsx@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.2.0.tgz"
  integrity sha512-HiUX/+K2YpkpJ+SzBffkM/AQ2YE03S0U1kjTLVpoJdhZMOWy8qvXVN9JdLqv2QsaQ6MPYQIuNmwD8zOiYUofLQ==

acorn-walk@^6.1.1:
  version "6.2.0"
  resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-6.2.0.tgz"
  integrity sha512-7evsyfH1cLOCdAzZAd43Cic04yKydNx0cF+7tiA19p1XnLLPU4dpCQOqpjqwokFe//vS0QqfqqjCS2JkiIs0cA==

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.2.0.tgz"
  integrity sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA==

acorn@^3.0.4:
  version "3.3.0"
  resolved "https://registry.npmjs.org/acorn/-/acorn-3.3.0.tgz"
  integrity sha1-ReN/s56No/JbruP/U2niu18iAXo=

acorn@^4.0.3:
  version "4.0.13"
  resolved "https://registry.npmmirror.com/acorn/-/acorn-4.0.13.tgz"
  integrity sha512-fu2ygVGuMmlzG8ZeRJ0bvR41nsAkxxhbyk8bZ1SS521Z7vmgJFTQQlfz/Mp/nJexGBz+v8sC9bM6+lNgskt4Ug==

acorn@^5.0.0, acorn@^5.5.0:
  version "5.7.4"
  resolved "https://registry.npmjs.org/acorn/-/acorn-5.7.4.tgz"
  integrity sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg==

"acorn@^6.0.0 || ^7.0.0", acorn@^6.0.2, acorn@^6.0.7:
  version "6.4.1"
  resolved "https://registry.npmjs.org/acorn/-/acorn-6.4.1.tgz"
  integrity sha512-ZVA9k326Nwrj3Cj9jlh3wGFutC2ZornPNARZwsNYqQYgN0EsV2d53w5RN/co65Ohn4sUAUtb1rSUAOD6XN9idA==

acorn@^6.1.1, acorn@^6.4.1:
  version "6.4.1"
  resolved "https://registry.npmjs.org/acorn/-/acorn-6.4.1.tgz"
  integrity sha512-ZVA9k326Nwrj3Cj9jlh3wGFutC2ZornPNARZwsNYqQYgN0EsV2d53w5RN/co65Ohn4sUAUtb1rSUAOD6XN9idA==

acorn@^7.1.1:
  version "7.3.1"
  resolved "https://registry.npmjs.org/acorn/-/acorn-7.3.1.tgz"
  integrity sha512-tLc0wSnatxAQHVHUapaHdz72pi9KUyHjq5KyHjGg9Y8Ifdc79pTh2XvI6I1/chZbnM7QtNKzh66ooDogPZSleA==

add-dom-event-listener@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/add-dom-event-listener/download/add-dom-event-listener-1.1.0.tgz"
  integrity sha1-apLbOg3Qq8JU4JXA8dwUrLuq4xA=
  dependencies:
    object-assign "4.x"

address@^1.0.3:
  version "1.1.2"
  resolved "https://registry.npmjs.org/address/-/address-1.1.2.tgz"
  integrity sha512-aT6camzM4xEA54YVJYSqxz1kv4IHnQZRtThJJHhUMRExaU5spC7jX5ugSwTaTgJliIgs4VhZOk7htClvQ/LmRA==

adjust-sourcemap-loader@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/adjust-sourcemap-loader/-/adjust-sourcemap-loader-1.2.0.tgz"
  integrity sha512-958oaHHVEXMvsY7v7cC5gEkNIcoaAVIhZ4mBReYVZJOTP9IgKmzLjIOhTtzpLMu+qriXvLsVjJ155EeInp45IQ==
  dependencies:
    assert "^1.3.0"
    camelcase "^1.2.1"
    loader-utils "^1.1.0"
    lodash.assign "^4.0.1"
    lodash.defaults "^3.1.2"
    object-path "^0.9.2"
    regex-parser "^2.2.9"

aes-decrypter@1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/aes-decrypter/-/aes-decrypter-1.0.3.tgz"
  integrity sha512-rsx8pfx7wJsn+ziYbpJ8XA5c93hKAtBCrfydxJqJCMT+qfjipd/B5wC2xHtBcoxyvlqJcpeAo3K55t0lXOn9yQ==
  dependencies:
    pkcs7 "^0.2.3"

aggregate-error@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.0.1.tgz"
  integrity sha512-quoaXsZ9/BLNae5yiNoUz+Nhkwz83GhWwtYFglcjEQB2NDHCIpApbqXxIFnm4Pq/Nvhrsq5sYJFyohrrxnTGAA==
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/ajv-errors/-/ajv-errors-1.0.1.tgz"
  integrity sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ==

ajv-keywords@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.1.1.tgz"
  integrity sha1-YXmX/F9gV2iUxDX5QNgZ4TW4B2I=

ajv-keywords@^3.1.0, ajv-keywords@^3.4.1:
  version "3.5.1"
  resolved "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.1.tgz"
  integrity sha512-KWcq3xN8fDjSB+IMoh2VaXVhRI0BBGxoYp3rx7Pkb6z0cFjYR9Q9l4yZqqals0/zsioCmocC5H6UvsGD4MoIBA==

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==

ajv@^5.0.0, ajv@^5.2.3:
  version "5.5.2"
  resolved "https://registry.npmjs.org/ajv/-/ajv-5.5.2.tgz"
  integrity sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ajv@^5.3.0:
  version "5.5.2"
  resolved "https://registry.npmjs.org/ajv/-/ajv-5.5.2.tgz"
  integrity sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ajv@^6.1.0, ajv@^6.10.2, ajv@^6.12.2, ajv@^6.5.5, ajv@^6.9.1, ajv@>=5.0.0:
  version "6.12.3"
  resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.3.tgz"
  integrity sha512-4K0cK3L1hsqk9xIb2z9vs/XU+PGJZ9PNpJRDS9YLzmNdX6jmVPfamLvTJr0aDAusnHyCHO6MjzlkAsgtqp9teA==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz"
  integrity sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

alphanum-sort@^1.0.0, alphanum-sort@^1.0.1, alphanum-sort@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/alphanum-sort/-/alphanum-sort-1.0.2.tgz"
  integrity sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz"
  integrity sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "https://registry.npmjs.org/ansi-colors/-/ansi-colors-3.2.4.tgz"
  integrity sha512-hHUXGagefjN2iRrID63xckIvotOXOojhQKWIPUZ4mNUZ9nLZW+7FMNoE1lOkEhNWYsx/7ysGIuJYCiMAA9FnrA==

ansi-escapes@^3.0.0, ansi-escapes@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.2.0.tgz"
  integrity sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==

ansi-html@0.0.7:
  version "0.0.7"
  resolved "https://registry.npmjs.org/ansi-html/-/ansi-html-0.0.7.tgz"
  integrity sha1-gTWEAhliqenm/QOflA0S9WynhZ4=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.0.tgz"
  integrity sha512-1apePfXM1UOSqw0o9IiFAovVz9M5S1Dg+4TrDwfMewQ6p/rmMueb7tWZjQ1rx4Loy1ArBggoqGpfqqdI4rondg==

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.0.tgz"
  integrity sha512-bY6fj56OUQ0hU1KjFNDQuJFezqKdrAyFdIevADiqrWHwSlbmBNMHp5ak2f40Pm8JTFyM2mqxkG6ngkHO11f/lg==

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.2.1.tgz"
  integrity sha512-9VGjrMsG1vePxcSweQsN20KY/c4zN0h9fLjqAbwbPfahM3t+NL+M9HC8xeXG2I8pX5NoamTGNuomEUFI7fcUjA==
  dependencies:
    "@types/color-name" "^1.1.1"
    color-convert "^2.0.1"

ant-design-vue@^1.7.2, ant-design-vue@>=1.0.0:
  version "1.7.4"
  resolved "https://registry.npm.taobao.org/ant-design-vue/download/ant-design-vue-1.7.4.tgz"
  integrity sha1-hU6QmF3A+pzMbWgcEEVgwA6q+FU=
  dependencies:
    "@ant-design/icons" "^2.1.1"
    "@ant-design/icons-vue" "^2.0.0"
    "@simonwep/pickr" "~1.7.0"
    add-dom-event-listener "^1.0.2"
    array-tree-filter "^2.1.0"
    async-validator "^3.0.3"
    babel-helper-vue-jsx-merge-props "^2.0.3"
    babel-runtime "6.x"
    classnames "^2.2.5"
    component-classes "^1.2.6"
    dom-align "^1.10.4"
    dom-closest "^0.2.0"
    dom-scroll-into-view "^2.0.0"
    enquire.js "^2.1.6"
    intersperse "^1.0.0"
    is-mobile "^2.2.1"
    is-negative-zero "^2.0.0"
    ismobilejs "^1.0.0"
    json2mq "^0.2.0"
    lodash "^4.17.5"
    moment "^2.21.0"
    mutationobserver-shim "^0.3.2"
    node-emoji "^1.10.0"
    omit.js "^1.0.0"
    raf "^3.4.0"
    resize-observer-polyfill "^1.5.1"
    shallow-equal "^1.0.0"
    shallowequal "^1.0.2"
    vue-ref "^2.0.0"
    warning "^4.0.0"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/anymatch/-/anymatch-2.0.0.tgz"
  integrity sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@~3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.1.tgz"
  integrity sha512-mM8522psRCqzV+6LhomX5wgp25YVibjh8Wj23I5RPkPppSVSjyKD2A2mBJmWGa+KN7f2D6LNh9jkBCeyLktzjg==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

aproba@^1.0.3, aproba@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz"
  integrity sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==

arch@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/arch/-/arch-2.1.2.tgz"
  integrity sha512-NTBIIbAfkJeIletyABbVtdPgeKfDafR+1mZV/AyyfC1UkVkp9iUjV+wwmqtUgphHYajbI86jejBJp5e+jkGTiQ==

are-we-there-yet@~1.1.2:
  version "1.1.7"
  resolved "https://registry.npmmirror.com/are-we-there-yet/-/are-we-there-yet-1.1.7.tgz"
  integrity sha512-nxwy40TuMiUGqMyRHgCSWZ9FM4VAoRP4xUYSTv5ImRog+h9yISPbVH7H8fASCIzYn9wlEv4zvFL7uKDMCFQm3g==
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

area-data@^5.0.6, area-data@>=5.0.6:
  version "5.0.6"
  resolved "https://registry.npmjs.org/area-data/-/area-data-5.0.6.tgz"
  integrity sha512-QxLoA+823xXKyhw5S3750I9TToki0OS42HU9ol3rCOsCXfkjtl8RtQ/eoj0cK0Levn4//oEM05FmMumfw/HIlg==

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz"
  integrity sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/array-find-index/-/array-find-index-1.0.2.tgz"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-2.1.2.tgz"
  integrity sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-includes@^3.0.3, array-includes@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.1.tgz"
  integrity sha512-c2VXaCHl7zPsvpkFsw4nxvFie4fh1ur9bpcgsVkIjqn0H/Xwdg+7fv3n2r/isyS8EBj5b06M9kHyZuIr4El6WQ==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0"
    is-string "^1.0.5"

array-tree-filter@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/array-tree-filter/download/array-tree-filter-2.1.0.tgz"
  integrity sha1-hzrAD+yDdJ8lWsjdCDgUtPYykZA=

array-union@^1.0.1, array-union@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.flat@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.2.3.tgz"
  integrity sha512-gBlRZV0VSmfPIeWfuuy56XZMvbVfbEUnOXUvt3F/eUUUSyzlgLxhEX4YAEpxNAogRGehPSnfXyPtYyKAhkzQhQ==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

asap@~2.0.3:
  version "2.0.6"
  resolved "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1.js@^4.0.0:
  version "4.10.1"
  resolved "https://registry.npmjs.org/asn1.js/-/asn1.js-4.10.1.tgz"
  integrity sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw==
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

asn1@~0.2.3:
  version "0.2.4"
  resolved "https://registry.npmjs.org/asn1/-/asn1-0.2.4.tgz"
  integrity sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg==
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@^1.0.0, assert-plus@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1, assert@^1.3.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/assert/-/assert-1.5.0.tgz"
  integrity sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA==
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/astral-regex/-/astral-regex-1.0.0.tgz"
  integrity sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg==

async-each@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmjs.org/async-each/-/async-each-1.0.3.tgz"
  integrity sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ==

async-foreach@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npmmirror.com/async-foreach/-/async-foreach-0.1.3.tgz"
  integrity sha512-VUeSMD8nEGBWaZK4lizI1sf3yEC7pnAQ/mrI7pC2fBz2s/tq5jWWEngTwaf0Gruu/OoXRGLGg1XFqpYBiGTYJA==

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz"
  integrity sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ==

async-validator@^3.0.3:
  version "3.5.1"
  resolved "https://registry.npm.taobao.org/async-validator/download/async-validator-3.5.1.tgz?cache=0&sync_timestamp=1605751734916&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync-validator%2Fdownload%2Fasync-validator-3.5.1.tgz"
  integrity sha1-zWK5aIskZfSEIOJ620d2CrG1VZ8=

async@^2.1.2, async@^2.4.1, async@^2.6.2:
  version "2.6.3"
  resolved "https://registry.npmjs.org/async/-/async-2.6.3.tgz"
  integrity sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg==
  dependencies:
    lodash "^4.17.14"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  integrity sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==

autoprefixer-loader@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/autoprefixer-loader/-/autoprefixer-loader-3.2.0.tgz"
  integrity sha1-Oae2ZGqCaYZQc9lYyX9IYVLCyEo=
  dependencies:
    autoprefixer "^6.0.2"
    loader-utils "^0.2.11"
    postcss "^5.0.4"
    postcss-safe-parser "^1.0.1"

autoprefixer@^6.0.2, autoprefixer@^6.3.1:
  version "6.7.7"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.7.tgz"
  integrity sha1-Hb0cg1ZY41zj+ZhAmdsAWFx4IBQ=
  dependencies:
    browserslist "^1.7.6"
    caniuse-db "^1.0.30000634"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^5.2.16"
    postcss-value-parser "^3.2.3"

autoprefixer@^9.5.1:
  version "9.8.4"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.4.tgz"
  integrity sha512-84aYfXlpUe45lvmS+HoAWKCkirI/sw4JK0/bTeeqgHYco3dcsOn0NqdejISjptsYwNji/21dnkDri9PsYKk89A==
  dependencies:
    browserslist "^4.12.0"
    caniuse-lite "^1.0.30001087"
    colorette "^1.2.0"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^7.0.32"
    postcss-value-parser "^4.1.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.10.0"
  resolved "https://registry.npmjs.org/aws4/-/aws4-1.10.0.tgz"
  integrity sha512-3YDiu347mtVtjpyV3u5kVqQLP242c06zwDOgpeRnybmXlYYsLbtTrUBUm8i8srONt+FWobl5aibnU1030PeeuA==

axios@^0.18.0:
  version "0.18.1"
  resolved "https://registry.npmjs.org/axios/-/axios-0.18.1.tgz"
  integrity sha512-0BfJq4NSfQXd+SkFdrvFbG7addhYSBA2mQwISr46pD6E5iqkWg02RAs8vyTT/j0RTnoYmeXauBuSv1qKwR179g==
  dependencies:
    follow-redirects "1.5.10"
    is-buffer "^2.0.2"

babel-code-frame@^6.22.0, babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-code-frame/-/babel-code-frame-6.26.0.tgz"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-core@^6.26.0, babel-core@6:
  version "6.26.3"
  resolved "https://registry.npmjs.org/babel-core/-/babel-core-6.26.3.tgz"
  integrity sha512-6jyFLuDmeidKmUEb3NM+/yawG0M2bDZ9Z1qbZP59cyHLz8kYGKYwpJP0UwUKKUiTRNvxfLesJnTedqczP7cTDA==
  dependencies:
    babel-code-frame "^6.26.0"
    babel-generator "^6.26.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.26.0"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    convert-source-map "^1.5.1"
    debug "^2.6.9"
    json5 "^0.5.1"
    lodash "^4.17.4"
    minimatch "^3.0.4"
    path-is-absolute "^1.0.1"
    private "^0.1.8"
    slash "^1.0.0"
    source-map "^0.5.7"

babel-eslint@^10.0.1:
  version "10.1.0"
  resolved "https://registry.npmjs.org/babel-eslint/-/babel-eslint-10.1.0.tgz"
  integrity sha512-ifWaTHQ0ce+448CYop8AdrQiBsGrnC+bMgfyKFdi6EsPLTAWG+QfyDeM6OH+FmWnKvEq5NnBMLvlBUPKQZoDSg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

babel-eslint@7.2.3:
  version "7.2.3"
  resolved "https://registry.npmjs.org/babel-eslint/-/babel-eslint-7.2.3.tgz"
  integrity sha1-sv4tgBJkcPXBlELcdXJTqJdxCCc=
  dependencies:
    babel-code-frame "^6.22.0"
    babel-traverse "^6.23.1"
    babel-types "^6.23.0"
    babylon "^6.17.0"

babel-generator@^6.26.0:
  version "6.26.1"
  resolved "https://registry.npmjs.org/babel-generator/-/babel-generator-6.26.1.tgz"
  integrity sha512-HyfwY6ApZj7BYTcJURpM5tznulaBvyio7/0d4zFOeMPUmfxkCjHocCuoLa2SAGzBI8AREcH3eP3758F672DppA==
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.17.4"
    source-map "^0.5.7"
    trim-right "^1.0.1"

babel-helper-builder-binary-assignment-operator-visitor@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz"
  integrity sha1-zORReto1b0IgvK6KAsKzRvmlZmQ=
  dependencies:
    babel-helper-explode-assignable-expression "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-call-delegate@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helper-call-delegate/-/babel-helper-call-delegate-6.24.1.tgz"
  integrity sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340=
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-define-map@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-helper-define-map/-/babel-helper-define-map-6.26.0.tgz"
  integrity sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-explode-assignable-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.24.1.tgz"
  integrity sha1-8luCz33BBDPFX3BZLVdGQArCLKo=
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helper-function-name/-/babel-helper-function-name-6.24.1.tgz"
  integrity sha1-00dbjAPtmCQqJbSDUasYOZ01gKk=
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.24.1.tgz"
  integrity sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-hoist-variables@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helper-hoist-variables/-/babel-helper-hoist-variables-6.24.1.tgz"
  integrity sha1-HssnaJydJVE+rbyZFKc/VAi+enY=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helper-optimise-call-expression/-/babel-helper-optimise-call-expression-6.24.1.tgz"
  integrity sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-regex@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-helper-regex/-/babel-helper-regex-6.26.0.tgz"
  integrity sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-remap-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helper-remap-async-to-generator/-/babel-helper-remap-async-to-generator-6.24.1.tgz"
  integrity sha1-XsWBgnrXI/7N04HxySg5BnbkVRs=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helper-replace-supers/-/babel-helper-replace-supers-6.24.1.tgz"
  integrity sha1-v22/5Dk40XNpohPKiov3S2qQqxo=
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-vue-jsx-merge-props@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  integrity sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY=

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-helpers/-/babel-helpers-6.24.1.tgz"
  integrity sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-loader@^7.1.2:
  version "7.1.5"
  resolved "https://registry.npmjs.org/babel-loader/-/babel-loader-7.1.5.tgz"
  integrity sha512-iCHfbieL5d1LfOQeeVJEUyD9rTwBcP/fcEbRCfempxTDuqrKpu0AZjLAQHEQa3Yqyj9ORKe2iHfoj4rHLf7xpw==
  dependencies:
    find-cache-dir "^1.0.0"
    loader-utils "^1.0.2"
    mkdirp "^0.5.1"

babel-loader@^8.0.5:
  version "8.1.0"
  resolved "https://registry.npmjs.org/babel-loader/-/babel-loader-8.1.0.tgz"
  integrity sha512-7q7nC1tYOrqvUrN3LQK4GwSk/TQorZSOlO9C+RZDZpODgyN4ZlCqE5q9cDsyWOliN+aU9B4JX01xK9eJXowJLw==
  dependencies:
    find-cache-dir "^2.1.0"
    loader-utils "^1.4.0"
    mkdirp "^0.5.3"
    pify "^4.0.1"
    schema-utils "^2.6.5"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmjs.org/babel-messages/-/babel-messages-6.23.0.tgz"
  integrity sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-check-es2015-constants@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmjs.org/babel-plugin-check-es2015-constants/-/babel-plugin-check-es2015-constants-6.22.0.tgz"
  integrity sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-dynamic-import-node@^2.2.0, babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  integrity sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ==
  dependencies:
    object.assign "^4.1.0"

babel-plugin-module-resolver@3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/babel-plugin-module-resolver/-/babel-plugin-module-resolver-3.2.0.tgz"
  integrity sha512-tjR0GvSndzPew/Iayf4uICWZqjBwnlMWjSx6brryfQ81F9rxBVqwDJtFCV8oOs0+vJeefK9TmdZtkIFdFe1UnA==
  dependencies:
    find-babel-config "^1.1.0"
    glob "^7.1.2"
    pkg-up "^2.0.0"
    reselect "^3.0.1"
    resolve "^1.4.0"

babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npmjs.org/babel-plugin-syntax-async-functions/-/babel-plugin-syntax-async-functions-6.13.0.tgz"
  integrity sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU=

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npmjs.org/babel-plugin-syntax-exponentiation-operator/-/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz"
  integrity sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4=

babel-plugin-syntax-trailing-function-commas@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmjs.org/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz"
  integrity sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM=

babel-plugin-transform-async-to-generator@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-async-to-generator/-/babel-plugin-transform-async-to-generator-6.24.1.tgz"
  integrity sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E=
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-arrow-functions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-arrow-functions/-/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz"
  integrity sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoped-functions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoped-functions/-/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz"
  integrity sha1-u8UbSflk1wy42OC5ToICRs46YUE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoping@^6.23.0:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoping/-/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz"
  integrity sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8=
  dependencies:
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-plugin-transform-es2015-classes@^6.23.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.24.1.tgz"
  integrity sha1-WkxYpQyclGHlZLSyo7+ryXolhNs=
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-computed-properties@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-computed-properties/-/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz"
  integrity sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM=
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-destructuring@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.23.0.tgz"
  integrity sha1-mXux8auWf2gtKwh2/jWNYOdlxW0=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-duplicate-keys@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-duplicate-keys/-/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz"
  integrity sha1-c+s9MQypaePvnskcU3QabxV2Qj4=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-for-of@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-for-of/-/babel-plugin-transform-es2015-for-of-6.23.0.tgz"
  integrity sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-function-name/-/babel-plugin-transform-es2015-function-name-6.24.1.tgz"
  integrity sha1-g0yJhTvDaxrw86TF26qU/Y6sqos=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-literals@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-literals/-/babel-plugin-transform-es2015-literals-6.22.0.tgz"
  integrity sha1-T1SgLWzWbPkVKAAZox0xklN3yi4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-modules-amd@^6.22.0, babel-plugin-transform-es2015-modules-amd@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz"
  integrity sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ=
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-commonjs@^6.23.0, babel-plugin-transform-es2015-modules-commonjs@^6.24.1:
  version "6.26.2"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz"
  integrity sha512-CV9ROOHEdrjcwhIaJNBGMBCodN+1cfkwtM1SbUHmvyy35KGT7fohbpOxkE2uLz1o6odKK2Ck/tz47z+VqQfi9Q==
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-types "^6.26.0"

babel-plugin-transform-es2015-modules-systemjs@^6.23.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz"
  integrity sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM=
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-umd@^6.23.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz"
  integrity sha1-rJl+YoXNGO1hdq22B9YCNErThGg=
  dependencies:
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-object-super@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-object-super/-/babel-plugin-transform-es2015-object-super-6.24.1.tgz"
  integrity sha1-JM72muIcuDp/hgPa0CH1cusnj40=
  dependencies:
    babel-helper-replace-supers "^6.24.1"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-parameters@^6.23.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.24.1.tgz"
  integrity sha1-V6w1GrScrxSpfNE7CfZv3wpiXys=
  dependencies:
    babel-helper-call-delegate "^6.24.1"
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-shorthand-properties@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-shorthand-properties/-/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz"
  integrity sha1-JPh11nIch2YbvZmkYi5R8U3jiqA=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-spread@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-spread/-/babel-plugin-transform-es2015-spread-6.22.0.tgz"
  integrity sha1-1taKmfia7cRTbIGlQujdnxdG+NE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-sticky-regex/-/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz"
  integrity sha1-AMHNsaynERLN8M9hJsLta0V8zbw=
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-template-literals@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-template-literals/-/babel-plugin-transform-es2015-template-literals-6.22.0.tgz"
  integrity sha1-qEs0UPfp+PH2g51taH2oS7EjbY0=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-typeof-symbol@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-typeof-symbol/-/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz"
  integrity sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-es2015-unicode-regex/-/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz"
  integrity sha1-04sS9C6nMj9yk4fxinxa4frrNek=
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-exponentiation-operator@^6.22.0:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.24.1.tgz"
  integrity sha1-KrDJx/MJj6SJB3cruBP+QejeOg4=
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.24.1"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-regenerator@^6.22.0:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-regenerator/-/babel-plugin-transform-regenerator-6.26.0.tgz"
  integrity sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8=
  dependencies:
    regenerator-transform "^0.10.0"

babel-plugin-transform-runtime@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npmjs.org/babel-plugin-transform-runtime/-/babel-plugin-transform-runtime-6.23.0.tgz"
  integrity sha1-iEkNRGUC6puOfvsP4J7E2ZR5se4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-strict-mode@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npmjs.org/babel-plugin-transform-strict-mode/-/babel-plugin-transform-strict-mode-6.24.1.tgz"
  integrity sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-preset-env@^1.6.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/babel-preset-env/-/babel-preset-env-1.7.0.tgz"
  integrity sha512-9OR2afuKDneX2/q2EurSftUYM0xGu4O2D9adAhVfADDhrYDaxXV0rBbevVYoY9n6nyX1PmQW/0jtpJvUNr9CHg==
  dependencies:
    babel-plugin-check-es2015-constants "^6.22.0"
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-to-generator "^6.22.0"
    babel-plugin-transform-es2015-arrow-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoping "^6.23.0"
    babel-plugin-transform-es2015-classes "^6.23.0"
    babel-plugin-transform-es2015-computed-properties "^6.22.0"
    babel-plugin-transform-es2015-destructuring "^6.23.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.22.0"
    babel-plugin-transform-es2015-for-of "^6.23.0"
    babel-plugin-transform-es2015-function-name "^6.22.0"
    babel-plugin-transform-es2015-literals "^6.22.0"
    babel-plugin-transform-es2015-modules-amd "^6.22.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.23.0"
    babel-plugin-transform-es2015-modules-systemjs "^6.23.0"
    babel-plugin-transform-es2015-modules-umd "^6.23.0"
    babel-plugin-transform-es2015-object-super "^6.22.0"
    babel-plugin-transform-es2015-parameters "^6.23.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.22.0"
    babel-plugin-transform-es2015-spread "^6.22.0"
    babel-plugin-transform-es2015-sticky-regex "^6.22.0"
    babel-plugin-transform-es2015-template-literals "^6.22.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.23.0"
    babel-plugin-transform-es2015-unicode-regex "^6.22.0"
    babel-plugin-transform-exponentiation-operator "^6.22.0"
    babel-plugin-transform-regenerator "^6.22.0"
    browserslist "^3.2.6"
    invariant "^2.2.2"
    semver "^5.3.0"

babel-register@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-register/-/babel-register-6.26.0.tgz"
  integrity sha1-btAhFz4vy0htestFxgCahW9kcHE=
  dependencies:
    babel-core "^6.26.0"
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    home-or-tmp "^2.0.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    source-map-support "^0.4.15"

babel-runtime@^6.18.0, babel-runtime@^6.22.0, babel-runtime@^6.23.0, babel-runtime@^6.26.0, babel-runtime@^6.9.2, babel-runtime@6.x:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.24.1, babel-template@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-template/-/babel-template-6.26.0.tgz"
  integrity sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.23.1, babel-traverse@^6.24.1, babel-traverse@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-traverse/-/babel-traverse-6.26.0.tgz"
  integrity sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.19.0, babel-types@^6.23.0, babel-types@^6.24.1, babel-types@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npmjs.org/babel-types/-/babel-types-6.26.0.tgz"
  integrity sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@^6.17.0, babylon@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npmjs.org/babylon/-/babylon-6.18.0.tgz"
  integrity sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ==

balanced-match@^0.4.2:
  version "0.4.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.2.tgz"
  integrity sha1-yz8+PHMtwPAe5wtAPzAuYddwmDg=

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.npmjs.org/base/-/base-0.11.2.tgz"
  integrity sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

base64-arraybuffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  integrity sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==

base64-js@^1.0.2:
  version "1.3.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.3.1.tgz"
  integrity sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g==

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bfj@^6.1.1:
  version "6.1.2"
  resolved "https://registry.npmjs.org/bfj/-/bfj-6.1.2.tgz"
  integrity sha512-BmBJa4Lip6BPRINSZ0BPEIfB1wUY/9rwbwvIHQA1KjX9om29B6id0wnWXq7m3bn5JrUVjeOTnVuhPT1FiHwPGw==
  dependencies:
    bluebird "^3.5.5"
    check-types "^8.0.3"
    hoopy "^0.1.4"
    tryer "^1.0.1"

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://registry.npmjs.org/big.js/-/big.js-3.2.0.tgz"
  integrity sha512-+hN/Zh2D08Mx65pZ/4g5bsmNiZUuChDiQfTUQ7qJr4/kuopCr88xZsAXv6mBoZEsUI4OuGHlX59qE94K2mMW8Q==

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.1.tgz"
  integrity sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==

binary-extensions@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.1.0.tgz"
  integrity sha512-1Yj8h9Q+QDF5FzhMs/c9+6UntbD5MkRfRwac8DoEm9ZfUBZ7tZ55YcGVAzEe4bXsdQHEk+s9S5wsOKVdZrw0tQ==

bindings@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/bindings/-/bindings-1.5.0.tgz"
  integrity sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==
  dependencies:
    file-uri-to-path "1.0.0"

block-stream@*:
  version "0.0.9"
  resolved "https://registry.npmmirror.com/block-stream/-/block-stream-0.0.9.tgz"
  integrity sha512-OorbnJVPII4DuUKbjARAe8u8EfqOmkEEaSFIyoQ7OjTHn6kafxWl0wLgoZ2rXaYd7MyLcDaU4TmhfxtwgcccMQ==
  dependencies:
    inherits "~2.0.0"

bluebird@^3.1.1, bluebird@^3.5.1, bluebird@^3.5.5:
  version "3.7.2"
  resolved "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz"
  integrity sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==

bn.js@^4.0.0:
  version "4.11.9"
  resolved "https://registry.npmjs.org/bn.js/-/bn.js-4.11.9.tgz"
  integrity sha512-E6QoYqCKZfgatHTdHzs1RRKP7ip4vvm+EyRUeE2RF0NblwVvb0p6jSVeNTOFxPn26QXN2o6SMfNxKp6kU8zQaw==

bn.js@^4.1.0:
  version "4.11.9"
  resolved "https://registry.npmjs.org/bn.js/-/bn.js-4.11.9.tgz"
  integrity sha512-E6QoYqCKZfgatHTdHzs1RRKP7ip4vvm+EyRUeE2RF0NblwVvb0p6jSVeNTOFxPn26QXN2o6SMfNxKp6kU8zQaw==

bn.js@^4.4.0:
  version "4.11.9"
  resolved "https://registry.npmjs.org/bn.js/-/bn.js-4.11.9.tgz"
  integrity sha512-E6QoYqCKZfgatHTdHzs1RRKP7ip4vvm+EyRUeE2RF0NblwVvb0p6jSVeNTOFxPn26QXN2o6SMfNxKp6kU8zQaw==

bn.js@^5.1.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/bn.js/-/bn.js-5.1.2.tgz"
  integrity sha512-40rZaf3bUNKTVYu9sIeeEGOg7g14Yvnj9kH7b50EiwX0Q7A6umbvfI5tvHaOERH0XigqKkfLkFQxzb4e6CIXnA==

body-parser@1.19.0:
  version "1.19.0"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.19.0.tgz"
  integrity sha512-dhEPs72UPbDnAQJ9ZKMNTP6ptJaionhP5cBb541nXPlW60Jepo9RV/a4fX4XWW9CuFNK22krhrj1+rgzifNCsw==
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://registry.npmjs.org/bonjour/-/bonjour-3.5.0.tgz"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^2.3.1:
  version "2.3.2"
  resolved "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz"
  integrity sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/braces/-/braces-2.3.2.tgz"
  integrity sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

brorand@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "https://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz"
  integrity sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/browserify-cipher/-/browserify-cipher-1.0.1.tgz"
  integrity sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/browserify-des/-/browserify-des-1.0.2.tgz"
  integrity sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/browserify-rsa/-/browserify-rsa-4.0.1.tgz"
  integrity sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=
  dependencies:
    bn.js "^4.1.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/browserify-sign/-/browserify-sign-4.2.0.tgz"
  integrity sha512-hEZC1KEeYuoHRqhGhTy6gWrpJA3ZDjFWv0DE61643ZnOXAKJb3u7yWcrU0mMc9SwAqK1n7myPGndkp0dFG7NFA==
  dependencies:
    bn.js "^5.1.1"
    browserify-rsa "^4.0.1"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.2"
    inherits "^2.0.4"
    parse-asn1 "^5.1.5"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  integrity sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==
  dependencies:
    pako "~1.0.5"

browserslist@^1.3.6, browserslist@^1.5.2, browserslist@^1.7.6:
  version "1.7.7"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-1.7.7.tgz"
  integrity sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk=
  dependencies:
    caniuse-db "^1.0.30000639"
    electron-to-chromium "^1.2.7"

browserslist@^3.2.6:
  version "3.2.8"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-3.2.8.tgz"
  integrity sha512-WHVocJYavUwVgVViC0ORikPHQquXwVh939TaelZ4WDqpWgTX/FsGhl/+P4qBUAGcRvtOgDgC+xftNWWp2RUTAQ==
  dependencies:
    caniuse-lite "^1.0.30000844"
    electron-to-chromium "^1.3.47"

browserslist@^4.0.0:
  version "4.13.0"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.13.0.tgz"
  integrity sha512-MINatJ5ZNrLnQ6blGvePd/QOz9Xtu+Ne+x29iQSCHfkU5BugKVJwZKn/iiL8UbpIpa3JhviKjz+XxMo0m2caFQ==
  dependencies:
    caniuse-lite "^1.0.30001093"
    electron-to-chromium "^1.3.488"
    escalade "^3.0.1"
    node-releases "^1.1.58"

browserslist@^4.12.0, browserslist@^4.5.4:
  version "4.13.0"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.13.0.tgz"
  integrity sha512-MINatJ5ZNrLnQ6blGvePd/QOz9Xtu+Ne+x29iQSCHfkU5BugKVJwZKn/iiL8UbpIpa3JhviKjz+XxMo0m2caFQ==
  dependencies:
    caniuse-lite "^1.0.30001093"
    electron-to-chromium "^1.3.488"
    escalade "^3.0.1"
    node-releases "^1.1.58"

browserslist@^4.3.4:
  version "4.13.0"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.13.0.tgz"
  integrity sha512-MINatJ5ZNrLnQ6blGvePd/QOz9Xtu+Ne+x29iQSCHfkU5BugKVJwZKn/iiL8UbpIpa3JhviKjz+XxMo0m2caFQ==
  dependencies:
    caniuse-lite "^1.0.30001093"
    electron-to-chromium "^1.3.488"
    escalade "^3.0.1"
    node-releases "^1.1.58"

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz"
  integrity sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/buffer-indexof/-/buffer-indexof-1.1.1.tgz"
  integrity sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g==

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/buffer-xor/-/buffer-xor-1.0.3.tgz"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.2"
  resolved "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.0.tgz"
  integrity sha512-zauLjrfCG+xvoyaqLoV8bLVXXNGC4JqlxFCutSDWA6fJrTo2ZuvLYTqZ7aHBLZSMOopbzwv8f+wZcVzfVTI2Dg==

cacache@^10.0.4:
  version "10.0.4"
  resolved "https://registry.npmjs.org/cacache/-/cacache-10.0.4.tgz"
  integrity sha512-Dph0MzuH+rTQzGPNT9fAnrPmMmjKfST6trxJeK7NQuHRaVw24VzPRWTmg9MpcwOVQZO0E1FBICUlFeNaKPIfHA==
  dependencies:
    bluebird "^3.5.1"
    chownr "^1.0.1"
    glob "^7.1.2"
    graceful-fs "^4.1.11"
    lru-cache "^4.1.1"
    mississippi "^2.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.2"
    ssri "^5.2.4"
    unique-filename "^1.1.0"
    y18n "^4.0.0"

cacache@^12.0.2:
  version "12.0.4"
  resolved "https://registry.npmjs.org/cacache/-/cacache-12.0.4.tgz"
  integrity sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ==
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cacache@^13.0.1:
  version "13.0.1"
  resolved "https://registry.npmjs.org/cacache/-/cacache-13.0.1.tgz"
  integrity sha512-5ZvAxd05HDDU+y9BVvcqYu2LLXmPnQ0hW62h32g4xBTgL/MppR4/04NHfj/ycM2y6lmTnbw6HVi+1eN0Psba6w==
  dependencies:
    chownr "^1.1.2"
    figgy-pudding "^3.5.1"
    fs-minipass "^2.0.0"
    glob "^7.1.4"
    graceful-fs "^4.2.2"
    infer-owner "^1.0.4"
    lru-cache "^5.1.1"
    minipass "^3.0.0"
    minipass-collect "^1.0.2"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.2"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    p-map "^3.0.0"
    promise-inflight "^1.0.1"
    rimraf "^2.7.1"
    ssri "^7.0.0"
    unique-filename "^1.1.1"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz"
  integrity sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-loader@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/cache-loader/-/cache-loader-2.0.1.tgz"
  integrity sha512-V99T3FOynmGx26Zom+JrVBytLBsmUCzVG2/4NnUKgvXN4bEV42R1ERl1IyiH/cvFIDA1Ytq2lPZ9tXDSahcQpQ==
  dependencies:
    loader-utils "^1.1.0"
    mkdirp "^0.5.1"
    neo-async "^2.6.0"
    normalize-path "^3.0.0"
    schema-utils "^1.0.0"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/call-me-maybe/-/call-me-maybe-1.0.1.tgz"
  integrity sha1-JtII6onje1y95gJQoV8DHBak1ms=

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/caller-callsite/-/caller-callsite-2.0.0.tgz"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/caller-path/-/caller-path-0.1.0.tgz"
  integrity sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=
  dependencies:
    callsites "^0.2.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-0.2.0.tgz"
  integrity sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camel-case@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/camel-case/-/camel-case-4.1.1.tgz"
  integrity sha512-7fa2WcG4fYFkclIvEmxBbTvmibwF2/agfEBc6q3lOpVu0A13ltLsA+Hr/8Hp6kp5f+G7hKi6t8lys6XxP+1K6Q==
  dependencies:
    pascal-case "^3.1.1"
    tslib "^1.10.0"

camel-case@3.0.x:
  version "3.0.0"
  resolved "https://registry.npmjs.org/camel-case/-/camel-case-3.0.0.tgz"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-2.1.0.tgz"
  integrity sha1-MIvur/3ygRkFHvodkyITyRuPkuc=
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^1.0.2, camelcase@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz"
  integrity sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=

camelcase@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-2.1.1.tgz"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-3.0.0.tgz"
  integrity sha1-MvxLn82vhF/N9+c7uXysImHwqwo=

camelcase@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-4.1.0.tgz"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

camelcase@^5.0.0:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==

caniuse-api@^1.5.2:
  version "1.6.1"
  resolved "https://registry.npmjs.org/caniuse-api/-/caniuse-api-1.6.1.tgz"
  integrity sha1-tTTnxzTE+B7F++isoq0kNUuWLGw=
  dependencies:
    browserslist "^1.3.6"
    caniuse-db "^1.0.30000529"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz"
  integrity sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-db@^1.0.30000529, caniuse-db@^1.0.30000634, caniuse-db@^1.0.30000639:
  version "1.0.30001097"
  resolved "https://registry.npmjs.org/caniuse-db/-/caniuse-db-1.0.30001097.tgz"
  integrity sha512-6zCYv0bxVP6ppQ/XpZsTQZNDC2zUutl8Yes2TDj7LyIDyBhq2N9JbQjcqQo6mAe8IVfszW5pmE0yHvAYD0zEmQ==

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30000844, caniuse-lite@^1.0.30001087, caniuse-lite@^1.0.30001093:
  version "1.0.30001097"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001097.tgz"
  integrity sha512-TeuSleKt/vWXaPkLVFqGDnbweYfq4IaZ6rUugFf3rWY6dlII8StUZ8Ddin0PkADfgYZ4wRqCdO2ORl4Rn5eZIA==

case-sensitive-paths-webpack-plugin@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.3.0.tgz"
  integrity sha512-/4YgnZS8y1UXXmC02xD5rRrBEu6T5ub+mQHLNRj0fzTRbgdBYhsNo2V5EqwgqrExjxsjtF/OpAKAMkKsxbD5XQ==

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

center-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz"
  integrity sha1-qg0yYptu6XIgBBHL1EYckHvCt60=
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^2.0.1:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^2.1.0, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^2.3.0:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz"
  integrity sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.4.0:
  version "0.4.2"
  resolved "https://registry.npmjs.org/chardet/-/chardet-0.4.2.tgz"
  integrity sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I=

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz"
  integrity sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==

charenc@~0.0.1:
  version "0.0.2"
  resolved "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz"
  integrity sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc=

check-types@^8.0.3:
  version "8.0.3"
  resolved "https://registry.npmjs.org/check-types/-/check-types-8.0.3.tgz"
  integrity sha512-YpeKZngUmG65rLudJ4taU7VLkOCTMhNl/u4ctNC56LQS/zJTyNH0Lrtwm1tfTsbLlwvlfsA2d1c8vCf/Kh2KwQ==

chokidar@^2.1.2:
  version "2.1.8"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-2.1.8.tgz"
  integrity sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^2.1.8:
  version "2.1.8"
  resolved "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.8.tgz"
  integrity sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.4.0.tgz"
  integrity sha512-aXAaho2VJtisB/1fg1+3nlLJqGOuewTzQpd/Tz0yTg2R0e4IGtshYvtjowyEumcBv2z+y4+kc75Mz7j5xJskcQ==
  dependencies:
    anymatch "~3.1.1"
    braces "~3.0.2"
    glob-parent "~5.1.0"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.4.0"
  optionalDependencies:
    fsevents "~2.1.2"

chokidar@^3.4.1:
  version "3.5.3"
  resolved "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^1.0.1, chownr@^1.1.1, chownr@^1.1.2:
  version "1.1.4"
  resolved "https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz"
  integrity sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==

chrome-trace-event@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.2.tgz"
  integrity sha512-9e/zx1jw7B4CO+c/RXoCsfg/x1AfUBioy4owYH0bJprEYAx5hRFLRhWBqHAG57D0ZM4H7vxbP7bPe0VwhQRYDQ==
  dependencies:
    tslib "^1.9.0"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/ci-info/-/ci-info-1.6.0.tgz"
  integrity sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A==

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz"
  integrity sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q==
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

circular-json@^0.3.1:
  version "0.3.3"
  resolved "https://registry.npmjs.org/circular-json/-/circular-json-0.3.3.tgz"
  integrity sha512-UZK3NBx2Mca+b5LsG7bY183pHWt5Y1xts4P3Pz7ENTwGVnJOUWbRb3ocjvX7hx9tq/yTAdclXm9sZ38gNuem4A==

clap@^1.0.9:
  version "1.2.3"
  resolved "https://registry.npmjs.org/clap/-/clap-1.2.3.tgz"
  integrity sha512-4CoL/A3hf90V3VIEjeuhSvlGFEHKzOz+Wfc2IVZc+FaUgU0ZQafJTP49fvnULipOPcAfqhyI2duwQyns6xqjYA==
  dependencies:
    chalk "^1.1.3"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz"
  integrity sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

classnames@^2.2.5:
  version "2.2.6"
  resolved "https://registry.npm.taobao.org/classnames/download/classnames-2.2.6.tgz"
  integrity sha1-Q5Nb/90pHzJtrQogUwmzjQD2UM4=

clean-css@^4.2.3, clean-css@4.2.x:
  version "4.2.3"
  resolved "https://registry.npmjs.org/clean-css/-/clean-css-4.2.3.tgz"
  integrity sha512-VcMWDN54ZN/DS+g58HYL5/n4Zrqe8vHJpGA8KdgUXFU4fuP/aHNw8eld9SyEIyabIMJX/0RaY/fplOo5hYLSFA==
  dependencies:
    source-map "~0.6.0"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
  integrity sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-highlight@^2.1.0:
  version "2.1.4"
  resolved "https://registry.npmjs.org/cli-highlight/-/cli-highlight-2.1.4.tgz"
  integrity sha512-s7Zofobm20qriqDoU9sXptQx0t2R9PEgac92mENNm7xaEe1hn71IIMsXMK+6encA6WRCWWxIGQbipr3q998tlQ==
  dependencies:
    chalk "^3.0.0"
    highlight.js "^9.6.0"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^5.1.1"
    yargs "^15.0.0"

cli-spinners@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.3.0.tgz"
  integrity sha512-Xs2Hf2nzrvJMFKimOR7YR0QwZ8fc0u98kdtwN1eNAZzNQgH3vK2pXzff6GJtKh7S5hoJ87ECiAiZFS2fb5Ii2w==

cli-width@^2.0.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/cli-width/-/cli-width-2.2.1.tgz"
  integrity sha512-GRMWDxpOB6Dgk2E5Uo+3eEBvtOOlimMmpbFiKuLFnQzYDavtLFY3K5ona41jgN/WdRZtG7utuVSVTL4HbZHGkw==

clipboard@^2.0.4:
  version "2.0.6"
  resolved "https://registry.npmjs.org/clipboard/-/clipboard-2.0.6.tgz"
  integrity sha512-g5zbiixBRk/wyKakSwCKd7vQXDjFnAMGHoEyBogG/bw9kTD9GvdAvaoRR1ALcEzt3pVKxZR0pViekPMIS0QyGg==
  dependencies:
    good-listener "^1.2.2"
    select "^1.1.2"
    tiny-emitter "^2.0.0"

clipboardy@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/clipboardy/-/clipboardy-2.3.0.tgz"
  integrity sha512-mKhiIL2DrQIsuXMgBgnfEHOZOryC7kY7YO//TN6c63wlEm3NG5tz+YgY5rVi29KCmq/QQjKYvM7a19+MDOTHOQ==
  dependencies:
    arch "^2.1.1"
    execa "^1.0.0"
    is-wsl "^2.1.1"

cliui@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz"
  integrity sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/cliui/-/cliui-3.2.0.tgz"
  integrity sha512-0yayqDxWQbqk3ojkYqUKqaAQ6AfNKeKWRNA8kR0WXzAsdHpP4BIaOmMAG87JGuO6qcobyW4GjxHd9PmhEd+T9w==
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/cliui/-/cliui-5.0.0.tgz"
  integrity sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA==
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz"
  integrity sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/clone-deep/-/clone-deep-4.0.1.tgz"
  integrity sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clone@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

clone@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

coa@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/coa/-/coa-2.0.2.tgz"
  integrity sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA==
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

coa@~1.0.1:
  version "1.0.4"
  resolved "https://registry.npmjs.org/coa/-/coa-1.0.4.tgz"
  integrity sha1-qe8VNmDWqGqL3sAomlxoTSF0Mv0=
  dependencies:
    q "^1.1.2"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

codemirror@^5.46.0, codemirror@^5.48.4:
  version "5.55.0"
  resolved "https://registry.npmjs.org/codemirror/-/codemirror-5.55.0.tgz"
  integrity sha512-TumikSANlwiGkdF/Blnu/rqovZ0Y3Jh8yy9TqrPbSM0xxSucq3RgnpVDQ+mD9q6JERJEIT2FMuF/fBGfkhIR/g==

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.3.0, color-convert@^1.9.0, color-convert@^1.9.1:
  version "1.9.3"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-string@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/color-string/-/color-string-0.3.0.tgz"
  integrity sha1-J9RvtnAlxcL6JZk7+/V55HhBuZE=
  dependencies:
    color-name "^1.0.0"

color-string@^1.5.2:
  version "1.5.3"
  resolved "https://registry.npmjs.org/color-string/-/color-string-1.5.3.tgz"
  integrity sha512-dC2C5qeWoYkxki5UAXapdjqO672AM4vZuPGRQfO8b5HKuKGBbKWpITyDYN7TOFKvRW7kOgAn3746clDBMDJyQw==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^0.11.0:
  version "0.11.4"
  resolved "https://registry.npmjs.org/color/-/color-0.11.4.tgz"
  integrity sha1-bXtcdPtl6EHNSHkq0e1eB7kE12Q=
  dependencies:
    clone "^1.0.2"
    color-convert "^1.3.0"
    color-string "^0.3.0"

color@^3.0.0:
  version "3.1.2"
  resolved "https://registry.npmjs.org/color/-/color-3.1.2.tgz"
  integrity sha512-vXTJhHebByxZn3lDvDJYw4lR5+uB3vuoHsuYA5AKuxRVn5wzzIfQKGLBmgdVRHKTJYeK5rvJcHnrd0Li49CFpg==
  dependencies:
    color-convert "^1.9.1"
    color-string "^1.5.2"

colorette@^1.2.0:
  version "1.2.1"
  resolved "https://registry.npmjs.org/colorette/-/colorette-1.2.1.tgz"
  integrity sha512-puCDz0CzydiSYOrnXpz/PKd69zRrribezjtE9yd4zvytoRc8+RY/KJPvtPFKZS3E3wP6neGyMe0vOTlHO5L3Pw==

colorette@^2.0.14:
  version "2.0.19"
  resolved "https://registry.npmmirror.com/colorette/-/colorette-2.0.19.tgz"
  integrity sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==

colormin@^1.0.5:
  version "1.1.2"
  resolved "https://registry.npmjs.org/colormin/-/colormin-1.1.2.tgz"
  integrity sha1-6i90IKcrlogaOKrlnsEkpvcpgTM=
  dependencies:
    color "^0.11.0"
    css-color-names "0.0.4"
    has "^1.0.1"

colors@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/colors/-/colors-1.1.2.tgz"
  integrity sha1-FopHAXVran9RoSzgyXv6KMCE7WM=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.18.0, commander@^2.20.0, commander@2:
  version "2.20.3"
  resolved "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

commander@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

commander@^7.0.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/commander/-/commander-7.2.0.tgz"
  integrity sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==

commander@~2.19.0:
  version "2.19.0"
  resolved "https://registry.npmjs.org/commander/-/commander-2.19.0.tgz"
  integrity sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg==

commander@2.17.x:
  version "2.17.1"
  resolved "https://registry.npmjs.org/commander/-/commander-2.17.1.tgz"
  integrity sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-classes@^1.2.6:
  version "1.2.6"
  resolved "https://registry.npm.taobao.org/component-classes/download/component-classes-1.2.6.tgz"
  integrity sha1-xkI5TDYYpNiwuJGe/Mu9kw5c1pE=
  dependencies:
    component-indexof "0.0.3"

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz"
  integrity sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==

component-indexof@0.0.3:
  version "0.0.3"
  resolved "https://registry.npm.taobao.org/component-indexof/download/component-indexof-0.0.3.tgz"
  integrity sha1-EdCRMSI5648yyPJa6csAL/6NPCQ=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz"
  integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression-webpack-plugin@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/compression-webpack-plugin/-/compression-webpack-plugin-3.1.0.tgz"
  integrity sha512-iqTHj3rADN4yHwXMBrQa/xrncex/uEQy8QHlaTKxGchT/hC0SdlJlmL/5eRqffmWq2ep0/Romw6Ld39JjTR/ug==
  dependencies:
    cacache "^13.0.1"
    find-cache-dir "^3.0.0"
    neo-async "^2.5.0"
    schema-utils "^2.6.1"
    serialize-javascript "^2.1.2"
    webpack-sources "^1.0.1"

compression@^1.7.3, compression@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz"
  integrity sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0, concat-stream@^1.6.0:
  version "1.6.2"
  resolved "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz"
  integrity sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect-history-api-fallback@^1.3.0, connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
  integrity sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg==

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/console-browserify/-/console-browserify-1.2.0.tgz"
  integrity sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA==

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/console-control-strings/-/console-control-strings-1.1.0.tgz"
  integrity sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==

consolidate@^0.14.0:
  version "0.14.5"
  resolved "https://registry.npmjs.org/consolidate/-/consolidate-0.14.5.tgz"
  integrity sha1-WiUEe8dvcwcmZ8jLUsmJiI9JTGM=
  dependencies:
    bluebird "^3.1.1"

consolidate@^0.15.1:
  version "0.15.1"
  resolved "https://registry.npmjs.org/consolidate/-/consolidate-0.15.1.tgz"
  integrity sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw==
  dependencies:
    bluebird "^3.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/constants-browserify/-/constants-browserify-1.0.0.tgz"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

contains-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/contains-path/-/contains-path-0.1.0.tgz"
  integrity sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=

content-disposition@0.5.3:
  version "0.5.3"
  resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.3.tgz"
  integrity sha512-ExO0774ikEObIAEV9kDo50o+79VCUdEB6n6lzKgGwupcVeRlhrj3qGAfwq8G6uBJjkqLrhT0qEYFcWng8z1z0g==
  dependencies:
    safe-buffer "5.1.2"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz"
  integrity sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==

contour_plot@^0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/contour_plot/-/contour_plot-0.0.1.tgz"
  integrity sha1-R1hw8DK44zhBKqX8UHiA8L9JXHc=

convert-source-map@^0.3.3:
  version "0.3.5"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-0.3.5.tgz"
  integrity sha1-8dgClQr33SYxof6+BZZVDIarMZA=

convert-source-map@^1.5.1, convert-source-map@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.7.0.tgz"
  integrity sha512-4FJkXzKXEDB1snCFZlLP4gpC3JILicCpGbzG9f9G7tGqGCzETQ2hWPrcinA9oU4wtf2biUaEH5065UnMeR33oA==
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.4.0.tgz"
  integrity sha512-+Hp8fLp57wnUSt0tY0tHEXh4voZRDnoIrZPqlo3DPiI4y9lwg/jqx+1Om94/W6ZaPDOUbnjOt/99w66zk+l1Xg==

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "https://registry.npmjs.org/copy-concurrently/-/copy-concurrently-1.0.5.tgz"
  integrity sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A==
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-webpack-plugin@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/copy-webpack-plugin/-/copy-webpack-plugin-4.6.0.tgz"
  integrity sha512-Y+SQCF+0NoWQryez2zXn5J5knmr9z/9qSQt7fbL78u83rxmigOy8X5+BFn8CFSuX+nKT8gpYwJX68ekqtQt6ZA==
  dependencies:
    cacache "^10.0.4"
    find-cache-dir "^1.0.0"
    globby "^7.1.1"
    is-glob "^4.0.0"
    loader-utils "^1.1.0"
    minimatch "^3.0.4"
    p-limit "^1.0.0"
    serialize-javascript "^1.4.0"

core-js-pure@^3.0.0:
  version "3.6.5"
  resolved "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.6.5.tgz"
  integrity sha512-lacdXOimsiD0QyNf9BC/mxivNJ/ybBGJXQFKzRekp1WTHoVUWsUHEn+2T8GJAzzIhyOuXA+gOxCVN3l+5PLPUA==

core-js@^2.4.0, core-js@^2.5.0, core-js@^2.6.5:
  version "2.6.11"
  resolved "https://registry.npmjs.org/core-js/-/core-js-2.6.11.tgz"
  integrity sha512-5wjnpaT/3dV+XB4borEsnAYQchn00XSgTAWKDkEqv+K8KevjbzmofK6hfJ9TZIlpj2N0xQpazy7PiRQiWHqzWg==

core-js@^3.6.5:
  version "3.9.1"
  resolved "https://registry.npm.taobao.org/core-js/download/core-js-3.9.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js%2Fdownload%2Fcore-js-3.9.1.tgz"
  integrity sha1-zsjeWT246yqF/7Db3rMSy25UYK4=

core-util-is@~1.0.0, core-util-is@1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^2.1.0, cosmiconfig@^2.1.1:
  version "2.2.2"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.2.2.tgz"
  integrity sha512-GiNXLwAFPYHy25XmTPpafYvn3CLAkJ8FLsscq78MQd1Kh0OU6Yzhn4eV2MVF4G9WEQZoWEGltatdR+ntGPMl5A==
  dependencies:
    is-directory "^0.3.1"
    js-yaml "^3.4.3"
    minimist "^1.2.0"
    object-assign "^4.1.0"
    os-homedir "^1.0.1"
    parse-json "^2.2.0"
    require-from-string "^1.1.0"

cosmiconfig@^5.0.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz"
  integrity sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA==
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

create-ecdh@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npmjs.org/create-ecdh/-/create-ecdh-4.0.3.tgz"
  integrity sha512-GbEHQPMOswGpKXM9kCWVrremUcBmjteUaQ01T9rkKCPDXfUHX0IoP9LpHYo2NPFampa4e+/pFDc3jQdxrxQLaw==
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.0.0"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz"
  integrity sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "https://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz"
  integrity sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-env@^5.0.5:
  version "5.2.1"
  resolved "https://registry.npmjs.org/cross-env/-/cross-env-5.2.1.tgz"
  integrity sha512-1yHhtcfAd1r4nwQgknowuUNfIT9E8dOMMspC36g45dN+iD1blloi7xp8X/xAIDnjHWyt1uQ8PHk2fkNaym7soQ==
  dependencies:
    cross-spawn "^6.0.5"

cross-spawn@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-3.0.1.tgz"
  integrity sha512-eZ+m1WNhSZutOa/uRblAc9Ut5MQfukFrFMtPSm3bZCA888NmMd5AWXWdgRZ80zd+pTk1P2JrGjg9pUPTvl2PWQ==
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-5.1.0.tgz"
  integrity sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A==
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz"
  integrity sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypt@~0.0.1:
  version "0.0.2"
  resolved "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz"
  integrity sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs=

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "https://registry.npmjs.org/crypto-browserify/-/crypto-browserify-3.12.0.tgz"
  integrity sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg==
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-color-names@^0.0.4, css-color-names@0.0.4:
  version "0.0.4"
  resolved "https://registry.npmjs.org/css-color-names/-/css-color-names-0.0.4.tgz"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-declaration-sorter@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-4.0.1.tgz"
  integrity sha512-BcxQSKTSEEQUftYpBVnsH4SF05NTuBokb19/sBt6asXGKZ/6VP7PLG1CBCkFDYOnhXhPh0jMhO6xZ71oYHXHBA==
  dependencies:
    postcss "^7.0.1"
    timsort "^0.3.0"

css-line-break@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/css-line-break/-/css-line-break-2.1.0.tgz"
  integrity sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==
  dependencies:
    utrie "^1.0.2"

css-loader@*, css-loader@^0.28.7:
  version "0.28.11"
  resolved "https://registry.npmjs.org/css-loader/-/css-loader-0.28.11.tgz"
  integrity sha512-wovHgjAx8ZIMGSL8pTys7edA1ClmzxHeY6n/d97gg5odgsxEgKjULPR0viqyC+FWMCL9sfqoC/QCUBo62tLvPg==
  dependencies:
    babel-code-frame "^6.26.0"
    css-selector-tokenizer "^0.7.0"
    cssnano "^3.10.0"
    icss-utils "^2.1.0"
    loader-utils "^1.0.2"
    lodash.camelcase "^4.3.0"
    object-assign "^4.1.1"
    postcss "^5.0.6"
    postcss-modules-extract-imports "^1.2.0"
    postcss-modules-local-by-default "^1.2.0"
    postcss-modules-scope "^1.1.0"
    postcss-modules-values "^1.3.0"
    postcss-value-parser "^3.3.0"
    source-list-map "^2.0.0"

css-loader@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/css-loader/-/css-loader-1.0.1.tgz"
  integrity sha512-+ZHAZm/yqvJ2kDtPne3uX0C+Vr3Zn5jFn2N4HywtS5ujwvsVkyg0VArEXpl3BgczDA8anieki1FIzhchX4yrDw==
  dependencies:
    babel-code-frame "^6.26.0"
    css-selector-tokenizer "^0.7.0"
    icss-utils "^2.1.0"
    loader-utils "^1.0.2"
    lodash "^4.17.11"
    postcss "^6.0.23"
    postcss-modules-extract-imports "^1.2.0"
    postcss-modules-local-by-default "^1.2.0"
    postcss-modules-scope "^1.1.0"
    postcss-modules-values "^1.3.0"
    postcss-value-parser "^3.3.0"
    source-list-map "^2.0.0"

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz"
  integrity sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w==

css-select@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/css-select/-/css-select-1.2.0.tgz"
  integrity sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg=
  dependencies:
    boolbase "~1.0.0"
    css-what "2.1"
    domutils "1.5.1"
    nth-check "~1.0.1"

css-select@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/css-select/-/css-select-2.1.0.tgz"
  integrity sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ==
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-selector-tokenizer@^0.7.0:
  version "0.7.2"
  resolved "https://registry.npmjs.org/css-selector-tokenizer/-/css-selector-tokenizer-0.7.2.tgz"
  integrity sha512-yj856NGuAymN6r8bn8/Jl46pR+OC3eEvAhfGYDUe7YPtTPAYrSSw4oAniZ9Y8T5B92hjhwTBLUen0/vKPxf6pw==
  dependencies:
    cssesc "^3.0.0"
    fastparse "^1.1.2"
    regexpu-core "^4.6.0"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  resolved "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.37.tgz"
  integrity sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg==
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-tree@1.0.0-alpha.39:
  version "1.0.0-alpha.39"
  resolved "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.39.tgz"
  integrity sha512-7UvkEYgBAHRG9Nt980lYxjsTrCyHFN53ky3wVsDkiMdVqylqRt+Zc+jm5qw7/qyOvN2dHSYtX0e4MbCCExSvnA==
  dependencies:
    mdn-data "2.0.6"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npmjs.org/css-what/-/css-what-3.3.0.tgz"
  integrity sha512-pv9JPyatiPaQ6pf4OvD/dbfm0o5LviWmwxNWzblYf/1u9QZd0ihV+PMwy5jdQWQ3349kZmKEx9WXuSka2dM4cg==

css-what@2.1:
  version "2.1.3"
  resolved "https://registry.npmjs.org/css-what/-/css-what-2.1.3.tgz"
  integrity sha512-a+EPoD+uZiNfh+5fxw2nO9QwFa6nJe2Or35fGY6Ipw1R3R4AGz1d1TEZrCegvw2YTmZ0jXirGYlzxxpYSHwpEg==

css@^2.0.0:
  version "2.2.4"
  resolved "https://registry.npmjs.org/css/-/css-2.2.4.tgz"
  integrity sha512-oUnjmWpy0niI3x/mPL8dVEI1l7MnG3+HHyRPHf+YFSbK+svOhXpmSOcDURUh2aOCgl2grzrOPt1nHLuCVFULLw==
  dependencies:
    inherits "^2.0.3"
    source-map "^0.6.1"
    source-map-resolve "^0.5.2"
    urix "^0.1.0"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

cssnano-preset-default@^4.0.0, cssnano-preset-default@^4.0.7:
  version "4.0.7"
  resolved "https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-4.0.7.tgz"
  integrity sha512-x0YHHx2h6p0fCl1zY9L9roD7rnlltugGu7zXSKQx6k2rYw0Hi3IqxcoAGF7u9Q5w1nt7vK0ulxV8Lo+EvllGsA==
  dependencies:
    css-declaration-sorter "^4.0.1"
    cssnano-util-raw-cache "^4.0.1"
    postcss "^7.0.0"
    postcss-calc "^7.0.1"
    postcss-colormin "^4.0.3"
    postcss-convert-values "^4.0.1"
    postcss-discard-comments "^4.0.2"
    postcss-discard-duplicates "^4.0.2"
    postcss-discard-empty "^4.0.1"
    postcss-discard-overridden "^4.0.1"
    postcss-merge-longhand "^4.0.11"
    postcss-merge-rules "^4.0.3"
    postcss-minify-font-values "^4.0.2"
    postcss-minify-gradients "^4.0.2"
    postcss-minify-params "^4.0.2"
    postcss-minify-selectors "^4.0.2"
    postcss-normalize-charset "^4.0.1"
    postcss-normalize-display-values "^4.0.2"
    postcss-normalize-positions "^4.0.2"
    postcss-normalize-repeat-style "^4.0.2"
    postcss-normalize-string "^4.0.2"
    postcss-normalize-timing-functions "^4.0.2"
    postcss-normalize-unicode "^4.0.1"
    postcss-normalize-url "^4.0.1"
    postcss-normalize-whitespace "^4.0.2"
    postcss-ordered-values "^4.1.2"
    postcss-reduce-initial "^4.0.3"
    postcss-reduce-transforms "^4.0.2"
    postcss-svgo "^4.0.2"
    postcss-unique-selectors "^4.0.1"

cssnano-util-get-arguments@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/cssnano-util-get-arguments/-/cssnano-util-get-arguments-4.0.0.tgz"
  integrity sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=

cssnano-util-get-match@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/cssnano-util-get-match/-/cssnano-util-get-match-4.0.0.tgz"
  integrity sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=

cssnano-util-raw-cache@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/cssnano-util-raw-cache/-/cssnano-util-raw-cache-4.0.1.tgz"
  integrity sha512-qLuYtWK2b2Dy55I8ZX3ky1Z16WYsx544Q0UWViebptpwn/xDBmog2TLg4f+DBMg1rJ6JDWtn96WHbOKDWt1WQA==
  dependencies:
    postcss "^7.0.0"

cssnano-util-same-parent@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.1.tgz"
  integrity sha512-WcKx5OY+KoSIAxBW6UBBRay1U6vkYheCdjyVNDm85zt5K9mHoGOfsOsqIszfAqrQQFIIKgjh2+FDgIj/zsl21Q==

cssnano@^3.10.0:
  version "3.10.0"
  resolved "https://registry.npmjs.org/cssnano/-/cssnano-3.10.0.tgz"
  integrity sha1-Tzj2zqK5sX+gFJDyPx3GjqZcHDg=
  dependencies:
    autoprefixer "^6.3.1"
    decamelize "^1.1.2"
    defined "^1.0.0"
    has "^1.0.1"
    object-assign "^4.0.1"
    postcss "^5.0.14"
    postcss-calc "^5.2.0"
    postcss-colormin "^2.1.8"
    postcss-convert-values "^2.3.4"
    postcss-discard-comments "^2.0.4"
    postcss-discard-duplicates "^2.0.1"
    postcss-discard-empty "^2.0.1"
    postcss-discard-overridden "^0.1.1"
    postcss-discard-unused "^2.2.1"
    postcss-filter-plugins "^2.0.0"
    postcss-merge-idents "^2.1.5"
    postcss-merge-longhand "^2.0.1"
    postcss-merge-rules "^2.0.3"
    postcss-minify-font-values "^1.0.2"
    postcss-minify-gradients "^1.0.1"
    postcss-minify-params "^1.0.4"
    postcss-minify-selectors "^2.0.4"
    postcss-normalize-charset "^1.1.0"
    postcss-normalize-url "^3.0.7"
    postcss-ordered-values "^2.1.0"
    postcss-reduce-idents "^2.2.2"
    postcss-reduce-initial "^1.0.0"
    postcss-reduce-transforms "^1.0.3"
    postcss-svgo "^2.1.1"
    postcss-unique-selectors "^2.0.2"
    postcss-value-parser "^3.2.3"
    postcss-zindex "^2.0.1"

cssnano@^4.0.0:
  version "4.1.10"
  resolved "https://registry.npmjs.org/cssnano/-/cssnano-4.1.10.tgz"
  integrity sha512-5wny+F6H4/8RgNlaqab4ktc3e0/blKutmq8yNlBFXA//nSFFAqAngjNVRzUvCgYROULmZZUoosL/KSoZo5aUaQ==
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.7"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

cssnano@^4.1.10:
  version "4.1.10"
  resolved "https://registry.npmjs.org/cssnano/-/cssnano-4.1.10.tgz"
  integrity sha512-5wny+F6H4/8RgNlaqab4ktc3e0/blKutmq8yNlBFXA//nSFFAqAngjNVRzUvCgYROULmZZUoosL/KSoZo5aUaQ==
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.7"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

csso@^4.0.2:
  version "4.0.3"
  resolved "https://registry.npmjs.org/csso/-/csso-4.0.3.tgz"
  integrity sha512-NL3spysxUkcrOgnpsT4Xdl2aiEiBG6bXswAABQVHcMrfjjBisFOKwLDOmf4wf32aPdcJws1zds2B0Rg+jqMyHQ==
  dependencies:
    css-tree "1.0.0-alpha.39"

csso@~2.3.1:
  version "2.3.2"
  resolved "https://registry.npmjs.org/csso/-/csso-2.3.2.tgz"
  integrity sha1-3dUsWHAz9J6Utx/FVWnyUuj/X4U=
  dependencies:
    clap "^1.0.9"
    source-map "^0.5.3"

current-script-polyfill@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/current-script-polyfill/-/current-script-polyfill-1.0.0.tgz"
  integrity sha1-8xz35PPiGLBybnOMqSoC00iO9hU=

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/currently-unhandled/-/currently-unhandled-0.4.1.tgz"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

cyclist@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/cyclist/-/cyclist-1.0.1.tgz"
  integrity sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=

d@^1.0.1, d@1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/d/-/d-1.0.1.tgz"
  integrity sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA==
  dependencies:
    es5-ext "^0.10.50"
    type "^1.0.1"

d3-array@1:
  version "1.2.4"
  resolved "https://registry.npmjs.org/d3-array/-/d3-array-1.2.4.tgz"
  integrity sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw==

d3-collection@1:
  version "1.0.7"
  resolved "https://registry.npmjs.org/d3-collection/-/d3-collection-1.0.7.tgz"
  integrity sha512-ii0/r5f4sjKNTfh84Di+DpztYwqKhEyUlKoPrzUFfeSkWxjW49xU2QzO9qrPrNkpdI0XJkfzvmTu8V2Zylln6A==

d3-color@1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/d3-color/-/d3-color-1.4.1.tgz"
  integrity sha512-p2sTHSLCJI2QKunbGb7ocOh7DgTAn8IrLx21QRc/BSnodXM4sv6aLQlnfpvehFMLZEfBc6g9pH9SWQccFYfJ9Q==

d3-composite-projections@^1.2.0:
  version "1.3.2"
  resolved "https://registry.npmjs.org/d3-composite-projections/-/d3-composite-projections-1.3.2.tgz"
  integrity sha512-dao+Dd/EEYBX+FqNYwe/4putAsMXWewSgDj5sETRFsj1z4X/CVI6ctmZhqvpa/MVYCFHoWV9Issu+20OU5VmbA==
  dependencies:
    d3-geo "^1.11.9"
    d3-path "^1.0.9"

d3-dispatch@1:
  version "1.0.6"
  resolved "https://registry.npmjs.org/d3-dispatch/-/d3-dispatch-1.0.6.tgz"
  integrity sha512-fVjoElzjhCEy+Hbn8KygnmMS7Or0a9sI2UzGwoB7cCtvI1XpVN9GpoYlnb3xt2YV66oXYb1fLJ8GMvP4hdU1RA==

d3-dsv@^1.0.5:
  version "1.2.0"
  resolved "https://registry.npmjs.org/d3-dsv/-/d3-dsv-1.2.0.tgz"
  integrity sha512-9yVlqvZcSOMhCYzniHE7EVUws7Fa1zgw+/EAV2BxJoG3ME19V6BQFBwI855XQDsxyOuG7NibqRMTtiF/Qup46g==
  dependencies:
    commander "2"
    iconv-lite "0.4"
    rw "1"

d3-ease@~1.0.3, d3-ease@1:
  version "1.0.6"
  resolved "https://registry.npmjs.org/d3-ease/-/d3-ease-1.0.6.tgz"
  integrity sha512-SZ/lVU7LRXafqp7XtIcBdxnWl8yyLpgOmzAk0mWBI9gXNzLDx5ybZgnRbH9dN/yY5tzVBqCQ9avltSnqVwessQ==

d3-format@^1.3.0:
  version "1.4.4"
  resolved "https://registry.npmjs.org/d3-format/-/d3-format-1.4.4.tgz"
  integrity sha512-TWks25e7t8/cqctxCmxpUuzZN11QxIA7YrMbram94zMQ0PXjE4LVIMe/f6a4+xxL8HQ3OsAFULOINQi1pE62Aw==

d3-geo-projection@~2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/d3-geo-projection/-/d3-geo-projection-2.1.2.tgz"
  integrity sha1-ffjh6dBG1jHGUJ9+UxNX1K3CSqM=
  dependencies:
    commander "2"
    d3-array "1"
    d3-geo "^1.1.0"

d3-geo@^1.1.0, d3-geo@~1.6.4:
  version "1.6.4"
  resolved "https://registry.npmjs.org/d3-geo/-/d3-geo-1.6.4.tgz"
  integrity sha1-8g4eRhyxhF9ai+Vatvh2VCp+MZk=
  dependencies:
    d3-array "1"

d3-geo@^1.11.9:
  version "1.12.1"
  resolved "https://registry.npmjs.org/d3-geo/-/d3-geo-1.12.1.tgz"
  integrity sha512-XG4d1c/UJSEX9NfU02KwBL6BYPj8YKHxgBEw5om2ZnTRSbIcego6dhHwcxuSR3clxh0EpE38os1DVPOmnYtTPg==
  dependencies:
    d3-array "1"

d3-hexjson@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npmjs.org/d3-hexjson/-/d3-hexjson-1.1.0.tgz"
  integrity sha512-fHXGMnA1iYBg0Kw5jqSm9kZDRyw4Mq74AzJzUM/FiCDy9a47xM5qj2+ofTaZbeku9tHsRtoujolHiqmD3s3EHA==
  dependencies:
    d3-array "1"

d3-hierarchy@^1.1.5:
  version "1.1.9"
  resolved "https://registry.npmjs.org/d3-hierarchy/-/d3-hierarchy-1.1.9.tgz"
  integrity sha512-j8tPxlqh1srJHAtxfvOUwKNYJkQuBFdM1+JAUfq6xqH5eAqf93L7oG1NVqDa4CpFZNvnNKtCYEUC8KY9yEn9lQ==

d3-interpolate@~1.1.5, d3-interpolate@1:
  version "1.1.6"
  resolved "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-1.1.6.tgz"
  integrity sha512-mOnv5a+pZzkNIHtw/V6I+w9Lqm9L5bG3OTXPM5A+QO0yyVMQ4W1uZhR+VOJmazaOZXri2ppbiZ5BUNWT0pFM9A==
  dependencies:
    d3-color "1"

d3-path@^1.0.9, d3-path@1:
  version "1.0.9"
  resolved "https://registry.npmjs.org/d3-path/-/d3-path-1.0.9.tgz"
  integrity sha512-VLaYcn81dtHVTjEHd8B+pbe9yHWpXKZUC87PzoFmsFrJqgFwDe/qxfp5MlfsfM1V5E/iVt0MmEbWQ7FVIXh/bg==

d3-sankey@^0.9.1:
  version "0.9.1"
  resolved "https://registry.npmjs.org/d3-sankey/-/d3-sankey-0.9.1.tgz"
  integrity sha512-nnRkDaUMjBdeuGg+kWGdA+tjG1AVTnJ+Ykw7ff7CZHVI17Hm5sy8n0UXykVffn13aNHwK5wPOdOt1gS1ZEaF+A==
  dependencies:
    d3-array "1"
    d3-collection "1"
    d3-shape "^1.2.0"

d3-selection@^1.0.2, d3-selection@^1.1.0:
  version "1.4.1"
  resolved "https://registry.npmjs.org/d3-selection/-/d3-selection-1.4.1.tgz"
  integrity sha512-BTIbRjv/m5rcVTfBs4AMBLKs4x8XaaLkwm28KWu9S2vKNqXkXt2AH2Qf0sdPZHjFxcWg/YL53zcqAz+3g4/7PA==

d3-shape@^1.2.0:
  version "1.3.7"
  resolved "https://registry.npmjs.org/d3-shape/-/d3-shape-1.3.7.tgz"
  integrity sha512-EUkvKjqPFUAZyOlhY5gzCxCeI0Aep04LwIRpsZ/mLFelJiUfnK56jo5JMDSE7yyP2kLSb6LtF+S5chMk7uqPqw==
  dependencies:
    d3-path "1"

d3-timer@~1.0.6, d3-timer@1:
  version "1.0.10"
  resolved "https://registry.npmjs.org/d3-timer/-/d3-timer-1.0.10.tgz"
  integrity sha512-B1JDm0XDaQC+uvo4DT79H0XmBskgS3l6Ve+1SBCfxgmtIb1AVrPIoqd+nPSv+loMX8szQ0sVUhGngL7D5QPiXw==

d3-transition@^1.0.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/d3-transition/-/d3-transition-1.3.2.tgz"
  integrity sha512-sc0gRU4PFqZ47lPVHloMn9tlPcv8jxgOQg+0zjhfZXMQuvppjG6YuwdMBE0TuqCZjeJkLecku/l9R0JPcRhaDA==
  dependencies:
    d3-color "1"
    d3-dispatch "1"
    d3-ease "1"
    d3-interpolate "1"
    d3-selection "^1.1.0"
    d3-timer "1"

d3-voronoi@^1.1.2:
  version "1.1.4"
  resolved "https://registry.npmjs.org/d3-voronoi/-/d3-voronoi-1.1.4.tgz"
  integrity sha512-dArJ32hchFsrQ8uMiTBLq256MpnZjeuBtdHpaDlYuQyjU0CVzCJl/BVW+SkszaAeH95D/8gxqAhgx0ouAWAfRg==

dagre@^0.8.2:
  version "0.8.5"
  resolved "https://registry.npmjs.org/dagre/-/dagre-0.8.5.tgz"
  integrity sha512-/aTqmnRta7x7MCCpExk7HQL2O4owCT2h8NT//9I1OQ9vt29Pa0BzSAkR5lwFUcQ7491yVi/3CXU9jQ5o0Mn2Sw==
  dependencies:
    graphlib "^2.1.8"
    lodash "^4.17.15"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

dayjs@^1.8.0:
  version "1.8.29"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.8.29.tgz"
  integrity sha512-Vm6teig8ZWK7rH/lxzVGxZJCljPdmUr6q/3f4fr5F0VWNGVkZEjZOQJsAN8hUHUqn+NK4XHNEpJZS1MwLyDcLw==

de-indent@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/de-indent/-/de-indent-1.0.2.tgz"
  integrity sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=

debug@^2.2.0:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^2.3.3:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^2.6.6:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^2.6.8:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^2.6.9:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^3.1.0, debug@=3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz"
  integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
  dependencies:
    ms "2.0.0"

debug@^3.1.1:
  version "3.2.6"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz"
  integrity sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==
  dependencies:
    ms "^2.1.1"

debug@^3.2.5:
  version "3.2.6"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz"
  integrity sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==
  dependencies:
    ms "^2.1.1"

debug@^4.0.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz"
  integrity sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==
  dependencies:
    ms "^2.1.1"

debug@^4.1.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz"
  integrity sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==
  dependencies:
    ms "^2.1.1"

debug@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz"
  integrity sha512-pYAIzeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==
  dependencies:
    ms "^2.1.1"

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

decamelize@^1.0.0, decamelize@^1.1.1, decamelize@^1.1.2, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decamelize@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/decamelize/-/decamelize-3.2.0.tgz"
  integrity sha512-4TgkVUsmmu7oCSyGBm5FvfMoACuoh9EOidm7V5/J2X2djAwwt57qb3F2KMP2ITqODTCSwb+YRV+0Zqrv18k/hw==
  dependencies:
    xregexp "^4.2.4"

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

decompress-response@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/decompress-response/-/decompress-response-6.0.0.tgz"
  integrity sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==
  dependencies:
    mimic-response "^3.1.0"

deep-equal@^1.0.1, deep-equal@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.1.tgz"
  integrity sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g==
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

deepmerge@^1.5.2:
  version "1.5.2"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.2.tgz"
  integrity sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==

default-gateway@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/default-gateway/-/default-gateway-4.2.0.tgz"
  integrity sha512-h6sMrVB1VMWVrW13mSc6ia/DwYYw5MN6+exNu1OaJeFac5aSAvwM7lZ0NVfTABuSkQelr4h5oebg3KB1XPdjgA==
  dependencies:
    execa "^1.0.0"
    ip-regex "^2.1.0"

default-gateway@^5.0.2:
  version "5.0.5"
  resolved "https://registry.npmjs.org/default-gateway/-/default-gateway-5.0.5.tgz"
  integrity sha512-z2RnruVmj8hVMmAnEJMTIJNijhKCDiGjbLP+BHJFOT7ld3Bo5qcIBpVYDniqhbMIIf+jZDlkP2MkPXiQy/DBLA==
  dependencies:
    execa "^3.3.0"

defaults@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/defaults/-/defaults-1.0.3.tgz"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz"
  integrity sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz"
  integrity sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

defined@^1.0.0, defined@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/defined/-/defined-1.0.0.tgz"
  integrity sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM=

del@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/del/-/del-3.0.0.tgz"
  integrity sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=
  dependencies:
    globby "^6.1.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    p-map "^1.1.1"
    pify "^3.0.0"
    rimraf "^2.2.8"

del@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/del/-/del-4.1.1.tgz"
  integrity sha512-QwGuEUouP2kVwQenAsOof5Fv8K9t3D8Ca8NxcXKrIpEHjTXK5J2nXLdP+ALI1cgv8wj7KuwBhTwBkOZSJKM5XQ==
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegate@^3.1.2:
  version "3.2.0"
  resolved "https://registry.npmjs.org/delegate/-/delegate-3.2.0.tgz"
  integrity sha512-IofjkYBZaZivn0V8nnsMJGBr4jVLxHDheKSW88PyxS5QC4Vo9ZbZVvhzlSxY87fVq3STR6r+4cGepyHkcWOQSw==

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/delegates/-/delegates-1.0.0.tgz"
  integrity sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/des.js/-/des.js-1.0.1.tgz"
  integrity sha512-Q0I4pfFrv2VPd34/vfLrFOoRmlYj3OV50i7fskps1jZWK1kApMWWT9G6RRUeYedLcBDIhnSDaUvJMb3AhUlaEA==
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-browser@^5.1.0:
  version "5.1.1"
  resolved "https://registry.npmjs.org/detect-browser/-/detect-browser-5.1.1.tgz"
  integrity sha512-5n2aWI57qC3kZaK4j2zYsG6L1LrxgLptGCNhMQgdKhVn6cSdcq43pp6xHPfTHG3TYM6myF4tIPWiZtfdVDgb9w==

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/detect-indent/-/detect-indent-4.0.0.tgz"
  integrity sha1-920GQ1LN9Docts5hnE7jqUdd4gg=
  dependencies:
    repeating "^2.0.0"

detect-node@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/detect-node/-/detect-node-2.0.4.tgz"
  integrity sha512-ZIzRpLJrOj7jjP2miAtgqIfmzbxa4ZOr5jJc601zklsfEx9oTzmmj2nVpIPRpNlRTIh8lc1kyViIY7BWSGNmKw==

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "https://registry.npmjs.org/diffie-hellman/-/diffie-hellman-5.0.3.tgz"
  integrity sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dir-glob@^2.0.0, dir-glob@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-2.2.2.tgz"
  integrity sha512-f9LBi5QWzIW3I6e//uxZoLBlUt9kcp66qo0sSCxL6YZKc75R1c4MFCoe/LaZiBGmgujvQdxc5Bn3QhfyvK5Hsw==
  dependencies:
    path-type "^3.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/dns-packet/-/dns-packet-1.3.1.tgz"
  integrity sha512-0UxfQkMhYAUaZI+xrNZOz/as5KgDU0M/fQ9b6SpkyLbk3GEswDi6PADJVaYJradtRVsRIlF1zLyOodbcTCDzUg==
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/dns-txt/-/dns-txt-2.0.2.tgz"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

doctrine@1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-1.5.0.tgz"
  integrity sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

dom-align@^1.10.4, dom-align@1.12.0:
  version "1.12.0"
  resolved "https://registry.npmjs.org/dom-align/-/dom-align-1.12.0.tgz"
  integrity sha512-YkoezQuhp3SLFGdOlr5xkqZ640iXrnHAwVYcDg8ZKRUtO7mSzSC2BA5V0VuyAwPSJA4CLIc6EDDJh4bEsD2+zA==

dom-closest@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/dom-closest/download/dom-closest-0.2.0.tgz"
  integrity sha1-69n5HRvyLo1vR3h2u80+yQIWwM8=
  dependencies:
    dom-matches ">=1.0.1"

dom-converter@^0.2:
  version "0.2.0"
  resolved "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz"
  integrity sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==
  dependencies:
    utila "~0.4"

dom-matches@>=1.0.1:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/dom-matches/download/dom-matches-2.0.0.tgz"
  integrity sha1-0nKLQWqHUzmA6wibhI0lPPI6dYw=

dom-scroll-into-view@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/dom-scroll-into-view/download/dom-scroll-into-view-2.0.1.tgz"
  integrity sha1-DezIUigB/Y0/HGujVadNOCxfmJs=

dom-serializer@0:
  version "0.2.2"
  resolved "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz"
  integrity sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

dom-walk@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npmjs.org/dom-walk/-/dom-walk-0.1.2.tgz"
  integrity sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w==

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/domain-browser/-/domain-browser-1.2.0.tgz"
  integrity sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA==

domelementtype@^1.3.1, domelementtype@1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz"
  integrity sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==

domelementtype@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/domelementtype/-/domelementtype-2.0.1.tgz"
  integrity sha512-5HOHUDsYZWV8FGWN0Njbr/Rn7f/eWSQi1v7+HsUVwXgn8nWWlL64zKDkS0n8ZmQ3mlWOMuXOnR+7Nx/5tMO5AQ==

domhandler@^2.3.0:
  version "2.4.2"
  resolved "https://registry.npmjs.org/domhandler/-/domhandler-2.4.2.tgz"
  integrity sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA==
  dependencies:
    domelementtype "1"

domutils@^1.5.1, domutils@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/domutils/-/domutils-1.7.0.tgz"
  integrity sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz"
  integrity sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

dot-case@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/dot-case/-/dot-case-3.0.3.tgz"
  integrity sha512-7hwEmg6RiSQfm/GwPL4AAWXKy3YNNZA3oFv2Pdiey0mwkRCPZ9x6SZbkLcn8Ma5PYeVokzoD4Twv2n7LKp5WeA==
  dependencies:
    no-case "^3.0.3"
    tslib "^1.10.0"

dot-prop@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/dot-prop/-/dot-prop-5.2.0.tgz"
  integrity sha512-uEUyaDKoSQ1M4Oq8l45hSE26SnTxL6snNnqvK/VWx5wJhmff5z0FUVJDKDanor/6w3kzE3i7XZOk+7wC0EXr1A==
  dependencies:
    is-obj "^2.0.0"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
  integrity sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==

dotenv@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/dotenv/-/dotenv-7.0.0.tgz"
  integrity sha512-M3NhsLbV1i6HuGzBUH8vXrtxOk+tWmzWKDMbAVSUp3Zsjm7ywFeuwrUXhmhQyRK1q5B5GGy7hcXPbj3bnfZg2g==

dotignore@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/dotignore/-/dotignore-0.1.2.tgz"
  integrity sha512-UGGGWfSauusaVJC+8fgV+NVvBXkCTmVv7sk6nojDZZvuOUNGUy0Zk4UpHQD6EDjS0jpBwcACvH4eofvyzBcRDw==
  dependencies:
    minimatch "^3.0.4"

duplexer@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/duplexer/-/duplexer-0.1.1.tgz"
  integrity sha1-rOb/gIwc5mtX0ev5eXessCM0z8E=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "https://registry.npmjs.org/duplexify/-/duplexify-3.7.1.tgz"
  integrity sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

easy-stack@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/easy-stack/-/easy-stack-1.0.0.tgz"
  integrity sha1-EskbMIWjfwuqM26UhurEv5Tj54g=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

echarts@^5.4.2:
  version "5.4.2"
  resolved "https://registry.npmmirror.com/echarts/-/echarts-5.4.2.tgz"
  integrity sha512-2W3vw3oI2tWJdyAz+b8DuWS0nfXtSDqlDmqgin/lfzbkB01cuMEN66KWBlmur3YMp5nEDEEt5s23pllnAzB4EA==
  dependencies:
    tslib "2.3.0"
    zrender "5.4.3"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.7.4"
  resolved "https://registry.npmjs.org/ejs/-/ejs-2.7.4.tgz"
  integrity sha512-7vmuyh5+kuUyJKePhQfRQBhXV5Ce+RnaeeQArKu1EAMpL3WbgMt5WG6uQZpEVvYSSsxMXRKOewtDk9RaTKXRlA==

electron-to-chromium@^1.2.7, electron-to-chromium@^1.3.47, electron-to-chromium@^1.3.488:
  version "1.3.495"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.3.495.tgz"
  integrity sha512-cIPX+4Y3RH/+SoqYd168Rx//CnjsZSdF3XTUhwMf3dwYuTfvWIgmmy6s1ipptGaOA+scH2Vj57Mc9fqp/ho22Q==

elliptic@^6.0.0, elliptic@^6.5.2:
  version "6.5.3"
  resolved "https://registry.npmjs.org/elliptic/-/elliptic-6.5.3.tgz"
  integrity sha512-IMqzv5wNQf+E6aHeIqATs0tOLeOTwj1QKbRcS3jBbYkl5oLAserA8yJTT7/VyHUYG91PRmPyeQDObKLPpeS4dw==
  dependencies:
    bn.js "^4.4.0"
    brorand "^1.0.1"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.0"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-7.0.3.tgz"
  integrity sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/emojis-list/-/emojis-list-2.1.0.tgz"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

enhanced-resolve@^3.4.0:
  version "3.4.1"
  resolved "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-3.4.1.tgz"
  integrity sha512-ZaAux1rigq1e2nQrztHn4h2ugvpzZxs64qneNah+8Mh/K0CRqJFJc+UoXnUsq+1yX+DmQFPPdVqboKAJ89e0Iw==
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.4.0"
    object-assign "^4.0.1"
    tapable "^0.2.7"

enhanced-resolve@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.2.0.tgz"
  integrity sha512-S7eiFb/erugyd1rLb6mQ3Vuq+EXHv5cpCkNqqIkYkBgN2QdFnyCZzFBleqwGEx4lgNGYij81BWnCrFNK7vxvjQ==
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

enhanced-resolve@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz"
  integrity sha512-Nv9m36S/vxpsI+Hc4/ZGRs0n9mXqSWGGq49zxb/cJfPAQMbUtttJAlNPS4AQzaBdw/pKskw5bMbekT/Y7W/Wlg==
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

enquire.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npmjs.org/enquire.js/-/enquire.js-2.1.6.tgz"
  integrity sha1-PoeAybi4NQhMP2DhZtvDwqPImBQ=

entities@^1.1.1:
  version "1.1.2"
  resolved "https://registry.npmjs.org/entities/-/entities-1.1.2.tgz"
  integrity sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w==

entities@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/entities/-/entities-2.0.3.tgz"
  integrity sha512-MyoZ0jgnLvB2X3Lg5HqpFmn1kybDiIfEQmKzTb5apr51Rb+T3KdmMiqa70T+bhGnyv7bQ6WMj2QMHpGMmlrUYQ==

envinfo@^7.7.3:
  version "7.8.1"
  resolved "https://registry.npmmirror.com/envinfo/-/envinfo-7.8.1.tgz"
  integrity sha512-/o+BXHmB7ocbHEAs6F2EnG0ogybVVUdkRunTT2glZU9XAaGmhqskrvKwqXuDfNjEO0LZKWdejEEpnq8aM0tOaw==

errno@^0.1.1, errno@^0.1.3, errno@~0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/errno/-/errno-0.1.7.tgz"
  integrity sha512-MfrRBDWzIWifgq6tJj60gkAwtLNb6sQPlcFrSOflcP1aFmmruKQ2wRnze/8V6kgyz7H3FF8Npzv78mZ7XLLflg==
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.0:
  version "2.0.6"
  resolved "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.0.6.tgz"
  integrity sha512-d51brTeqC+BHlwF0BhPtcYgF5nlzf9ZZ0ZIUQNZpc9ZB9qw5IJ2diTrBY9jlCJkTLITYPjmiX6OWCwH+fuyNgQ==
  dependencies:
    stackframe "^1.1.1"

es-abstract@^1.17.0, es-abstract@^1.17.0-next.1, es-abstract@^1.17.2, es-abstract@^1.17.5:
  version "1.17.6"
  resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.17.6.tgz"
  integrity sha512-Fr89bON3WFyUi5EvAeI48QTWX0AyekGgLA8H+c+7fbfCkJwRWRMLd8CQedNEyJuoYYhmtEqY92pgte1FAhBlhw==
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.0"
    is-regex "^1.1.0"
    object-inspect "^1.7.0"
    object-keys "^1.1.1"
    object.assign "^4.1.0"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es5-ext@^0.10.35, es5-ext@^0.10.46, es5-ext@^0.10.50, es5-ext@^0.10.62, es5-ext@~0.10.14:
  version "0.10.62"
  resolved "https://registry.npmmirror.com/es5-ext/-/es5-ext-0.10.62.tgz"
  integrity sha512-BHLqn0klhEpnOKSrzn/Xsz2UIW8j+cGmo9JLzr8BiUapV8hPL9+FliFqjwr9ngW7jWdnxv6eO+/LqyhJVqgrjA==
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    next-tick "^1.1.0"

es5-shim@^4.5.1:
  version "4.6.7"
  resolved "https://registry.npmjs.org/es5-shim/-/es5-shim-4.6.7.tgz"
  integrity sha512-jg21/dmlrNQI7JyyA2w7n+yifSxBng0ZralnSfVZjoCawgNTCnS+yBCyVM9DL5itm7SUnDGgv7hcq2XCZX4iRQ==

es6-iterator@^2.0.3, es6-iterator@~2.0.1, es6-iterator@~2.0.3:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/es6-iterator/-/es6-iterator-2.0.3.tgz"
  integrity sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g==
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-map@^0.1.3:
  version "0.1.5"
  resolved "https://registry.npmmirror.com/es6-map/-/es6-map-0.1.5.tgz"
  integrity sha512-mz3UqCh0uPCIqsw1SSAkB/p0rOzF/M0V++vyN7JqlPtSW/VsYgQBvVvqMLmfBuyMzTpLnNqi6JmcSizs4jy19A==
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-set "~0.1.5"
    es6-symbol "~3.1.1"
    event-emitter "~0.3.5"

es6-set@~0.1.5:
  version "0.1.6"
  resolved "https://registry.npmmirror.com/es6-set/-/es6-set-0.1.6.tgz"
  integrity sha512-TE3LgGLDIBX332jq3ypv6bcOpkLO0AslAQo7p2VqX/1N46YNsvIWgvjojjSEnWEGWMhr1qUbYeTSir5J6mFHOw==
  dependencies:
    d "^1.0.1"
    es5-ext "^0.10.62"
    es6-iterator "~2.0.3"
    es6-symbol "^3.1.3"
    event-emitter "^0.3.5"
    type "^2.7.2"

es6-symbol@^3.1.1, es6-symbol@^3.1.3, es6-symbol@~3.1.1:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/es6-symbol/-/es6-symbol-3.1.3.tgz"
  integrity sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA==
  dependencies:
    d "^1.0.1"
    ext "^1.1.2"

es6-weak-map@^2.0.1:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/es6-weak-map/-/es6-weak-map-2.0.3.tgz"
  integrity sha512-p5um32HOTO1kP+w7PRnB+5lQ43Z6muuMuIMffvDN8ZB4GcnjLBV6zGStpbASIMk4DCAvEaamhe2zhyCb/QXXsA==
  dependencies:
    d "1"
    es5-ext "^0.10.46"
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.1"

escalade@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.0.1.tgz"
  integrity sha512-DR6NO3h9niOT+MZs7bjxlj2a1k+POu5RN8CLTPX2+i78bRi9eLe7+0zXgUHMnGXWybYcL61E9hGhPKqedy8tQA==

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escope@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/escope/-/escope-3.6.0.tgz"
  integrity sha512-75IUQsusDdalQEW/G/2esa87J7raqdJF+Ca0/Xm5C3Q58Nr4yVYjZGp/P1+2xiEVgXRrA39dpRb8LcshajbqDQ==
  dependencies:
    es6-map "^0.1.3"
    es6-weak-map "^2.0.1"
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-config-standard@^12.0.0:
  version "12.0.0"
  resolved "https://registry.npmjs.org/eslint-config-standard/-/eslint-config-standard-12.0.0.tgz"
  integrity sha512-COUz8FnXhqFitYj4DTqHzidjIL/t4mumGZto5c7DrBpvWoie+Sn3P4sLEzUGeYhRElWuFEf8K1S1EfvD1vixCQ==

eslint-import-resolver-node@^0.3.3:
  version "0.3.4"
  resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.4.tgz"
  integrity sha512-ogtf+5AB/O+nM6DIeBUNr2fuT7ot9Qg/1harBfBtaP13ekEWFQEEMP94BCB7zaNW3gyY+8SHYF00rnqYwXKWOA==
  dependencies:
    debug "^2.6.9"
    resolve "^1.13.1"

eslint-loader@^2.1.2:
  version "2.2.1"
  resolved "https://registry.npmjs.org/eslint-loader/-/eslint-loader-2.2.1.tgz"
  integrity sha512-RLgV9hoCVsMLvOxCuNjdqOrUqIj9oJg8hF44vzJaYqsAHuY9G2YAeN3joQ9nxP0p5Th9iFSIpKo+SD8KISxXRg==
  dependencies:
    loader-fs-cache "^1.0.0"
    loader-utils "^1.0.2"
    object-assign "^4.0.1"
    object-hash "^1.1.4"
    rimraf "^2.6.1"

eslint-module-utils@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.6.0.tgz"
  integrity sha512-6j9xxegbqe8/kZY8cYpcp0xhbK0EgJlg3g9mib3/miLaExuuwc3n5UEfSnU6hWMbT0FAYVvDbL9RrRgpUeQIvA==
  dependencies:
    debug "^2.6.9"
    pkg-dir "^2.0.0"

eslint-plugin-es@^1.3.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/eslint-plugin-es/-/eslint-plugin-es-1.4.1.tgz"
  integrity sha512-5fa/gR2yR3NxQf+UXkeLeP8FBBl6tSgdrAz1+cF84v1FMM4twGwQoqTnn+QxFLcPOrF4pdKEJKDB/q9GoyJrCA==
  dependencies:
    eslint-utils "^1.4.2"
    regexpp "^2.0.1"

eslint-plugin-import@^2.14.0, eslint-plugin-import@>=2.13.0:
  version "2.22.0"
  resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.22.0.tgz"
  integrity sha512-66Fpf1Ln6aIS5Gr/55ts19eUuoDhAbZgnr6UxK5hbDx6l/QgQgx61AePq+BV4PP2uXQFClgMVzep5zZ94qqsxg==
  dependencies:
    array-includes "^3.1.1"
    array.prototype.flat "^1.2.3"
    contains-path "^0.1.0"
    debug "^2.6.9"
    doctrine "1.5.0"
    eslint-import-resolver-node "^0.3.3"
    eslint-module-utils "^2.6.0"
    has "^1.0.3"
    minimatch "^3.0.4"
    object.values "^1.1.1"
    read-pkg-up "^2.0.0"
    resolve "^1.17.0"
    tsconfig-paths "^3.9.0"

eslint-plugin-node@^8.0.0, eslint-plugin-node@>=7.0.0:
  version "8.0.1"
  resolved "https://registry.npmjs.org/eslint-plugin-node/-/eslint-plugin-node-8.0.1.tgz"
  integrity sha512-ZjOjbjEi6jd82rIpFSgagv4CHWzG9xsQAVp1ZPlhRnnYxcTgENUVBvhYmkQ7GvT1QFijUSo69RaiOJKhMu6i8w==
  dependencies:
    eslint-plugin-es "^1.3.1"
    eslint-utils "^1.3.1"
    ignore "^5.0.2"
    minimatch "^3.0.4"
    resolve "^1.8.1"
    semver "^5.5.0"

eslint-plugin-promise@^4.0.1, eslint-plugin-promise@>=4.0.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/eslint-plugin-promise/-/eslint-plugin-promise-4.2.1.tgz"
  integrity sha512-VoM09vT7bfA7D+upt+FjeBO5eHIJQBUWki1aPvB+vbNiHS3+oGIJGIeyBtKQTME6UPXXy3vV07OL1tHd3ANuDw==

eslint-plugin-standard@^4.0.0, eslint-plugin-standard@>=4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/eslint-plugin-standard/-/eslint-plugin-standard-4.0.1.tgz"
  integrity sha512-v/KBnfyaOMPmZc/dmc6ozOdWqekGp7bBGq4jLAecEfPGmfKiWS4sA8sC0LqiV9w5qmXAtXVn4M3p1jSyhY85SQ==

eslint-plugin-vue@^4.7.1:
  version "4.7.1"
  resolved "https://registry.npmjs.org/eslint-plugin-vue/-/eslint-plugin-vue-4.7.1.tgz"
  integrity sha512-esETKhVMI7Vdli70Wt4bvAwnZBJeM0pxVX9Yb0wWKxdCJc2EADalVYK/q2FzMw8oKN0wPMdqVCKS8kmR89recA==
  dependencies:
    vue-eslint-parser "^2.0.3"

eslint-plugin-vue@^5.1.0:
  version "5.2.3"
  resolved "https://registry.npmjs.org/eslint-plugin-vue/-/eslint-plugin-vue-5.2.3.tgz"
  integrity sha512-mGwMqbbJf0+VvpGR5Lllq0PMxvTdrZ/ZPjmhkacrCHbubJeJOt+T6E3HUzAifa2Mxi7RSdJfC9HFpOeSYVMMIw==
  dependencies:
    vue-eslint-parser "^5.0.0"

eslint-scope@^3.7.1:
  version "3.7.3"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-3.7.3.tgz"
  integrity sha512-W+B0SvF4gamyCTmUc+uITPY0989iXVfKvhwtmJocTaYoc/3khEHmEmvfY/Gn9HA9VV75jrQECsHizkNw1b68FA==
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^4.0.0, eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.3.tgz"
  integrity sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-utils@^1.3.1, eslint-utils@^1.4.2:
  version "1.4.3"
  resolved "https://registry.npmjs.org/eslint-utils/-/eslint-utils-1.4.3.tgz"
  integrity sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q==
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  integrity sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==

"eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0", eslint@^5.0.0, eslint@^5.16.0, "eslint@>=1.6.0 <7.0.0", eslint@>=3.9.0, eslint@>=4.19.1, eslint@>=5.0.0:
  version "5.16.0"
  resolved "https://registry.npmjs.org/eslint/-/eslint-5.16.0.tgz"
  integrity sha512-S3Rz11i7c8AA5JPv7xAH+dOyq/Cu/VXHiHXBPOU1k/JAM5dXqQPt3qcrhpHSorXmrpu2g0gkIBVXAqCpzfoZIg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.9.1"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^4.0.3"
    eslint-utils "^1.3.1"
    eslint-visitor-keys "^1.0.0"
    espree "^5.0.1"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^11.7.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^6.2.2"
    js-yaml "^3.13.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.11"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^5.5.1"
    strip-ansi "^4.0.0"
    strip-json-comments "^2.0.1"
    table "^5.2.3"
    text-table "^0.2.0"

"eslint@^3.18.0 || ^4.0.0", eslint@^4.19.1, "eslint@>= 4.12.1":
  version "4.19.1"
  resolved "https://registry.npmjs.org/eslint/-/eslint-4.19.1.tgz"
  integrity sha512-bT3/1x1EbZB7phzYu7vCr1v3ONuzDtX8WjuM9c0iYxe+cq+pwcKEoQjl7zd3RpC6YOLgnSy3cTN58M2jcoPDIQ==
  dependencies:
    ajv "^5.3.0"
    babel-code-frame "^6.22.0"
    chalk "^2.1.0"
    concat-stream "^1.6.0"
    cross-spawn "^5.1.0"
    debug "^3.1.0"
    doctrine "^2.1.0"
    eslint-scope "^3.7.1"
    eslint-visitor-keys "^1.0.0"
    espree "^3.5.4"
    esquery "^1.0.0"
    esutils "^2.0.2"
    file-entry-cache "^2.0.0"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^11.0.1"
    ignore "^3.3.3"
    imurmurhash "^0.1.4"
    inquirer "^3.0.6"
    is-resolvable "^1.0.0"
    js-yaml "^3.9.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.4"
    minimatch "^3.0.2"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    pluralize "^7.0.0"
    progress "^2.0.0"
    regexpp "^1.0.1"
    require-uncached "^1.0.3"
    semver "^5.3.0"
    strip-ansi "^4.0.0"
    strip-json-comments "~2.0.1"
    table "4.0.2"
    text-table "~0.2.0"

espree@^3.5.2, espree@^3.5.4:
  version "3.5.4"
  resolved "https://registry.npmjs.org/espree/-/espree-3.5.4.tgz"
  integrity sha512-yAcIQxtmMiB/jL32dzEp2enBeidsB7xWPLNiw3IIkpVds1P+h7qF9YwJq1yUNzp2OKXgAprs4F61ih66UsoD1A==
  dependencies:
    acorn "^5.5.0"
    acorn-jsx "^3.0.0"

espree@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/espree/-/espree-4.1.0.tgz"
  integrity sha512-I5BycZW6FCVIub93TeVY1s7vjhP9CY6cXCznIRfiig7nRviKZYdRnj/sHEWC6A7WE9RDWOFq9+7OsWSYz8qv2w==
  dependencies:
    acorn "^6.0.2"
    acorn-jsx "^5.0.0"
    eslint-visitor-keys "^1.0.0"

espree@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/espree/-/espree-5.0.1.tgz"
  integrity sha512-qWAZcWh4XE/RwzLJejfcofscgMc9CamR6Tn1+XRXNzrvUSSbiAjGOI/fggztjIi7y9VLPqnICMIPiGyr8JaZ0A==
  dependencies:
    acorn "^6.0.7"
    acorn-jsx "^5.0.0"
    eslint-visitor-keys "^1.0.0"

esprima@^2.6.0:
  version "2.7.3"
  resolved "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz"
  integrity sha1-luO3DVd59q1JzQMmc9HDEnZ7pYE=

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

esquery@^1.0.0, esquery@^1.0.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/esquery/-/esquery-1.3.1.tgz"
  integrity sha512-olpvt9QG0vniUBZspVRN6lwB7hOZoTRtT+jzR+tS4ffYx2mzbw+z0XCOk44aaLYKApNX5nMm+E+P6o25ip/DHQ==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.1.tgz"
  integrity sha512-64RBB++fIOAXPw3P9cy89qfMlvZEXZkqqJkjqqXIvzP5ezRZjW+lPWjw35UX/3EhUPFYbg5ER4JYgDw4007/DQ==
  dependencies:
    estraverse "^4.1.0"

estraverse@^4.1.0, estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.1.0.tgz"
  integrity sha512-FyohXK+R0vE+y1nHLoBM7ZTyqRpqAlhdZHCWIWEviFLiGB8b04H6bQs8G+XTthacvT8VuwvteiP7RJSxMs8UEw==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-emitter@^0.3.5, event-emitter@~0.3.5:
  version "0.3.5"
  resolved "https://registry.npmmirror.com/event-emitter/-/event-emitter-0.3.5.tgz"
  integrity sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA==
  dependencies:
    d "1"
    es5-ext "~0.10.14"

event-pubsub@4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/event-pubsub/-/event-pubsub-4.3.0.tgz"
  integrity sha512-z7IyloorXvKbFx9Bpie2+vMJKKx1fH1EN5yiTfp8CiLOTptSYy1g8H4yDpGlEdshL1PBiFtBHepF2cNsqeEeFQ==

eventemitter3@^4.0.0:
  version "4.0.4"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.4.tgz"
  integrity sha512-rlaVLnVxtxvoyLsQQFBx53YmXHDxRIzzTLbdfxqi4yocpSjAxXwkU0cScM5JgSKMqEhrZpnvQ2D9gjylR0AimQ==

events@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/events/-/events-3.1.0.tgz"
  integrity sha512-Rv+u8MLHNOdMjTAFeT3nCjHn2aGlx435FP/sDHNaRhDEMwyI/aB22Kj2qIN8R0cw3z28psEQLYwxVKLsKrMgWg==

eventsource@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/eventsource/-/eventsource-1.0.7.tgz"
  integrity sha512-4Ln17+vVT0k8aWq+t/bF5arcS3EpT9gYtW66EPacdj/mAFevznsnyoHLPy2BA8gbIQeIHoPsvwmfBftfcG//BQ==
  dependencies:
    original "^1.0.0"

eventsource@0.1.6:
  version "0.1.6"
  resolved "https://registry.npmjs.org/eventsource/-/eventsource-0.1.6.tgz"
  integrity sha1-Cs7ehJ7X3RzMMsgRuxG5RNTykjI=
  dependencies:
    original ">=0.0.5"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz"
  integrity sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmmirror.com/execa/-/execa-0.7.0.tgz"
  integrity sha512-RztN09XglpYI7aBBrJCPW95jEH7YF1UEPOoX9yDhUTPdp7mK+CQvnLTuD10BNXZ3byLTu2uehZ8EcKT/4CGiFw==
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npmjs.org/execa/-/execa-0.8.0.tgz"
  integrity sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz"
  integrity sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^3.3.0:
  version "3.4.0"
  resolved "https://registry.npmjs.org/execa/-/execa-3.4.0.tgz"
  integrity sha512-r9vdGQk4bmCuK1yKQu1KTwcT2zwfWdbdaXfCtAh+5nU/4fSX+JAb7vZGvI5naJrQlvONrEB20jeruESI69530g==
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    p-finally "^2.0.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

express@^4.16.2, express@^4.16.3, express@^4.17.1:
  version "4.17.1"
  resolved "https://registry.npmjs.org/express/-/express-4.17.1.tgz"
  integrity sha512-mHJ9O79RqluphRrcw2X/GTh3k9tVv8YcoyY4Kkh4WDMUYKRZUq0h1o0w2rrrxBqM7VoeUVqgb27xlEMXTnYt4g==
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

ext@^1.1.2:
  version "1.7.0"
  resolved "https://registry.npmmirror.com/ext/-/ext-1.7.0.tgz"
  integrity sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==
  dependencies:
    type "^2.7.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==

external-editor@^2.0.4:
  version "2.2.0"
  resolved "https://registry.npmjs.org/external-editor/-/external-editor-2.2.0.tgz"
  integrity sha512-bSn6gvGxKt+b7+6TKEv1ZycHleA7aHhRHyAqJyp5pbUFuYYNIzpZnQDk7AsYckyWdEnTeAnay0aCy2aV6iTk9A==
  dependencies:
    chardet "^0.4.0"
    iconv-lite "^0.4.17"
    tmp "^0.0.33"

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz"
  integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz"
  integrity sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extract-text-webpack-plugin@^3.0.1:
  version "3.0.2"
  resolved "https://registry.npmjs.org/extract-text-webpack-plugin/-/extract-text-webpack-plugin-3.0.2.tgz"
  integrity sha512-bt/LZ4m5Rqt/Crl2HiKuAl/oqg0psx1tsTLkvWbJen1CtD+fftkZhMaQ9HOtY2gWsl2Wq+sABmMVi9z3DhKWQQ==
  dependencies:
    async "^2.4.1"
    loader-utils "^1.1.0"
    schema-utils "^0.3.0"
    webpack-sources "^1.0.1"

extsprintf@^1.2.0, extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

fast-deep-equal@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz"
  integrity sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-glob@^2.2.6:
  version "2.2.7"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-2.2.7.tgz"
  integrity sha512-g1KuQwHOZAmOZMuBtHdxDtju+T2RT8jgCC9aANsbpdiDDTSnjgfuVsIBNKbUeJI3oKMRExcfNDtJl4OhbffMsw==
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastest-levenshtein@^1.0.12:
  version "1.0.16"
  resolved "https://registry.npmmirror.com/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz"
  integrity sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==

fastparse@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/fastparse/-/fastparse-1.1.2.tgz"
  integrity sha512-483XLLxTVIwWK3QTrMGRqUfUpoOs/0hbQrl2oz4J0pAcm3A3bu84wxTFqGqkJzewCLdME38xJLJAxBABfQT8sQ==

faye-websocket@^0.10.0:
  version "0.10.0"
  resolved "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.10.0.tgz"
  integrity sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.0:
  version "0.11.3"
  resolved "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.3.tgz"
  integrity sha512-D2y4bovYpzziGgbHYtGCMjlJM36vAl/y+xUyn1C+FVx8szd1E+86KwVw6XvYSzOP8iMpm1X0I4xJD+QtUb36OA==
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.1:
  version "0.11.3"
  resolved "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.3.tgz"
  integrity sha512-D2y4bovYpzziGgbHYtGCMjlJM36vAl/y+xUyn1C+FVx8szd1E+86KwVw6XvYSzOP8iMpm1X0I4xJD+QtUb36OA==
  dependencies:
    websocket-driver ">=0.5.1"

fecha@~2.3.3:
  version "2.3.3"
  resolved "https://registry.npmjs.org/fecha/-/fecha-2.3.3.tgz"
  integrity sha512-lUGBnIamTAwk4znq5BcqsDaxSmZ9nDVJaij6NvRt/Tg4R69gERA+otPKbS86ROw9nxVMw2/mp1fnaiWqbs6Sdg==

figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "https://registry.npmjs.org/figgy-pudding/-/figgy-pudding-3.5.2.tgz"
  integrity sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw==

figures@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/figures/-/figures-2.0.0.tgz"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-2.0.0.tgz"
  integrity sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E=
  dependencies:
    flat-cache "^1.2.1"
    object-assign "^4.0.1"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-5.0.1.tgz"
  integrity sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g==
  dependencies:
    flat-cache "^2.0.1"

file-loader@^1.1.4:
  version "1.1.11"
  resolved "https://registry.npmjs.org/file-loader/-/file-loader-1.1.11.tgz"
  integrity sha512-TGR4HU7HUsGg6GCOPJnFk06RhWgEWFLAGWiT6rcD+GRC2keU3s9RGJ+b3Z6/U73jwwNb2gKLJ7YCrp+jvU4ALg==
  dependencies:
    loader-utils "^1.0.2"
    schema-utils "^0.4.5"

file-loader@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/file-loader/-/file-loader-3.0.1.tgz"
  integrity sha512-4sNIOXgtH/9WZq4NvlfU3Opn5ynUsqBwSLyM+I7UOwdGigTBYfVVQEwe/msZNX/j4pCJTIM14Fsw66Svo1oVrw==
  dependencies:
    loader-utils "^1.0.2"
    schema-utils "^1.0.0"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz"
  integrity sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==

filesize@^3.6.1:
  version "3.6.1"
  resolved "https://registry.npmjs.org/filesize/-/filesize-3.6.1.tgz"
  integrity sha512-7KjR1vv6qnicaPMi1iiTcI85CyYwRO/PSFCu6SvqL8jN2Wjt/NIYQTFtFs7fSDCYOstUkEWIQGFUg5YZQfjlcg==

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz"
  integrity sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-babel-config@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/find-babel-config/-/find-babel-config-1.2.0.tgz"
  integrity sha512-jB2CHJeqy6a820ssiqwrKMeyC6nNdmrcgkKWJWmpoxpE8RKciYJXCcXRq1h2AzCo5I5BJeN2tkGEO3hLTuePRA==
  dependencies:
    json5 "^0.5.1"
    path-exists "^3.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-0.1.1.tgz"
  integrity sha1-yN765XyKUqinhPnjHFfHQumToLk=
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-cache-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-1.0.0.tgz"
  integrity sha1-kojj6ePMN0hxfTnq3hfPcfww7m8=
  dependencies:
    commondir "^1.0.1"
    make-dir "^1.0.0"
    pkg-dir "^2.0.0"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-2.1.0.tgz"
  integrity sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-cache-dir@^3.0.0:
  version "3.3.1"
  resolved "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-3.3.1.tgz"
  integrity sha512-t2GDMt3oGC/v+BMwzmllWDuJF/xcDtE5j/fCGbqDD7OLuJkj0cfh1YSA5VKPvwMeLFLNDBkwOKZ2X85jGLVftQ==
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/find-up/-/find-up-1.1.2.tgz"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz"
  integrity sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^1.2.1:
  version "1.3.4"
  resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-1.3.4.tgz"
  integrity sha512-VwyB3Lkgacfik2vhqR4uv2rvebqmDvFu4jlN/C1RzWoJEo8I7z4Q404oiqYCkq41mni8EzQnm95emU9seckwtg==
  dependencies:
    circular-json "^0.3.1"
    graceful-fs "^4.1.2"
    rimraf "~2.6.2"
    write "^0.2.1"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-2.0.1.tgz"
  integrity sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA==
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/flatted/-/flatted-2.0.2.tgz"
  integrity sha512-r5wGx7YeOwNWNlCA0wQ86zKyDLMQr+/RB8xy74M4hTphfmjlijTSSXGuH8rnvKZnfT9i+75zmd8jcKdMR4O6jA==

flatten@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/flatten/-/flatten-1.0.3.tgz"
  integrity sha512-dVsPA/UwQ8+2uoFe5GHtiBMu48dWLTdsuEd7CKGlZlD78r1TTWBvDuFaFGKCo/ZfEr95Uk56vZoX86OsHkUeIg==

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/flush-write-stream/-/flush-write-stream-1.1.1.tgz"
  integrity sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w==
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

fmin@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/fmin/-/fmin-0.0.2.tgz"
  integrity sha1-Wbu0DUP/3ByUzQClaMQflfGXMBc=
  dependencies:
    contour_plot "^0.0.1"
    json2module "^0.0.3"
    rollup "^0.25.8"
    tape "^4.5.1"
    uglify-js "^2.6.2"

follow-redirects@^1.0.0, follow-redirects@1.5.10:
  version "1.5.10"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.10.tgz"
  integrity sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==
  dependencies:
    debug "=3.1.0"

for-each@~0.3.3:
  version "0.3.3"
  resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz"
  integrity sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/forwarded/-/forwarded-0.1.2.tgz"
  integrity sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/from2/-/from2-2.3.0.tgz"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-7.0.1.tgz"
  integrity sha512-YJDaCJZEnBmcbw13fvdAM9AwNOJwOzrE4pqMqBq5nFiEqXUqHwlK4B+3pUw6JNvfSPtX05xFHtYy/1ni01eGCw==
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz"
  integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
  dependencies:
    minipass "^3.0.0"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "https://registry.npmjs.org/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.13"
  resolved "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.13.tgz"
  integrity sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw==
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.1.2:
  version "2.1.3"
  resolved "https://registry.npmjs.org/fsevents/-/fsevents-2.1.3.tgz"
  integrity sha512-Auw9a4AxqWpa9GUfj370BMPzzyncfBABW8Mab7BGWBYDj4Isgq+cDKtx0i6u9jcX9pQDnswsaaOTgTmA5pEjuQ==

fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz"
  integrity sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==

fstream@^1.0.0, fstream@^1.0.12:
  version "1.0.12"
  resolved "https://registry.npmmirror.com/fstream/-/fstream-1.0.12.tgz"
  integrity sha512-WvJ193OHa0GHPEL+AycEJgxvBEwyfRkN1vhjca23OaPVMCaLCXTd5qAu82AjTcgP1UJmytkOKb63Ypde7raDIg==
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.1.1, function-bind@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://registry.npmmirror.com/gauge/-/gauge-2.7.4.tgz"
  integrity sha512-14x4kjc6lkD3ltw589k0NrPD6cCNTD6CWoVUNpB85+DrtONoZn+Rug6xZU5RvSC4+TZPxA5AnBibQYAvZn41Hg==
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gaze@^1.0.0:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/gaze/-/gaze-1.1.3.tgz"
  integrity sha512-BRdNm8hbWzFzWHERTrejLqwHDfS4GibPoq5wjTPIoJHoBtKGPg3xAFfxmM+9ztbXelxcf2hwQcaz1PtmFeue8g==
  dependencies:
    globule "^1.0.0"

gensync@^1.0.0-beta.1:
  version "1.0.0-beta.1"
  resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.1.tgz"
  integrity sha512-r8EC6NO1sngH/zdD9fiRDLdcgnbayXah+mLgManTaIZJqEC1MZstmnox8KpnI2/fxQwrp5OpCOYWLp4rBl4Jcg==

get-caller-file@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.3.tgz"
  integrity sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w==

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/get-stdin/-/get-stdin-4.0.1.tgz"
  integrity sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz"
  integrity sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-5.1.0.tgz"
  integrity sha512-EXr1FOzrzTfGeL0gQdeFEvOMm2mzMOglyiOXSTpPC+iAjAKftbr3jpCMWynogwYnM+eSj9sHGc6wjIcDvYiygw==
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@~5.1.0:
  version "5.1.1"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.1.tgz"
  integrity sha512-FnI+VGOpnlGHWZxthPGR+QhR78fuiK0sNLkHQv+bL9fQi57lNNdquIbna/WrfROrolq8GK5Ek6BiMwqL/voRYQ==
  dependencies:
    is-glob "^4.0.1"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.3.0.tgz"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob@^7.0.0, glob@^7.0.3, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@~7.1.1, glob@~7.1.6:
  version "7.1.6"
  resolved "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz"
  integrity sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^8.0.1:
  version "8.1.0"
  resolved "https://registry.npmmirror.com/glob/-/glob-8.1.0.tgz"
  integrity sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^5.0.1"
    once "^1.3.0"

global@^4.3.0, global@^4.3.1, global@~4.3.0, global@4.3.2:
  version "4.3.2"
  resolved "https://registry.npmjs.org/global/-/global-4.3.2.tgz"
  integrity sha512-/4AybdwIDU4HkCUbJkZdWpe4P6vuw/CUtu+0I1YlLIPe7OlUO7KNJ+q/rO70CW2/NW6Jc6I62++Hzsf5Alu6rQ==
  dependencies:
    min-document "^2.19.0"
    process "~0.5.1"

global@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/global/-/global-4.4.0.tgz"
  integrity sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==
  dependencies:
    min-document "^2.19.0"
    process "^0.11.10"

globals@^11.0.1:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^11.7.0:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^9.18.0:
  version "9.18.0"
  resolved "https://registry.npmjs.org/globals/-/globals-9.18.0.tgz"
  integrity sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ==

globby@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/globby/-/globby-6.1.0.tgz"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/globby/-/globby-7.1.1.tgz"
  integrity sha1-+yzP+UAfhgCUXfral0QMypcrhoA=
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globby@^9.2.0:
  version "9.2.0"
  resolved "https://registry.npmjs.org/globby/-/globby-9.2.0.tgz"
  integrity sha512-ollPHROa5mcxDEkwg6bPt3QbEf4pDQSNtd6JPL1YvOvAo/7/0VAm9TccUeoTmarjPw4pfUthSCqcyfNB1I3ZSg==
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

globule@^1.0.0:
  version "1.3.4"
  resolved "https://registry.npmmirror.com/globule/-/globule-1.3.4.tgz"
  integrity sha512-OPTIfhMBh7JbBYDpa5b+Q5ptmMWKwcNcFSR/0c6t8V4f3ZAVBEsKNY37QdVqmLRYSMhOUGYrY0QhSoEpzGr/Eg==
  dependencies:
    glob "~7.1.1"
    lodash "^4.17.21"
    minimatch "~3.0.2"

good-listener@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/good-listener/-/good-listener-1.2.2.tgz"
  integrity sha1-1TswzfkxPf+33JoNR3CWqm0UXFA=
  dependencies:
    delegate "^3.1.2"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.2:
  version "4.2.4"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.4.tgz"
  integrity sha512-WjKPNJF79dtJAVniUlGGWHYGz2jWxT6VhN/4m1NdkbZ2nOsEF+cI1Edgql5zCRhs/VsQYRvrXctxktVXZUkixw==

graphlib@^2.1.8:
  version "2.1.8"
  resolved "https://registry.npmjs.org/graphlib/-/graphlib-2.1.8.tgz"
  integrity sha512-jcLLfkpoVGmH7/InMC/1hIvOPSUh38oJtGhvrOFGzioE1DZ+0YW16RgmOJhHiuWTvGiJQ9Z1Ik43JvkRPRvE+A==
  dependencies:
    lodash "^4.17.15"

gzip-size@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmjs.org/gzip-size/-/gzip-size-5.1.1.tgz"
  integrity sha512-FNHi6mmoHvs1mxZAds4PpdCS6QG8B4C1krxJsMutgxl5t3+GlRTzzI3NEkifXx2pVsOvJdOGSmIgDhQ55FwdPA==
  dependencies:
    duplexer "^0.1.1"
    pify "^4.0.1"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz"
  integrity sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.3"
  resolved "https://registry.npmjs.org/har-validator/-/har-validator-5.1.3.tgz"
  integrity sha512-sNvOCzEQNr/qrvJgc3UG/kD4QtlHycrzwS+6mfTrrSq97BvaYcPZZI1ZSqGSPR73Cxn4LKTD4PttRwfU7jWq5g==
  dependencies:
    ajv "^6.5.5"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz"
  integrity sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=

has-flag@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/has-flag/-/has-flag-2.0.0.tgz"
  integrity sha512-P+1n3MnwjR/Epg9BBo1KT8qbye2g2Ou4sFumihwt6I4tsUX7jnLcX4BTOSKg/B1ZrIYMN9FcEnG4x5a7NB8Eng==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-symbols@^1.0.0, has-symbols@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.1.tgz"
  integrity sha512-PLcsoqu++dmEIZB+6totNFKq/7Do+Z0u4oT0zKOJNl3lYK6vGwwu2hjHs+68OEZbTjiUE9bgOABXbP/GvrS0Kg==

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/has-unicode/-/has-unicode-2.0.1.tgz"
  integrity sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.0, has@^1.0.1, has@^1.0.3, has@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/hash-base/-/hash-base-3.1.0.tgz"
  integrity sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA==
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/hash-sum/-/hash-sum-1.0.2.tgz"
  integrity sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz"
  integrity sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

he@^1.1.0, he@^1.2.0, he@1.2.x:
  version "1.2.0"
  resolved "https://registry.npmjs.org/he/-/he-1.2.0.tgz"
  integrity sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==

hex-color-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/hex-color-regex/-/hex-color-regex-1.1.0.tgz"
  integrity sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ==

highlight.js@^9.6.0:
  version "9.18.1"
  resolved "https://registry.npmjs.org/highlight.js/-/highlight.js-9.18.1.tgz"
  integrity sha512-OrVKYz70LHsnCgmbXctv/bfuvntIKDz177h0Co37DQ5jamGZLVmoCVMtjMtNZY3X9DrCcKfklHPNeA0uPZhSJg==

hmac-drbg@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/home-or-tmp/-/home-or-tmp-2.0.0.tgz"
  integrity sha1-42w/LSyufXRqhX440Y1fMqeILbg=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

hoopy@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/hoopy/-/hoopy-0.1.4.tgz"
  integrity sha512-HRcs+2mr52W0K+x8RzcLzuPPmVIKMSv97RGHy0Ea9y/mpcaK+xTrjICA04KAHi4GRzxliNqNJEFYWHghy3rSfQ==

hosted-git-info@^2.1.4:
  version "2.8.8"
  resolved "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.8.tgz"
  integrity sha512-f/wzC2QaWBs7t9IYqB4T3sR1xviIViXJRJTWBlx2Gf3g0Xi5vI7Yy4koXQ1c9OYDGHN9sBy1DQ2AB8fqZBWhUg==

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

hsl-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/hsl-regex/-/hsl-regex-1.0.0.tgz"
  integrity sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=

hsla-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/hsla-regex/-/hsla-regex-1.0.0.tgz"
  integrity sha1-wc56MWjIxmFAM6S194d/OyJfnDg=

html-comment-regex@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/html-comment-regex/-/html-comment-regex-1.1.2.tgz"
  integrity sha512-P+M65QY2JQ5Y0G9KKdlDpo0zK+/OHptU5AaBwUfAIDJZk1MYf32Frm84EcOytfJE0t5JvkAnKlmjsXDnWzCJmQ==

html-entities@^1.2.0, html-entities@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/html-entities/-/html-entities-1.3.1.tgz"
  integrity sha512-rhE/4Z3hIhzHAUKbW8jVcCyuT5oJCXXqhN/6mXXVCpzTmvJnoH2HL/bt3EZ6p55jbFJBeAe1ZNpL5BugLujxNA==

html-minifier-terser@^5.0.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/html-minifier-terser/-/html-minifier-terser-5.1.1.tgz"
  integrity sha512-ZPr5MNObqnV/T9akshPKbVgyOqLmy+Bxo7juKCfTfnjNniTAMdy4hz21YQqoofMBJD2kdREaqPPdThoR78Tgxg==
  dependencies:
    camel-case "^4.1.1"
    clean-css "^4.2.3"
    commander "^4.1.1"
    he "^1.2.0"
    param-case "^3.0.3"
    relateurl "^0.2.7"
    terser "^4.6.3"

html-minifier@^3.2.3:
  version "3.5.21"
  resolved "https://registry.npmjs.org/html-minifier/-/html-minifier-3.5.21.tgz"
  integrity sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA==
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.2.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/html-tags/-/html-tags-2.0.0.tgz"
  integrity sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=

html-webpack-plugin@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-3.2.0.tgz"
  integrity sha1-sBq71yOsqqeze2r0SS69oD2d03s=
  dependencies:
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    tapable "^1.0.0"
    toposort "^1.0.0"
    util.promisify "1.0.0"

html-webpack-plugin@^4.2.0, html-webpack-plugin@>=2.26.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-4.3.0.tgz"
  integrity sha512-C0fzKN8yQoVLTelcJxZfJCE+aAvQiY2VUf3UuKrR4a9k5UMWYOtpDLsaXwATbcVCnI05hUS7L9ULQHWLZhyi3w==
  dependencies:
    "@types/html-minifier-terser" "^5.0.0"
    "@types/tapable" "^1.0.5"
    "@types/webpack" "^4.41.8"
    html-minifier-terser "^5.0.1"
    loader-utils "^1.2.3"
    lodash "^4.17.15"
    pretty-error "^2.1.1"
    tapable "^1.1.3"
    util.promisify "1.0.0"

html2canvas@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/html2canvas/-/html2canvas-1.4.1.tgz"
  integrity sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA==
  dependencies:
    css-line-break "^2.1.0"
    text-segmentation "^1.0.3"

htmlparser2@^3.3.0:
  version "3.10.1"
  resolved "https://registry.npmjs.org/htmlparser2/-/htmlparser2-3.10.1.tgz"
  integrity sha512-IgieNijUMbkDovyoKObU1DUhm1iwNYE/fuifEoEHfd1oZKZDaONBSkal7Y01shxsM49R4XaMdGez3WnF9UfiCQ==
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-errors@~1.7.2, http-errors@1.7.2:
  version "1.7.2"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-1.7.2.tgz"
  integrity sha512-uUQBt3H/cSIVfch6i1EuPNy/YsRSOUBXTVfZ+yR7Zjez3qjBz6i9+i4zjNaoqcoFVI4lQJ5plg63TvGfRSDCRg==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-parser-js@>=0.5.1:
  version "0.5.2"
  resolved "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.2.tgz"
  integrity sha512-opCO9ASqg5Wy2FNo7A0sxy71yGbbkJJXLdgMK04Tcypw9jr2MgWbyubb0+WdmDmGnFflO7fRbqbaihh/ENDlRQ==

http-proxy-middleware@^0.19.1:
  version "0.19.2"
  resolved "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-0.19.2.tgz"
  integrity sha512-aYk1rTKqLTus23X3L96LGNCGNgWpG4cG0XoZIT1GUPhhulEHX/QalnO6Vbo+WmKWi4AL2IidjuC0wZtbpg0yhQ==
  dependencies:
    http-proxy "^1.18.1"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy-middleware@0.19.1:
  version "0.19.1"
  resolved "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-0.19.1.tgz"
  integrity sha512-yHYTgWMQO8VvwNS22eLLloAkvungsKdKTLO8AJlftYIKNfJr3GK3zK0ZCfzDDGUBttdGc8xFy1mCitvNKQtC3Q==
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy@^1.17.0, http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz"
  integrity sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/https-browserify/-/https-browserify-1.0.0.tgz"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

human-signals@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/human-signals/-/human-signals-1.1.1.tgz"
  integrity sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw==

iconv-lite@^0.4.17, iconv-lite@^0.4.24, iconv-lite@0.4, iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-replace-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz"
  integrity sha1-Bupvg2ead0njhs/h/oEq5dsiPe0=

icss-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/icss-utils/-/icss-utils-2.1.0.tgz"
  integrity sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI=
  dependencies:
    postcss "^6.0.1"

ieee754@^1.1.4:
  version "1.1.13"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz"
  integrity sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==

iferr@^0.1.5:
  version "0.1.5"
  resolved "https://registry.npmjs.org/iferr/-/iferr-0.1.5.tgz"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^3.3.3, ignore@^3.3.5:
  version "3.3.10"
  resolved "https://registry.npmjs.org/ignore/-/ignore-3.3.10.tgz"
  integrity sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug==

ignore@^4.0.3:
  version "4.0.6"
  resolved "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz"
  integrity sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==

ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz"
  integrity sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==

ignore@^5.0.2:
  version "5.1.8"
  resolved "https://registry.npmjs.org/ignore/-/ignore-5.1.8.tgz"
  integrity sha512-BMpfD7PpiETpBl/A6S498BaIJ6Y/ABT93ETbby2fP00v4EbvPBXWEoaR1UBPKs3iR53pJY7EtZk5KACI57i1Uw==

image-size@~0.5.0:
  version "0.5.5"
  resolved "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz"
  integrity sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/import-cwd/-/import-cwd-2.1.0.tgz"
  integrity sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0:
  version "3.2.1"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.2.1.tgz"
  integrity sha512-6e1q1cnWP2RXD9/keSkxHScg508CdXqXWgWBaETNhyuBFz+kUZlKboh+ISK+bU++DmbHimVBrOz/zzPe0sZ3sQ==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/import-from/-/import-from-2.1.0.tgz"
  integrity sha1-M1238qev/VOqpHHUuAId7ja387E=
  dependencies:
    resolve-from "^3.0.0"

import-local@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/import-local/-/import-local-1.0.0.tgz"
  integrity sha512-vAaZHieK9qjGo58agRBg+bhHX3hoTZU/Oa3GESWLz7t1U62fk63aHuDJJEteXoDeTCcPmUT+z38gkHPZkkmpmQ==
  dependencies:
    pkg-dir "^2.0.0"
    resolve-cwd "^2.0.0"

import-local@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/import-local/-/import-local-2.0.0.tgz"
  integrity sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ==
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

import-local@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/import-local/-/import-local-3.1.0.tgz"
  integrity sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

in-publish@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/in-publish/-/in-publish-2.0.1.tgz"
  integrity sha512-oDM0kUSNFC31ShNxHKUyfZKy8ZeXZBWMjMdZHKLOk13uvT27VTL/QzRGfRUcevJhpkZAvlhPYuXkF7eNWrtyxQ==

indent-string@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/indent-string/-/indent-string-2.1.0.tgz"
  integrity sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=
  dependencies:
    repeating "^2.0.0"

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/indexes-of/-/indexes-of-1.0.1.tgz"
  integrity sha1-8w9xbI4r00bHtn0985FVZqfAVgc=

individual@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/individual/-/individual-2.0.0.tgz"
  integrity sha512-pWt8hBCqJsUWI/HtcfWod7+N9SgAqyPEaF7JQjwzjn5vGrpg6aQ5qeAFQ7dx//UH4J1O+7xqew+gCeeFt6xN/g==

infer-owner@^1.0.3, infer-owner@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/infer-owner/-/infer-owner-1.0.4.tgz"
  integrity sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3, inherits@~2.0.4, inherits@2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inherits@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

inquirer@^3.0.6:
  version "3.3.0"
  resolved "https://registry.npmjs.org/inquirer/-/inquirer-3.3.0.tgz"
  integrity sha512-h+xtnyk4EwKvFWHrUYsWErEVR+igKtLdchu+o0Z1RL7VU/jVMFbYir2bp6bAj8efFNxWqHX0dIss6fJQ+/+qeQ==
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^2.0.4"
    figures "^2.0.0"
    lodash "^4.3.0"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rx-lite "^4.0.8"
    rx-lite-aggregates "^4.0.8"
    string-width "^2.1.0"
    strip-ansi "^4.0.0"
    through "^2.3.6"

inquirer@^6.2.2:
  version "6.5.2"
  resolved "https://registry.npmjs.org/inquirer/-/inquirer-6.5.2.tgz"
  integrity sha512-cntlB5ghuB0iuO65Ovoi8ogLHiWGs/5yNrtUcKjFhSSiVeAIVpD7koaSU9RM8mpXw5YDi9RdYXGQMaOURB7ycQ==
  dependencies:
    ansi-escapes "^3.2.0"
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^2.0.0"
    lodash "^4.17.12"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.4.0"
    string-width "^2.1.0"
    strip-ansi "^5.1.0"
    through "^2.3.6"

internal-ip@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/internal-ip/-/internal-ip-4.3.0.tgz"
  integrity sha512-S1zBo1D6zcsyuC6PMmY5+55YMILQ9av8lotMx447Bq6SAgo/sDK6y6uUKmuYhW7eacnIhFfsPmCNYdDzsnnDCg==
  dependencies:
    default-gateway "^4.2.0"
    ipaddr.js "^1.9.0"

internal-ip@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/internal-ip/-/internal-ip-1.2.0.tgz"
  integrity sha1-rp+/k7mEh4eF1QqN4bNWlWBYz1w=
  dependencies:
    meow "^3.3.0"

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/interpret/-/interpret-1.4.0.tgz"
  integrity sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==

interpret@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/interpret/-/interpret-2.2.0.tgz"
  integrity sha512-Ju0Bz/cEia55xDwUWEa8+olFpCiQoypjnQySseKtmjNrnps3P+xfpUmGr90T7yjlVJmOtybRvPXhKMbHr+fWnw==

intersperse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/intersperse/download/intersperse-1.0.0.tgz"
  integrity sha1-8lYfsc/vn1J3zDNHoiiGtDUaUYE=

invariant@^2.2.2:
  version "2.2.4"
  resolved "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
  integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/invert-kv/-/invert-kv-1.0.0.tgz"
  integrity sha1-EEqOSqym09jNFXqO+L+rLXo//bY=

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/ip-regex/-/ip-regex-2.1.0.tgz"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@^1.1.0, ip@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmjs.org/ip/-/ip-1.1.5.tgz"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ipaddr.js@^1.9.0, ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-absolute-url/-/is-absolute-url-2.1.0.tgz"
  integrity sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=

is-absolute-url@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/is-absolute-url/-/is-absolute-url-3.0.3.tgz"
  integrity sha512-opmNIX7uFnS96NtPmhWQgQx6/NYFgsUXYMllcfzwWKUMwfo8kku1TvE6hkNcH+Q1ts5cMVrsY7j0bxXQDciu9Q==

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz"
  integrity sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==
  dependencies:
    kind-of "^6.0.0"

is-arguments@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-arguments/-/is-arguments-1.0.4.tgz"
  integrity sha512-xPh0Rmt8NE65sNzvyUmWgI1tz3mKq74lGA0mL8LYZcoIzKOzDh6HmrYm3d18k60nHerC8A9Km8kYu87zfSFnLA==

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==

is-buffer@^2.0.2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.4.tgz"
  integrity sha512-Kq1rokWXOPXWuaMAqZiJW4XxsmD9zGx9q4aePabbn3qCRGedtH7Cm+zV8WETitMfu1wdh+Rvd6w5egwSngUX2A==

is-buffer@~1.1.1:
  version "1.1.6"
  resolved "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.0.tgz"
  integrity sha512-pyVD9AaGLxtg6srb2Ng6ynWJqkHU9bEM087AKck0w8QwDarTfNcpIYoU8x8Hv2Icm8u6kFJM18Dag8lyqGkviw==

is-ci@^1.0.10:
  version "1.2.1"
  resolved "https://registry.npmjs.org/is-ci/-/is-ci-1.2.1.tgz"
  integrity sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg==
  dependencies:
    ci-info "^1.5.0"

is-color-stop@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-color-stop/-/is-color-stop-1.1.0.tgz"
  integrity sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=
  dependencies:
    css-color-names "^0.0.4"
    hex-color-regex "^1.1.0"
    hsl-regex "^1.0.0"
    hsla-regex "^1.0.0"
    rgb-regex "^1.0.1"
    rgba-regex "^1.0.0"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz"
  integrity sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.2.tgz"
  integrity sha512-USlDT524woQ08aoZFzh3/Z6ch9Y/EWXEHQ/AaRN0SkKq4t2Jw2R2339tSXmwuVoY7LLlBCbOIlx2myP/L5zk0g==

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz"
  integrity sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg==
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz"
  integrity sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz"
  integrity sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-docker@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-docker/-/is-docker-2.0.0.tgz"
  integrity sha512-pJEdRugimx4fBMra5z2/5iRdZ63OhYV0vr0Dwm5+xtW4D1FvRkB8hamMIhnWfyJeDdyr/aa7BDyNbtG38VxgoQ==

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz"
  integrity sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finite@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-finite/-/is-finite-1.1.0.tgz"
  integrity sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w==

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-function@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-function/-/is-function-1.0.2.tgz"
  integrity sha512-lw7DUp0aWXYg+CBCN+JKkcE0Q2RayZnSvnZBlwgxHBQhqt5pZNVy4Ri7H9GmmXkdu7LUthszM+Tor1u/2iBcpQ==

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz"
  integrity sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg==
  dependencies:
    is-extglob "^2.1.1"

is-mobile@^2.2.1:
  version "2.2.2"
  resolved "https://registry.npm.taobao.org/is-mobile/download/is-mobile-2.2.2.tgz"
  integrity sha1-9snF1Q7gElTOBec5vdg18e1OmVQ=

is-negative-zero@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/is-negative-zero/download/is-negative-zero-2.0.1.tgz"
  integrity sha1-PedGwY3aIxkkGlNnWQjY92bxHCQ=

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-obj/-/is-obj-2.0.0.tgz"
  integrity sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz"
  integrity sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-2.2.0.tgz"
  integrity sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==

is-path-in-cwd@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz"
  integrity sha512-FjV1RTW48E7CWM7eE/J2NJvAEEVektecDBVBE5Hh3nM1Jd0kvhHtX68Pr3xsDf857xt3Y4AkwVULK1Vku62aaQ==
  dependencies:
    is-path-inside "^1.0.0"

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz"
  integrity sha512-rNocXHgipO+rvnP6dk3zI20RpOtrAM/kzbB258Uw5BWr3TpXi861yzjo16Dn4hUox07iw5AyeMLHWsujkjzvRQ==
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.1.tgz"
  integrity sha1-jvW33lBDej/cprToZe96pVy0gDY=
  dependencies:
    path-is-inside "^1.0.1"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-2.1.0.tgz"
  integrity sha512-wiyhTzfDWsvwAW53OBWF5zuvaOGlZ6PwYxAbPVDhpm+gM09xKQGjBq/8uYN12aDvMxnAnq3dxTyoSoRNmg5YFg==
  dependencies:
    path-is-inside "^1.0.2"

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz"
  integrity sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==
  dependencies:
    isobject "^3.0.1"

is-regex@^1.0.4, is-regex@~1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.0.5.tgz"
  integrity sha512-vlKW17SNq44owv5AQR3Cq0bQPEb8+kF3UKZ2fiZNOWtztYE5i0CzCZxFDwO58qAOWtxdBRVO/V5Qin1wjCqFYQ==
  dependencies:
    has "^1.0.3"

is-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.1.0.tgz"
  integrity sha512-iI97M8KTWID2la5uYXlkbSDQIg4F6o1sYboZKKTDpnDQMLtUL86zxhgDet3Q2SriaYsyGqZ6Mn2SjbRKeLHdqw==
  dependencies:
    has-symbols "^1.0.1"

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.1.0.tgz"
  integrity sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg==

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.0.tgz"
  integrity sha512-XCoy+WlUr7d1+Z8GgSuXmpuUFC9fOhRXglJMx+dwLKTkL44Cjd4W1Z5P+BQZpr+cR93aGP4S/s7Ftw6Nd/kiEw==

is-string@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/is-string/-/is-string-1.0.5.tgz"
  integrity sha512-buY6VNRjhQMiF1qWDouloZlQbRhDPCebwxSjxMjxgemYT46YMd2NR0/H+fBhEfWX4A/w9TBJ+ol+okqJKFE6vQ==

is-svg@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-svg/-/is-svg-2.1.0.tgz"
  integrity sha1-z2EJDaDZ77yrhyLeum8DIgjbsOk=
  dependencies:
    html-comment-regex "^1.1.0"

is-svg@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-svg/-/is-svg-3.0.0.tgz"
  integrity sha512-gi4iHK53LR2ujhLVVj+37Ykh9GLqYHX6JOVXbLAucaG/Cqw9xwdFOjDM2qeifLs1sF1npXXFvDu0r5HNgCMrzQ==
  dependencies:
    html-comment-regex "^1.1.0"

is-symbol@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.3.tgz"
  integrity sha512-OwijhaRSgqvhm/0ZdAcXNZt9lYdKFpcRDT5ULUuYXPoT794UNOdU+gpT6Rzo7b4V2HUl/op6GqY894AZwv9faQ==
  dependencies:
    has-symbols "^1.0.1"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz"
  integrity sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz"
  integrity sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==
  dependencies:
    is-docker "^2.0.0"

isarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@~0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

ismobilejs@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/ismobilejs/download/ismobilejs-1.1.1.tgz"
  integrity sha1-xWygro5Sskyg8iul7zIVot27qg4=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

javascript-stringify@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/javascript-stringify/-/javascript-stringify-1.6.0.tgz"
  integrity sha1-FC0RHzpuPa6PSpr9d9RYVbWpzOM=

js-base64@^2.1.8, js-base64@^2.1.9:
  version "2.6.3"
  resolved "https://registry.npmjs.org/js-base64/-/js-base64-2.6.3.tgz"
  integrity sha512-fiUvdfCaAXoQTHdKMgTvg6IkecXDcVz6V5rlftUTclF9IKBjMizvSdQaCl/z/6TApDeby5NL+axYou3i0mu1Pg==

js-binary-schema-parser@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/js-binary-schema-parser/-/js-binary-schema-parser-2.0.3.tgz"
  integrity sha512-xezGJmOb4lk/M1ZZLTR/jaBHQ4gG/lqQnJqdIv4721DMggsa1bDVlHXNeHYogaIEHD9vCRv0fcL4hMA+Coarkg==

js-cookie@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/js-cookie/-/js-cookie-2.2.1.tgz"
  integrity sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==

js-levenshtein@^1.1.3:
  version "1.1.6"
  resolved "https://registry.npmjs.org/js-levenshtein/-/js-levenshtein-1.1.6.tgz"
  integrity sha512-X2BB11YZtrRqY4EnQcLX5Rh373zbK4alC1FW7D7MBhL2gtcC17cTnr6DmfHZeS0s2rTHjUTMMHfG7gO8SSdw+g==

js-message@1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/js-message/-/js-message-1.0.5.tgz"
  integrity sha1-IwDSSxrwjondCVvBpMnJz8uJLRU=

js-queue@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/js-queue/-/js-queue-2.0.0.tgz"
  integrity sha1-NiITz4YPRo8BJfxslqvBdCUx+Ug=
  dependencies:
    easy-stack "^1.0.0"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

js-yaml@^3.13.0:
  version "3.14.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.0.tgz"
  integrity sha512-/4IbIeHcD9VMHFqDR/gQ7EdZdLimOvW2DdcxFjdyyZ9NsbS+ccrXqVWDtab/lRl5AlUqmpBx8EhPaWR+OtY17A==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^3.13.1:
  version "3.14.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.0.tgz"
  integrity sha512-/4IbIeHcD9VMHFqDR/gQ7EdZdLimOvW2DdcxFjdyyZ9NsbS+ccrXqVWDtab/lRl5AlUqmpBx8EhPaWR+OtY17A==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^3.4.3, js-yaml@~3.7.0:
  version "3.7.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.7.0.tgz"
  integrity sha1-XJZ93YN6m/3KXy3oQlOr6KHAO4A=
  dependencies:
    argparse "^1.0.7"
    esprima "^2.6.0"

js-yaml@^3.9.1:
  version "3.14.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.0.tgz"
  integrity sha512-/4IbIeHcD9VMHFqDR/gQ7EdZdLimOvW2DdcxFjdyyZ9NsbS+ccrXqVWDtab/lRl5AlUqmpBx8EhPaWR+OtY17A==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-1.3.0.tgz"
  integrity sha1-RsP+yMGJKxKwgz25vHYiF226s0s=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
  integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-loader@^0.5.4:
  version "0.5.7"
  resolved "https://registry.npmmirror.com/json-loader/-/json-loader-0.5.7.tgz"
  integrity sha512-QLPs8Dj7lnf3e3QYS1zkCo+4ZwqOiF9d/nZnYozTISxXWCfNs9yuky5rJw4/W34s7POaNlbZmQGaB5NiXCbP4w==

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  integrity sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==

json-schema-traverse@^0.3.0:
  version "0.3.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz"
  integrity sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json2module@^0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/json2module/-/json2module-0.0.3.tgz"
  integrity sha1-APtfSpt638PwZHwpyxe80Zeb6bI=
  dependencies:
    rw "^1.3.2"

json2mq@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/json2mq/download/json2mq-0.2.0.tgz"
  integrity sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=
  dependencies:
    string-convert "^0.2.0"

json3@^3.3.2:
  version "3.3.3"
  resolved "https://registry.npmjs.org/json3/-/json3-3.3.3.tgz"
  integrity sha512-c7/8mbUsKigAbLkD5B010BK4D9LZm7A1pNItkEwiUZRpIN66exu/e7YQWysGun+TRKaJp8MhemM+VkfWv42aCA==

json5@^0.5.0:
  version "0.5.1"
  resolved "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz"
  integrity sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2:
  version "2.1.3"
  resolved "https://registry.npmjs.org/json5/-/json5-2.1.3.tgz"
  integrity sha512-KXPvOm8K9IJKFM0bmdn8QXh7udDh1g/giieX0NLCaMnb4hEiVFqnop2ImTXCc5e0/oHz3LTqmHGtExn5hfMkOA==
  dependencies:
    minimist "^1.2.5"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsprim@^1.2.2:
  version "1.4.1"
  resolved "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

killable@^1.0.0, killable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/killable/-/killable-1.0.1.tgz"
  integrity sha512-LzqtLKlUwirEUyl/nicirVmNiPvYs7l5n8wOPP7fyJVpUPkvCnW/vuiXGpylGUlnPDnB7311rARzAt3Mhswpjg==

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz"
  integrity sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
  integrity sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==

launch-editor-middleware@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/launch-editor-middleware/-/launch-editor-middleware-2.2.1.tgz"
  integrity sha512-s0UO2/gEGiCgei3/2UN3SMuUj1phjQN8lcpnvgLSz26fAzNWPQ6Nf/kF5IFClnfU2ehp6LrmKdMU/beveO+2jg==
  dependencies:
    launch-editor "^2.2.1"

launch-editor@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/launch-editor/-/launch-editor-2.2.1.tgz"
  integrity sha512-On+V7K2uZK6wK7x691ycSUbLD/FyKKelArkbaAMSSJU8JmqmhwN2+mnJDNINuJWSrh2L0kDk+ZQtbC/gOWUwLw==
  dependencies:
    chalk "^2.3.0"
    shell-quote "^1.6.1"

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz"
  integrity sha1-odePw6UEdMuAhF07O24dpJpEbo4=

lcid@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/lcid/-/lcid-1.0.0.tgz"
  integrity sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=
  dependencies:
    invert-kv "^1.0.0"

less-loader@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/less-loader/-/less-loader-4.1.0.tgz"
  integrity sha512-KNTsgCE9tMOM70+ddxp9yyt9iHqgmSs0yTZc5XH5Wo+g80RWRIYNqE58QJKm/yMud5wZEvz50ugRDuzVIkyahg==
  dependencies:
    clone "^2.1.1"
    loader-utils "^1.1.0"
    pify "^3.0.0"

"less@^2.3.1 || ^3.0.0", less@^3.9.0:
  version "3.11.3"
  resolved "https://registry.npmjs.org/less/-/less-3.11.3.tgz"
  integrity sha512-VkZiTDdtNEzXA3LgjQiC3D7/ejleBPFVvq+aRI9mIj+Zhmif5TvFPM244bT4rzkvOCvJ9q4zAztok1M7Nygagw==
  dependencies:
    clone "^2.1.2"
    tslib "^1.10.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    promise "^7.1.1"
    request "^2.83.0"
    source-map "~0.6.0"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.1.6.tgz"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/load-json-file/-/load-json-file-1.1.0.tgz"
  integrity sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/load-json-file/-/load-json-file-2.0.0.tgz"
  integrity sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

loader-fs-cache@^1.0.0:
  version "1.0.3"
  resolved "https://registry.npmjs.org/loader-fs-cache/-/loader-fs-cache-1.0.3.tgz"
  integrity sha512-ldcgZpjNJj71n+2Mf6yetz+c9bM4xpKtNds4LbqXzU/PTdeAX0g3ytnU1AJMEcTk2Lex4Smpe3Q/eCTsvUBxbA==
  dependencies:
    find-cache-dir "^0.1.1"
    mkdirp "^0.5.1"

loader-runner@^2.3.0, loader-runner@^2.3.1, loader-runner@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/loader-runner/-/loader-runner-2.4.0.tgz"
  integrity sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw==

loader-utils@^0.2.11:
  version "0.2.17"
  resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-0.2.17.tgz"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-0.2.17.tgz"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.2.3, loader-utils@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.0.tgz"
  integrity sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-2.0.0.tgz"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz"
  integrity sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

lodash._baseassign@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/lodash._baseassign/-/lodash._baseassign-3.2.0.tgz"
  integrity sha1-jDigmVAPIVrQnlnxci/QxSv+Ck4=
  dependencies:
    lodash._basecopy "^3.0.0"
    lodash.keys "^3.0.0"

lodash._basecopy@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/lodash._basecopy/-/lodash._basecopy-3.0.1.tgz"
  integrity sha1-jaDmqHbPNEwK2KVIghEd08XHyjY=

lodash._bindcallback@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/lodash._bindcallback/-/lodash._bindcallback-3.0.1.tgz"
  integrity sha1-5THCdkTPi1epnhftlbNcdIeJOS4=

lodash._createassigner@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/lodash._createassigner/-/lodash._createassigner-3.1.1.tgz"
  integrity sha1-g4pbri/aymOsIt7o4Z+k5taXCxE=
  dependencies:
    lodash._bindcallback "^3.0.0"
    lodash._isiterateecall "^3.0.0"
    lodash.restparam "^3.0.0"

lodash._getnative@^3.0.0:
  version "3.9.1"
  resolved "https://registry.npmjs.org/lodash._getnative/-/lodash._getnative-3.9.1.tgz"
  integrity sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U=

lodash._isiterateecall@^3.0.0:
  version "3.0.9"
  resolved "https://registry.npmjs.org/lodash._isiterateecall/-/lodash._isiterateecall-3.0.9.tgz"
  integrity sha1-UgOte6Ql+uhCRg5pbbnPPmqsBXw=

lodash.assign@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/lodash.assign/-/lodash.assign-3.2.0.tgz"
  integrity sha1-POnwI0tLIiPilrj6CsH+6OvKZPo=
  dependencies:
    lodash._baseassign "^3.0.0"
    lodash._createassigner "^3.0.0"
    lodash.keys "^3.0.0"

lodash.assign@^4.0.1:
  version "4.2.0"
  resolved "https://registry.npmjs.org/lodash.assign/-/lodash.assign-4.2.0.tgz"
  integrity sha1-DZnzzNem0mHRm9rrkkUAXShYCOc=

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.defaults@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-3.1.2.tgz"
  integrity sha1-xzCLGNv4vJNy1wGnNJPGEZK9Liw=
  dependencies:
    lodash.assign "^3.0.0"
    lodash.restparam "^3.0.0"

lodash.defaults@^4.0.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-4.2.0.tgz"
  integrity sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=

lodash.defaultsdeep@^4.6.1:
  version "4.6.1"
  resolved "https://registry.npmjs.org/lodash.defaultsdeep/-/lodash.defaultsdeep-4.6.1.tgz"
  integrity sha512-3j8wdDzYuWO3lM3Reg03MuQR957t287Rpcxp1njpEa8oDrikb+FwGdW3n+FELh/A6qib6yPit0j/pv9G/yeAqA==

lodash.find@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/lodash.find/-/lodash.find-4.6.0.tgz"
  integrity sha1-ywcE1Hq3F4n/oN6Ll92Sb7iLE7E=

lodash.get@^4.4.2:
  version "4.4.2"
  resolved "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.2.tgz"
  integrity sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=

lodash.isarguments@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz"
  integrity sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=

lodash.isarray@^3.0.0:
  version "3.0.4"
  resolved "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-3.0.4.tgz"
  integrity sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U=

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash.keys@^3.0.0:
  version "3.1.2"
  resolved "https://registry.npmjs.org/lodash.keys/-/lodash.keys-3.1.2.tgz"
  integrity sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=
  dependencies:
    lodash._getnative "^3.0.0"
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"

lodash.mapvalues@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/lodash.mapvalues/-/lodash.mapvalues-4.6.0.tgz"
  integrity sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.pick@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/lodash.pick/-/lodash.pick-4.4.0.tgz"
  integrity sha1-UvBWEP/53tQiYRRB7R/BI6AwAbM=

lodash.restparam@^3.0.0:
  version "3.6.1"
  resolved "https://registry.npmjs.org/lodash.restparam/-/lodash.restparam-3.6.1.tgz"
  integrity sha1-k2pOMJ7zMKdkXtQUWYbIWuWyCAU=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npm.taobao.org/lodash.toarray/download/lodash.toarray-4.4.0.tgz"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash.transform@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/lodash.transform/-/lodash.transform-4.6.0.tgz"
  integrity sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.0.0, lodash@^4.17.11, lodash@^4.17.12, lodash@^4.17.13, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.3, lodash@^4.17.4, lodash@^4.17.5, lodash@^4.3.0:
  version "4.17.19"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.19.tgz"
  integrity sha512-JNvd8XER9GQX0v2qJgsaN/mzFCNA5BRe/j8JN9d+tWyGLSodKQHKFicdwNYzWwI3wjRnaKPsGj1XkBjx/F96DQ==

lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/log-symbols/-/log-symbols-2.2.0.tgz"
  integrity sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==
  dependencies:
    chalk "^2.0.1"

loglevel@^1.4.1, loglevel@^1.6.8:
  version "1.6.8"
  resolved "https://registry.npmjs.org/loglevel/-/loglevel-1.6.8.tgz"
  integrity sha512-bsU7+gc9AJ2SqpzxwU3+1fedl8zAntbtC5XYlt3s2j1hJcn2PsXSmgN8TaLG/J1/2mod4+cE/3vNL70/c1RNCA==

longest@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz"
  integrity sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/loud-rejection/-/loud-rejection-1.6.0.tgz"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "https://registry.npmjs.org/lower-case/-/lower-case-1.1.4.tgz"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lower-case@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/lower-case/-/lower-case-2.0.1.tgz"
  integrity sha512-LiWgfDLLb1dwbFQZsSglpRj+1ctGnayXz3Uv0/WO8n558JycT5fg6zkNcnW0G68Nn0aEldTFeEfmjCfmqry/rQ==
  dependencies:
    tslib "^1.10.0"

lru-cache@^4.0.1, lru-cache@^4.1.1, lru-cache@^4.1.2:
  version "4.1.5"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz"
  integrity sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

m3u8-parser@2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/m3u8-parser/-/m3u8-parser-2.1.0.tgz"
  integrity sha512-WbEpQ2FUaNGbJ0YanSeyj9D9ruu4FUvz+ZvebIzI2bSME+PUwcPXO1kKXZkjcPUAFruDikoOI5fWQNIA6JCCOQ==

make-dir@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-1.3.0.tgz"
  integrity sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==
  dependencies:
    pify "^3.0.0"

make-dir@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz"
  integrity sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz"
  integrity sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

math-expression-evaluator@^1.2.14:
  version "1.2.22"
  resolved "https://registry.npmjs.org/math-expression-evaluator/-/math-expression-evaluator-1.2.22.tgz"
  integrity sha512-L0j0tFVZBQQLeEjmWOvDLoRciIY8gQGWahvkztXUal8jH8R5Rlqo9GCvgqvXcy9LQhEWdQCVvzqAbxgYNt4blQ==

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://registry.npmjs.org/md5.js/-/md5.js-1.3.5.tgz"
  integrity sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

md5@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/md5/-/md5-2.2.1.tgz"
  integrity sha1-U6s41f48iJG6RlMp6iP6wFQBJvk=
  dependencies:
    charenc "~0.0.1"
    crypt "~0.0.1"
    is-buffer "~1.1.1"

mdn-data@2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.4.tgz"
  integrity sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA==

mdn-data@2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.6.tgz"
  integrity sha512-rQvjv71olwNHgiTbfPZFkJtjNMciWgswYeciZhtvWLO8bmX3TnhyA62I6sTWOyZssWHJJjY6/KiWwqQsWWsqOA==

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

mem@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/mem/-/mem-1.1.0.tgz"
  integrity sha512-nOBDrc/wgpkd3X/JOhMqYR+/eLqlfLP4oQfoBA6QExIxEl+GU01oyEkwWyueyO8110pUKijtiHGhEmYoOn88oQ==
  dependencies:
    mimic-fn "^1.0.0"

memory-fs@^0.4.0, memory-fs@^0.4.1, memory-fs@~0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/memory-fs/-/memory-fs-0.4.1.tgz"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/memory-fs/-/memory-fs-0.5.0.tgz"
  integrity sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA==
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^3.3.0, meow@^3.7.0:
  version "3.7.0"
  resolved "https://registry.npmjs.org/meow/-/meow-3.7.0.tgz"
  integrity sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/merge-source-map/-/merge-source-map-1.1.0.tgz"
  integrity sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==
  dependencies:
    source-map "^0.6.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.2.3:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz"
  integrity sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/miller-rabin/-/miller-rabin-4.0.1.tgz"
  integrity sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

"mime-db@>= 1.43.0 < 2", mime-db@1.44.0:
  version "1.44.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.44.0.tgz"
  integrity sha512-/NOTfLrsPBVeH7YtFPgsVWveuL+4SjjYxaQ1xtM1KMFj7HdxlBlxeyNLzhyJVx7r4rZGJAZ/6lkKCitSc/Nmpg==

mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24:
  version "2.1.27"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.27.tgz"
  integrity sha512-JIhqnCasI9yD+SsmkquHBxTSEuZdQX5BuQnS2Vc7puQQQ+8yiP5AY5uWhpdv4YL4VM5c6iliiYWPgJ/nJQLp7w==
  dependencies:
    mime-db "1.44.0"

mime@^1.4.1, mime@^1.5.0, mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mime@^2.0.3:
  version "2.4.6"
  resolved "https://registry.npmjs.org/mime/-/mime-2.4.6.tgz"
  integrity sha512-RZKhC3EmpBchfTGBVb8fb+RL2cWyw/32lshnsETttkBAyAUXSGHxbEJWWRXc751DrIxG1q04b8QwMbAwkRPpUA==

mime@^2.4.4:
  version "2.4.6"
  resolved "https://registry.npmjs.org/mime/-/mime-2.4.6.tgz"
  integrity sha512-RZKhC3EmpBchfTGBVb8fb+RL2cWyw/32lshnsETttkBAyAUXSGHxbEJWWRXc751DrIxG1q04b8QwMbAwkRPpUA==

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz"
  integrity sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mimic-response@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/mimic-response/-/mimic-response-3.1.0.tgz"
  integrity sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==

min-document@^2.19.0:
  version "2.19.0"
  resolved "https://registry.npmjs.org/min-document/-/min-document-2.19.0.tgz"
  integrity sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==
  dependencies:
    dom-walk "^0.1.0"

mini-css-extract-plugin@^0.8.0:
  version "0.8.2"
  resolved "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-0.8.2.tgz"
  integrity sha512-a3Y4of27Wz+mqK3qrcd3VhYz6cU0iW5x3Sgvqzbj+XmlrSizmvu8QQMl5oMYJjgHOC4iyt+w7l4umP+dQeW3bw==
  dependencies:
    loader-utils "^1.1.0"
    normalize-url "1.9.1"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  integrity sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==

minimalistic-crypto-utils@^1.0.0, minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.2, minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz"
  integrity sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "https://registry.npmmirror.com/minimatch/-/minimatch-5.1.6.tgz"
  integrity sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.1.3, minimist@^1.2.0, minimist@^1.2.5, minimist@~1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz"
  integrity sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw==

minipass-collect@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/minipass-collect/-/minipass-collect-1.0.2.tgz"
  integrity sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==
  dependencies:
    minipass "^3.0.0"

minipass-flush@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/minipass-flush/-/minipass-flush-1.0.5.tgz"
  integrity sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==
  dependencies:
    minipass "^3.0.0"

minipass-pipeline@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/minipass-pipeline/-/minipass-pipeline-1.2.3.tgz"
  integrity sha512-cFOknTvng5vqnwOpDsZTWhNll6Jf8o2x+/diplafmxpuIymAjzoOolZG0VvQf3V2HgqzJNhnuKHYp2BqDgz8IQ==
  dependencies:
    minipass "^3.0.0"

minipass@^3.0.0, minipass@^3.1.1:
  version "3.1.3"
  resolved "https://registry.npmjs.org/minipass/-/minipass-3.1.3.tgz"
  integrity sha512-Mgd2GdMVzY+x3IJ+oHnVM+KG3lA5c8tnabyJKmHSaG2kAGpudxuOf8ToDkhumF7UzME7DecbQE9uOZhNm7PuJg==
  dependencies:
    yallist "^4.0.0"

mississippi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/mississippi/-/mississippi-2.0.0.tgz"
  integrity sha512-zHo8v+otD1J10j/tC+VNoGK9keCuByhKovAvdn74dmxJl9+mWHnx6EMsDN4lgRoMI/eYo2nchAxniIbUPb5onw==
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^2.0.1"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mississippi@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/mississippi/-/mississippi-3.0.0.tgz"
  integrity sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA==
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.npmjs.org/mixin-deep/-/mixin-deep-1.3.2.tgz"
  integrity sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@^0.5.3, "mkdirp@>=0.5 0", mkdirp@~0.5.0, mkdirp@~0.5.1:
  version "0.5.5"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz"
  integrity sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ==
  dependencies:
    minimist "^1.2.5"

moment@^2.21.0:
  version "2.29.1"
  resolved "https://registry.npm.taobao.org/moment/download/moment-2.29.1.tgz"
  integrity sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M=

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/move-concurrently/-/move-concurrently-1.0.1.tgz"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz"
  integrity sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg==

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://registry.npmjs.org/multicast-dns/-/multicast-dns-6.2.3.tgz"
  integrity sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g==
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mutationobserver-shim@^0.3.2:
  version "0.3.7"
  resolved "https://registry.npm.taobao.org/mutationobserver-shim/download/mutationobserver-shim-0.3.7.tgz"
  integrity sha1-i/YzsMCwKRoRByVe0ywTCIqMW/M=

mute-stream@0.0.7:
  version "0.0.7"
  resolved "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.7.tgz"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

mux.js@4.3.2:
  version "4.3.2"
  resolved "https://registry.npmjs.org/mux.js/-/mux.js-4.3.2.tgz"
  integrity sha512-g0q6DPdvb3yYcoK7ElBGobdSSrhY/RjPt19U7uUc733aqvc5bCS/aCvL9z+448y+IoCZnYDwyZfQBBXMSmGOaQ==

mz@^2.4.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.12.1, nan@^2.13.2:
  version "2.17.0"
  resolved "https://registry.npmmirror.com/nan/-/nan-2.17.0.tgz"
  integrity sha512-2ZTgtl0nJsO0KQCjEpxcIr5D+Yv90plTitZt9JBfQvVJDS5seMl3FOvsh3+9CoYWXf/1l5OaZzzF6nDm4cagaQ==

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.13.tgz"
  integrity sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

nanopop@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/nanopop/download/nanopop-2.1.0.tgz"
  integrity sha1-I0dlE87iQFiIr9LopLVAZrcLnmA=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz"
  integrity sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw==

neo-async@^2.5.0, neo-async@^2.6.0, neo-async@^2.6.1:
  version "2.6.2"
  resolved "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==

next-tick@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/next-tick/-/next-tick-1.1.0.tgz"
  integrity sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ==

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz"
  integrity sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==

no-case@^2.2.0:
  version "2.3.2"
  resolved "https://registry.npmjs.org/no-case/-/no-case-2.3.2.tgz"
  integrity sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ==
  dependencies:
    lower-case "^1.1.1"

no-case@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/no-case/-/no-case-3.0.3.tgz"
  integrity sha512-ehY/mVQCf9BL0gKfsJBvFJen+1V//U+0HQMPrWct40ixE4jnv0bfvxDbWtAHL9EcaPEOJHVVYKoQn1TlZUB8Tw==
  dependencies:
    lower-case "^2.0.1"
    tslib "^1.10.0"

node-emoji@^1.10.0:
  version "1.10.0"
  resolved "https://registry.npm.taobao.org/node-emoji/download/node-emoji-1.10.0.tgz"
  integrity sha1-iIar0l2ce7YYAqZYUj0fjSqJsto=
  dependencies:
    lodash.toarray "^4.4.0"

node-forge@0.9.0:
  version "0.9.0"
  resolved "https://registry.npmjs.org/node-forge/-/node-forge-0.9.0.tgz"
  integrity sha512-7ASaDa3pD+lJ3WvXFsxekJQelBKRpne+GOVbLbtHYdd7pFspyeuJHnWfLplGf3SwKGbfs/aYl5V/JCIaHVUKKQ==

node-gyp@^3.8.0:
  version "3.8.0"
  resolved "https://registry.npmmirror.com/node-gyp/-/node-gyp-3.8.0.tgz"
  integrity sha512-3g8lYefrRRzvGeSowdJKAKyks8oUpLEd/DyPV4eMhVlhJ0aNaZqIrNUIPuEWWTAoPqyFkfGrM67MC69baqn6vA==
  dependencies:
    fstream "^1.0.0"
    glob "^7.0.3"
    graceful-fs "^4.1.2"
    mkdirp "^0.5.0"
    nopt "2 || 3"
    npmlog "0 || 1 || 2 || 3 || 4"
    osenv "0"
    request "^2.87.0"
    rimraf "2"
    semver "~5.3.0"
    tar "^2.0.0"
    which "1"

node-ipc@^9.1.1:
  version "9.1.1"
  resolved "https://registry.npmjs.org/node-ipc/-/node-ipc-9.1.1.tgz"
  integrity sha512-FAyICv0sIRJxVp3GW5fzgaf9jwwRQxAKDJlmNFUL5hOy+W4X/I5AypyHoq0DXXbo9o/gt79gj++4cMr4jVWE/w==
  dependencies:
    event-pubsub "4.3.0"
    js-message "1.0.5"
    js-queue "2.0.0"

node-libs-browser@^2.0.0, node-libs-browser@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/node-libs-browser/-/node-libs-browser-2.2.1.tgz"
  integrity sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-releases@^1.1.58:
  version "1.1.59"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-1.1.59.tgz"
  integrity sha512-H3JrdUczbdiwxN5FuJPyCHnGHIFqQ0wWxo+9j1kAXAzqNMAHlo+4I/sYYxpyK0irQ73HgdiyzD32oqQDcU2Osw==

node-sass@^4.0.0, node-sass@^4.14.1:
  version "4.14.1"
  resolved "https://registry.npmmirror.com/node-sass/-/node-sass-4.14.1.tgz"
  integrity sha512-sjCuOlvGyCJS40R8BscF5vhVlQjNN069NtQ1gSxyK1u9iqvn6tf7O1R4GNowVZfiZUCRt5MmMs1xd+4V/7Yr0g==
  dependencies:
    async-foreach "^0.1.3"
    chalk "^1.1.1"
    cross-spawn "^3.0.0"
    gaze "^1.0.0"
    get-stdin "^4.0.1"
    glob "^7.0.3"
    in-publish "^2.0.0"
    lodash "^4.17.15"
    meow "^3.7.0"
    mkdirp "^0.5.1"
    nan "^2.13.2"
    node-gyp "^3.8.0"
    npmlog "^4.0.0"
    request "^2.88.0"
    sass-graph "2.2.5"
    stdout-stream "^1.4.0"
    "true-case-path" "^1.0.2"

"nopt@2 || 3":
  version "3.0.6"
  resolved "https://registry.npmmirror.com/nopt/-/nopt-3.0.6.tgz"
  integrity sha512-4GUt3kSEYmk4ITxzB/b9vaIDfUVWN/Ml1Fwl11IlnIG2iaJ9O6WXZ9SrYM9NLI8OCBieN2Y8SWC2oJV0RQ7qYg==
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4, normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  integrity sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-1.0.0.tgz"
  integrity sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@^1.4.0, normalize-url@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/normalize-url/-/normalize-url-1.9.1.tgz"
  integrity sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

normalize-url@^3.0.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/normalize-url/-/normalize-url-3.3.0.tgz"
  integrity sha512-U+JJi7duF1o+u2pynbp2zXDW2/PADgC30f0GsHZtRh+HOcXHnw137TrNlyxxRvWW5fjKd3bcLHPxofWuCjaeZg==

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

npmlog@^4.0.0, "npmlog@0 || 1 || 2 || 3 || 4":
  version "4.1.2"
  resolved "https://registry.npmmirror.com/npmlog/-/npmlog-4.1.2.tgz"
  integrity sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

nprogress@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/nprogress/-/nprogress-0.2.0.tgz"
  integrity sha1-y480xTIT2JVyP8urkH6UIq28r7E=

nth-check@^1.0.2, nth-check@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/nth-check/-/nth-check-1.0.2.tgz"
  integrity sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg==
  dependencies:
    boolbase "~1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz"
  integrity sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1, object-assign@4.x:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-hash@^1.1.4:
  version "1.3.1"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-1.3.1.tgz"
  integrity sha512-OSuu/pU4ENM9kmREg0BdNrUDIl1heYa4mBZacJc+vVWz4GtAwu7jO8s4AIt2aGRUTqxykpWzI3Oqnsm13tTMDA==

object-inspect@^1.7.0, object-inspect@~1.7.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.7.0.tgz"
  integrity sha512-a7pEHdh1xKIAgTySUGgLMx/xwDZskN1Ud6egYYN3EdRW4ZMPNEDUTF+hwy2LUC+Bl+SyLXANnwz/jyh/qutKUw==

object-is@^1.0.1:
  version "1.1.2"
  resolved "https://registry.npmjs.org/object-is/-/object-is-1.1.2.tgz"
  integrity sha512-5lHCz+0uufF6wZ7CRFWJN3hp8Jqblpgve06U5CMQ3f//6iDjPr2PEo9MWCjEssDsa+UZEL4PkFpr+BMop6aKzQ==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

object-keys@^1.0.11, object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object-path@^0.9.2:
  version "0.9.2"
  resolved "https://registry.npmjs.org/object-path/-/object-path-0.9.2.tgz"
  integrity sha1-D9mnT8X60a45aLWGvaXGMr1sBaU=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.0.tgz"
  integrity sha512-exHJeq6kBKj58mqGyTQ9DFvrZC/eR6OwxzoM9YRoGBqrXYonaFyGiFMuc9VZrXf7DarreEwMpurG3dd+CNyW5w==
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.getownpropertydescriptors@^2.0.3, object.getownpropertydescriptors@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.0.tgz"
  integrity sha512-Z53Oah9A3TdLoblT7VKJaTDdXdT+lQO+cNpKVnya5JDe9uLvzu1YyY1yFDFrcxrlRgWrEFH0jJtD/IbuwjcEVg==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.0, object.values@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object.values/-/object.values-1.1.1.tgz"
  integrity sha512-WTa54g2K8iu0kmS/us18jEmdv1a4Wi//BZ/DTVYEcH0XhLM5NYdpDHja3gt57VrZLcNAO2WGA+KpWsDBaHt6eA==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"
    function-bind "^1.1.1"
    has "^1.0.3"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

omit.js@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/omit.js/download/omit.js-1.0.2.tgz"
  integrity sha1-kaFPDrqEBm36AVvzDkdMR/MLyFg=
  dependencies:
    babel-runtime "^6.23.0"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
  integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.0.tgz"
  integrity sha512-5NcSkPHhwTVFIQN+TUqXoS5+dlElHXdpAWu9I0HP20YOtIi+aZ0Ct82jdlILDxjLEAWwvm+qj1m6aEtsDVmm6Q==
  dependencies:
    mimic-fn "^2.1.0"

open@^6.3.0:
  version "6.4.0"
  resolved "https://registry.npmjs.org/open/-/open-6.4.0.tgz"
  integrity sha512-IFenVPgF70fSm1keSd2iDBIDIBZkroLeuffXq+wKTzTJlBpesFWojV9lb8mzOfaAzM1sr7HQHuO0vtV0zYekGg==
  dependencies:
    is-wsl "^1.1.0"

opener@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/opener/-/opener-1.5.1.tgz"
  integrity sha512-goYSy5c2UXE4Ra1xixabeVh1guIX/ZV/YokJksb6q2lubWu6UbvPQ20p542/sFIll1nl8JnCyK9oBaOcCWXwvA==

opn@^5.1.0, opn@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/opn/-/opn-5.5.0.tgz"
  integrity sha512-PqHpggC9bLV0VeWcdKhkpxY+3JTzetLSqTCWL/z/tFIbI6G8JCjondXklT1JinczLz2Xib62sSp0T/gKT4KksA==
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.8.2:
  version "0.8.3"
  resolved "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz"
  integrity sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

ora@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npmjs.org/ora/-/ora-3.4.0.tgz"
  integrity sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg==
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

original@^1.0.0, original@>=0.0.5:
  version "1.0.2"
  resolved "https://registry.npmjs.org/original/-/original-1.0.2.tgz"
  integrity sha512-hyBVl6iqqUOJ8FqRe+l/gS8H+kKYjrEndd5Pm1MfBtsEKA038HkkdbAl/72EAXGyonD/PFsvmVG+EvcIpliMBg==
  dependencies:
    url-parse "^1.4.3"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-homedir@^1.0.0, os-homedir@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-locale@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/os-locale/-/os-locale-1.4.0.tgz"
  integrity sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=
  dependencies:
    lcid "^1.0.0"

os-locale@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/os-locale/-/os-locale-2.1.0.tgz"
  integrity sha512-3sslG3zJbEYcaC4YVAvDorjGxc7tv6KVATnLPZONiljsUncvihe9BQoVCEs0RZ1kmf4Hk9OBqlZfJZWI4GanKA==
  dependencies:
    execa "^0.7.0"
    lcid "^1.0.0"
    mem "^1.1.0"

os-tmpdir@^1.0.0, os-tmpdir@^1.0.1, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osenv@0:
  version "0.1.5"
  resolved "https://registry.npmmirror.com/osenv/-/osenv-0.1.5.tgz"
  integrity sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-finally@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/p-finally/-/p-finally-2.0.1.tgz"
  integrity sha512-vpm09aKwq6H9phqRQzecoDpD8TmVyGw70qmWlyq5onxY7tqyTTFVvxMykxQSQKILBSFlbXpypIw2T1Ml7+DDtw==

p-limit@^1.0.0, p-limit@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-1.3.0.tgz"
  integrity sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-2.0.0.tgz"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz"
  integrity sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-map@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/p-map/-/p-map-1.2.0.tgz"
  integrity sha512-r6zKACMNhjPJMTl8KcFH4li//gkrXWfbD6feV8l6doRHlzljFWGJ2AP6iKaCJXyZmAUMOPtvbW7EXkbWO/pLEA==

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/p-map/-/p-map-2.1.0.tgz"
  integrity sha512-y3b8Kpd8OAN444hxfBbFfj1FY/RjtTd8tzYwhUqNYXx0fXx2iX4maP4Qr6qhIKbQXI02wTLAda4fYUbDagTUFw==

p-map@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/p-map/-/p-map-3.0.0.tgz"
  integrity sha512-d3qXVTF/s+W+CdJ5A29wywV2n8CQQYahlgz2bFiA+4eVNJbHJodPZ+/gXwPGh0bOqA+j8S+6+ckmvLGPk1QpxQ==
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/p-retry/-/p-retry-3.0.1.tgz"
  integrity sha512-XE6G4+YTTkT2a0UWb2kjZe8xNwf8bIbnqpc/IS/idOBVhyves0mK5OJgeocjx7q5pvX/6m23xuzVPYT1uGM73w==
  dependencies:
    retry "^0.12.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

pako@~1.0.5:
  version "1.0.11"
  resolved "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  integrity sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/parallel-transform/-/parallel-transform-1.2.0.tgz"
  integrity sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg==
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/param-case/-/param-case-3.0.3.tgz"
  integrity sha512-VWBVyimc1+QrzappRs7waeN2YmoZFCGXWASRYX1/rGHtXqEcrGEIDm+jqIwFa2fRXNgQEwrxaYuIrX0WcAguTA==
  dependencies:
    dot-case "^3.0.3"
    tslib "^1.10.0"

param-case@2.1.x:
  version "2.1.1"
  resolved "https://registry.npmjs.org/param-case/-/param-case-2.1.1.tgz"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parenthesis@^3.1.5:
  version "3.1.8"
  resolved "https://registry.npmmirror.com/parenthesis/-/parenthesis-3.1.8.tgz"
  integrity sha512-KF/U8tk54BgQewkJPvB4s/US3VQY68BRDpH638+7O/n58TpnwiwnOtGIOsT2/i+M78s61BBpeC83STB88d8sqw==

parse-asn1@^5.0.0, parse-asn1@^5.1.5:
  version "5.1.5"
  resolved "https://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.5.tgz"
  integrity sha512-jkMYn1dcJqF6d5CpU689bq7w/b5ALS9ROVSpQDPrZsqqesUJii9qutvoT5ltGedNXMO2e16YUWIghG9KxaViTQ==
  dependencies:
    asn1.js "^4.0.0"
    browserify-aes "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-headers@^2.0.0:
  version "2.0.5"
  resolved "https://registry.npmjs.org/parse-headers/-/parse-headers-2.0.5.tgz"
  integrity sha512-ft3iAoLOB/MlwbNXgzy43SWGP6sQki2jQvAyBg/zDFAgr9bfNWZIUj42Kw2eJIl8kEi4PbgE6U1Zau/HwI75HA==

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-2.2.0.tgz"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.0.0.tgz"
  integrity sha512-OOY5b7PAEFV0E2Fir1KOkxchnZNCdowAJgQ5NuxjpBKTRP3pQhwkrkxqQjeoKJ+fO7bCpmIZaogI4eZGDMEGOw==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"
    lines-and-columns "^1.1.6"

parse-svg-path@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npmjs.org/parse-svg-path/-/parse-svg-path-0.1.2.tgz"
  integrity sha1-en7A0esG+lMlx9PgCbhZoJtdSes=

parse5-htmlparser2-tree-adapter@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-5.1.1.tgz"
  integrity sha512-CF+TKjXqoqyDwHqBhFQ+3l5t83xYi6fVT1tQNg+Ye0JRLnTxWvIroCjEp1A0k4lneHNBGnICUf0cfYVYGEazqw==
  dependencies:
    parse5 "^5.1.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/parse5/-/parse5-5.1.1.tgz"
  integrity sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug==

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

pascal-case@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/pascal-case/-/pascal-case-3.1.1.tgz"
  integrity sha512-XIeHKqIrsquVTQL2crjq3NfJUxmdLasn3TYOU0VBM+UX2a6ztAWBlJQBePLGY7VHW8+2dRadeIPK5+KImwTxQA==
  dependencies:
    no-case "^3.0.3"
    tslib "^1.10.0"

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/path-browserify/-/path-browserify-0.0.1.tgz"
  integrity sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ==

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-2.1.0.tgz"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0, path-is-absolute@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.1, path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz"
  integrity sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw==

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-1.1.0.tgz"
  integrity sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-2.0.0.tgz"
  integrity sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=
  dependencies:
    pify "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-3.0.0.tgz"
  integrity sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==
  dependencies:
    pify "^3.0.0"

pbkdf2@^3.0.3:
  version "3.1.1"
  resolved "https://registry.npmjs.org/pbkdf2/-/pbkdf2-3.1.1.tgz"
  integrity sha512-4Ejy1OPxi9f2tt1rRV7Go7zmfDQ+ZectEQz3VGUQhgq62HtIRPDyG/JtnwIxs6x3uNMwo2V7q1fMvKjb+Tnpqg==
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

photoswipe@^4.1.2:
  version "4.1.3"
  resolved "https://registry.npmjs.org/photoswipe/-/photoswipe-4.1.3.tgz"
  integrity sha512-89Z43IRUyw7ycTolo+AaiDn3W1EEIfox54hERmm9bI12IB9cvRfHSHez3XhAyU8XW2EAFrC+2sKMhh7SJwn0bA==

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.2.2"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.2.2.tgz"
  integrity sha512-q0M/9eZHzmr0AulXyPwNfZjtwZ/RBZlbN3K3CErVrk50T2ASYI7Bye0EvekFY3IP1Nt2DHu0re+V2ZHIpMkuWg==

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz"
  integrity sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pkcs7@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npmjs.org/pkcs7/-/pkcs7-0.2.3.tgz"
  integrity sha512-kJRwmADEQUg+qJyRgWLtpEL9q9cFjZschejTEK3GRjKvnsU9G5WWoe/wKqRgbBoqWdVSeTUKP6vIA3Y72M3rWA==

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-1.0.0.tgz"
  integrity sha1-ektQio1bstYp1EcFb/TpyTFM89Q=
  dependencies:
    find-up "^1.0.0"

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-2.0.0.tgz"
  integrity sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=
  dependencies:
    find-up "^2.1.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-3.0.0.tgz"
  integrity sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/pkg-up/-/pkg-up-2.0.0.tgz"
  integrity sha1-yBmscoBZpGHKscOImivjxJoATX8=
  dependencies:
    find-up "^2.1.0"

pluralize@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/pluralize/-/pluralize-7.0.0.tgz"
  integrity sha512-ARhBOdzS3e41FbkW/XWrTEtukqqLoK5+Z/4UeDaLuSW+39JPeFgs4gCGqsrJHVZX0fUrx//4OF0K1CUGwlIFow==

point-at-length@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npmjs.org/point-at-length/-/point-at-length-1.1.0.tgz"
  integrity sha1-CtcuvQmA1/WhqxIpbAVfnrazDlc=
  dependencies:
    abs-svg-path "~0.1.1"
    isarray "~0.0.1"
    parse-svg-path "~0.1.1"

portfinder@^1.0.20, portfinder@^1.0.26, portfinder@^1.0.9:
  version "1.0.26"
  resolved "https://registry.npmjs.org/portfinder/-/portfinder-1.0.26.tgz"
  integrity sha512-Xi7mKxJHHMI3rIUrnm/jjUgwhbYMkp/XKEcZX3aG4BrumLpq3nmoQMX+ClYnDZnZ/New7IatC1no5RX0zo1vXQ==
  dependencies:
    async "^2.6.2"
    debug "^3.1.1"
    mkdirp "^0.5.1"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-calc@^5.2.0:
  version "5.3.1"
  resolved "https://registry.npmjs.org/postcss-calc/-/postcss-calc-5.3.1.tgz"
  integrity sha1-d7rnypKK2FcW4v2kLyYb98HWW14=
  dependencies:
    postcss "^5.0.2"
    postcss-message-helpers "^2.0.0"
    reduce-css-calc "^1.2.6"

postcss-calc@^7.0.1:
  version "7.0.2"
  resolved "https://registry.npmjs.org/postcss-calc/-/postcss-calc-7.0.2.tgz"
  integrity sha512-rofZFHUg6ZIrvRwPeFktv06GdbDYLcGqh9EwiMutZg+a0oePCCw1zHOEiji6LCpyRcjTREtPASuUqeAvYlEVvQ==
  dependencies:
    postcss "^7.0.27"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.0.2"

postcss-colormin@^2.1.8:
  version "2.2.2"
  resolved "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-2.2.2.tgz"
  integrity sha1-ZjFBfV8OkJo9fsJrJMio0eT5bks=
  dependencies:
    colormin "^1.0.5"
    postcss "^5.0.13"
    postcss-value-parser "^3.2.3"

postcss-colormin@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-4.0.3.tgz"
  integrity sha512-WyQFAdDZpExQh32j0U0feWisZ0dmOtPl44qYmJKkq9xFWY3p+4qnRzCHeNrkeRhwPHz9bQ3mo0/yVkaply0MNw==
  dependencies:
    browserslist "^4.0.0"
    color "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-convert-values@^2.3.4:
  version "2.6.1"
  resolved "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-2.6.1.tgz"
  integrity sha1-u9hZPFwf0uPRwyK7kl3K6Nrk1i0=
  dependencies:
    postcss "^5.0.11"
    postcss-value-parser "^3.1.2"

postcss-convert-values@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-4.0.1.tgz"
  integrity sha512-Kisdo1y77KUC0Jmn0OXU/COOJbzM8cImvw1ZFsBgBgMgb1iL23Zs/LXRe3r+EZqM3vGYKdQ2YJVQ5VkJI+zEJQ==
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-discard-comments@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-2.0.4.tgz"
  integrity sha1-vv6J+v1bPazlzM5Rt2uBUUvgDj0=
  dependencies:
    postcss "^5.0.14"

postcss-discard-comments@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-4.0.2.tgz"
  integrity sha512-RJutN259iuRf3IW7GZyLM5Sw4GLTOH8FmsXBnv8Ab/Tc2k4SR4qbV4DNbyyY4+Sjo362SyDmW2DQ7lBSChrpkg==
  dependencies:
    postcss "^7.0.0"

postcss-discard-duplicates@^2.0.1:
  version "2.1.0"
  resolved "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-2.1.0.tgz"
  integrity sha1-uavye4isGIFYpesSq8riAmO5GTI=
  dependencies:
    postcss "^5.0.4"

postcss-discard-duplicates@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-4.0.2.tgz"
  integrity sha512-ZNQfR1gPNAiXZhgENFfEglF93pciw0WxMkJeVmw8eF+JZBbMD7jp6C67GqJAXVZP2BWbOztKfbsdmMp/k8c6oQ==
  dependencies:
    postcss "^7.0.0"

postcss-discard-empty@^2.0.1:
  version "2.1.0"
  resolved "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-2.1.0.tgz"
  integrity sha1-0rS9nVztXr2Nyt52QMfXzX9PkrU=
  dependencies:
    postcss "^5.0.14"

postcss-discard-empty@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-4.0.1.tgz"
  integrity sha512-B9miTzbznhDjTfjvipfHoqbWKwd0Mj+/fL5s1QOz06wufguil+Xheo4XpOnc4NqKYBCNqqEzgPv2aPBIJLox0w==
  dependencies:
    postcss "^7.0.0"

postcss-discard-overridden@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-0.1.1.tgz"
  integrity sha1-ix6vVU9ob7KIzYdMVWZ7CqNmjVg=
  dependencies:
    postcss "^5.0.16"

postcss-discard-overridden@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-4.0.1.tgz"
  integrity sha512-IYY2bEDD7g1XM1IDEsUT4//iEYCxAmP5oDSFMVU/JVvT7gh+l4fmjciLqGgwjdWpQIdb0Che2VX00QObS5+cTg==
  dependencies:
    postcss "^7.0.0"

postcss-discard-unused@^2.2.1:
  version "2.2.3"
  resolved "https://registry.npmjs.org/postcss-discard-unused/-/postcss-discard-unused-2.2.3.tgz"
  integrity sha1-vOMLLMWR/8Y0Mitfs0ZLbZNPRDM=
  dependencies:
    postcss "^5.0.14"
    uniqs "^2.0.0"

postcss-filter-plugins@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/postcss-filter-plugins/-/postcss-filter-plugins-2.0.3.tgz"
  integrity sha512-T53GVFsdinJhgwm7rg1BzbeBRomOg9y5MBVhGcsV0CxurUdVj1UlPdKtn7aqYA/c/QVkzKMjq2bSV5dKG5+AwQ==
  dependencies:
    postcss "^5.0.4"

postcss-load-config@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.2.0.tgz"
  integrity sha1-U56a/J3chiASHr+djDZz4M5Q0oo=
  dependencies:
    cosmiconfig "^2.1.0"
    object-assign "^4.1.0"
    postcss-load-options "^1.2.0"
    postcss-load-plugins "^2.3.0"

postcss-load-config@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-2.1.0.tgz"
  integrity sha512-4pV3JJVPLd5+RueiVVB+gFOAa7GWc25XQcMp86Zexzke69mKf6Nx9LRcQywdz7yZI9n1udOxmLuAwTBypypF8Q==
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-load-options@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/postcss-load-options/-/postcss-load-options-1.2.0.tgz"
  integrity sha1-sJixVZ3awt8EvAuzdfmaXP4rbYw=
  dependencies:
    cosmiconfig "^2.1.0"
    object-assign "^4.1.0"

postcss-load-plugins@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/postcss-load-plugins/-/postcss-load-plugins-2.3.0.tgz"
  integrity sha1-dFdoEWWZrKLwCfrUJrABdQSdjZI=
  dependencies:
    cosmiconfig "^2.1.1"
    object-assign "^4.1.0"

postcss-loader@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/postcss-loader/-/postcss-loader-3.0.0.tgz"
  integrity sha512-cLWoDEY5OwHcAjDnkyRQzAXfs2jrKjXpO/HQFcc5b5u/r7aa471wdmChmwfnv7x2u840iat/wi0lQ5nbRgSkUA==
  dependencies:
    loader-utils "^1.1.0"
    postcss "^7.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^1.0.0"

postcss-merge-idents@^2.1.5:
  version "2.1.7"
  resolved "https://registry.npmjs.org/postcss-merge-idents/-/postcss-merge-idents-2.1.7.tgz"
  integrity sha1-TFUwMTwI4dWzu/PSu8dH4njuonA=
  dependencies:
    has "^1.0.1"
    postcss "^5.0.10"
    postcss-value-parser "^3.1.1"

postcss-merge-longhand@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-2.0.2.tgz"
  integrity sha1-I9kM0Sewp3mUkVMyc5A0oaTz1lg=
  dependencies:
    postcss "^5.0.4"

postcss-merge-longhand@^4.0.11:
  version "4.0.11"
  resolved "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-4.0.11.tgz"
  integrity sha512-alx/zmoeXvJjp7L4mxEMjh8lxVlDFX1gqWHzaaQewwMZiVhLo42TEClKaeHbRf6J7j82ZOdTJ808RtN0ZOZwvw==
  dependencies:
    css-color-names "0.0.4"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    stylehacks "^4.0.0"

postcss-merge-rules@^2.0.3:
  version "2.1.2"
  resolved "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-2.1.2.tgz"
  integrity sha1-0d9d+qexrMO+VT8OnhDofGG19yE=
  dependencies:
    browserslist "^1.5.2"
    caniuse-api "^1.5.2"
    postcss "^5.0.4"
    postcss-selector-parser "^2.2.2"
    vendors "^1.0.0"

postcss-merge-rules@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-4.0.3.tgz"
  integrity sha512-U7e3r1SbvYzO0Jr3UT/zKBVgYYyhAz0aitvGIYOYK5CPmkNih+WDSsS5tvPrJ8YMQYlEMvsZIiqmn7HdFUaeEQ==
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    cssnano-util-same-parent "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"
    vendors "^1.0.0"

postcss-message-helpers@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/postcss-message-helpers/-/postcss-message-helpers-2.0.0.tgz"
  integrity sha1-pPL0+rbk/gAvCu0ABHjN9S+bpg4=

postcss-minify-font-values@^1.0.2:
  version "1.0.5"
  resolved "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-1.0.5.tgz"
  integrity sha1-S1jttWZB66fIR0qzUmyv17vey2k=
  dependencies:
    object-assign "^4.0.1"
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-minify-font-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-4.0.2.tgz"
  integrity sha512-j85oO6OnRU9zPf04+PZv1LYIYOprWm6IA6zkXkrJXyRveDEuQggG6tvoy8ir8ZwjLxLuGfNkCZEQG7zan+Hbtg==
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-gradients@^1.0.1:
  version "1.0.5"
  resolved "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-1.0.5.tgz"
  integrity sha1-Xb2hE3NwP4PPtKPqOIHY11/15uE=
  dependencies:
    postcss "^5.0.12"
    postcss-value-parser "^3.3.0"

postcss-minify-gradients@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-4.0.2.tgz"
  integrity sha512-qKPfwlONdcf/AndP1U8SJ/uzIJtowHlMaSioKzebAXSG4iJthlWC9iSWznQcX4f66gIWX44RSA841HTHj3wK+Q==
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    is-color-stop "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-params@^1.0.4:
  version "1.2.2"
  resolved "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-1.2.2.tgz"
  integrity sha1-rSzgcTc7lDs9kwo/pZo1jCjW8fM=
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.2"
    postcss-value-parser "^3.0.2"
    uniqs "^2.0.0"

postcss-minify-params@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-4.0.2.tgz"
  integrity sha512-G7eWyzEx0xL4/wiBBJxJOz48zAKV2WG3iZOqVhPet/9geefm/Px5uo1fzlHu+DOjT+m0Mmiz3jkQzVHe6wxAWg==
  dependencies:
    alphanum-sort "^1.0.0"
    browserslist "^4.0.0"
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    uniqs "^2.0.0"

postcss-minify-selectors@^2.0.4:
  version "2.1.1"
  resolved "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-2.1.1.tgz"
  integrity sha1-ssapjAByz5G5MtGkllCBFDEXNb8=
  dependencies:
    alphanum-sort "^1.0.2"
    has "^1.0.1"
    postcss "^5.0.14"
    postcss-selector-parser "^2.0.0"

postcss-minify-selectors@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-4.0.2.tgz"
  integrity sha512-D5S1iViljXBj9kflQo4YutWnJmwm8VvIsU1GeXJGiG9j8CIg9zs4voPMdQDUmIxetUOh60VilsNzCiAFTOqu3g==
  dependencies:
    alphanum-sort "^1.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

postcss-modules-extract-imports@^1.2.0:
  version "1.2.1"
  resolved "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-1.2.1.tgz"
  integrity sha512-6jt9XZwUhwmRUhb/CkyJY020PYaPJsCyt3UjbaWo6XEbH/94Hmv6MP7fG2C5NDU/BcHzyGYxNtHvM+LTf9HrYw==
  dependencies:
    postcss "^6.0.1"

postcss-modules-local-by-default@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-1.2.0.tgz"
  integrity sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-scope@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-1.1.0.tgz"
  integrity sha1-1upkmUx5+XtipytCb75gVqGUu5A=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-values@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-1.3.0.tgz"
  integrity sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA=
  dependencies:
    icss-replace-symbols "^1.1.0"
    postcss "^6.0.1"

postcss-normalize-charset@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-1.1.1.tgz"
  integrity sha1-757nEhLX/nWceO0WL2HtYrXLk/E=
  dependencies:
    postcss "^5.0.5"

postcss-normalize-charset@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-4.0.1.tgz"
  integrity sha512-gMXCrrlWh6G27U0hF3vNvR3w8I1s2wOBILvA87iNXaPvSNo5uZAMYsZG7XjCUf1eVxuPfyL4TJ7++SGZLc9A3g==
  dependencies:
    postcss "^7.0.0"

postcss-normalize-display-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-4.0.2.tgz"
  integrity sha512-3F2jcsaMW7+VtRMAqf/3m4cPFhPD3EFRgNs18u+k3lTJJlVe7d0YPO+bnwqo2xg8YiRpDXJI2u8A0wqJxMsQuQ==
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-positions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-4.0.2.tgz"
  integrity sha512-Dlf3/9AxpxE+NF1fJxYDeggi5WwV35MXGFnnoccP/9qDtFrTArZ0D0R+iKcg5WsUd8nUYMIl8yXDCtcrT8JrdA==
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-repeat-style@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-4.0.2.tgz"
  integrity sha512-qvigdYYMpSuoFs3Is/f5nHdRLJN/ITA7huIoCyqqENJe9PvPmLhNLMu7QTjPdtnVf6OcYYO5SHonx4+fbJE1+Q==
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-string@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-4.0.2.tgz"
  integrity sha512-RrERod97Dnwqq49WNz8qo66ps0swYZDSb6rM57kN2J+aoyEAJfZ6bMx0sx/F9TIEX0xthPGCmeyiam/jXif0eA==
  dependencies:
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-timing-functions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-4.0.2.tgz"
  integrity sha512-acwJY95edP762e++00Ehq9L4sZCEcOPyaHwoaFOhIwWCDfik6YvqsYNxckee65JHLKzuNSSmAdxwD2Cud1Z54A==
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-unicode@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.1.tgz"
  integrity sha512-od18Uq2wCYn+vZ/qCOeutvHjB5jm57ToxRaMeNuf0nWVHaP9Hua56QyMF6fs/4FSUnVIw0CBPsU0K4LnBPwYwg==
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-url@^3.0.7:
  version "3.0.8"
  resolved "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-3.0.8.tgz"
  integrity sha1-EI90s/L82viRov+j6kWSJ5/HgiI=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^1.4.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"

postcss-normalize-url@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-4.0.1.tgz"
  integrity sha512-p5oVaF4+IHwu7VpMan/SSpmpYxcJMtkGppYf0VbdH5B6hN8YNmVyJLuY9FmLQTzY3fag5ESUUHDqM+heid0UVA==
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-whitespace@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-4.0.2.tgz"
  integrity sha512-tO8QIgrsI3p95r8fyqKV+ufKlSHh9hMJqACqbv2XknufqEDhDvbguXGBBqxw9nsQoXWf0qOqppziKJKHMD4GtA==
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-ordered-values@^2.1.0:
  version "2.2.3"
  resolved "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-2.2.3.tgz"
  integrity sha1-7sbCpntsQSqNsgQud/6NpD+VwR0=
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.1"

postcss-ordered-values@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-4.1.2.tgz"
  integrity sha512-2fCObh5UanxvSxeXrtLtlwVThBvHn6MQcu4ksNT2tsaV2Fg76R2CV98W7wNSlX+5/pFwEyaDwKLLoEV7uRybAw==
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-reduce-idents@^2.2.2:
  version "2.4.0"
  resolved "https://registry.npmjs.org/postcss-reduce-idents/-/postcss-reduce-idents-2.4.0.tgz"
  integrity sha1-wsbSDMlYKE9qv75j92Cb9AkFmtM=
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-reduce-initial@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-1.0.1.tgz"
  integrity sha1-aPgGlfBF0IJjqHmtJA343WT2ROo=
  dependencies:
    postcss "^5.0.4"

postcss-reduce-initial@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-4.0.3.tgz"
  integrity sha512-gKWmR5aUulSjbzOfD9AlJiHCGH6AEVLaM0AV+aSioxUDd16qXP1PCh8d1/BGVvpdWn8k/HiK7n6TjeoXN1F7DA==
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"

postcss-reduce-transforms@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-1.0.4.tgz"
  integrity sha1-/3b02CEkN7McKYpC0uFEQCV3GuE=
  dependencies:
    has "^1.0.1"
    postcss "^5.0.8"
    postcss-value-parser "^3.0.1"

postcss-reduce-transforms@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-4.0.2.tgz"
  integrity sha512-EEVig1Q2QJ4ELpJXMZR8Vt5DQx8/mo+dGWSR7vWXqcob2gQLyQGsionYcGKATXvQzMPn6DSN1vTN7yFximdIAg==
  dependencies:
    cssnano-util-get-match "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-safe-parser@^1.0.1:
  version "1.0.7"
  resolved "https://registry.npmjs.org/postcss-safe-parser/-/postcss-safe-parser-1.0.7.tgz"
  integrity sha1-Q70MjITV99hHTeglxpnk2ryscqg=
  dependencies:
    postcss "^5.0.18"

postcss-selector-parser@^2.0.0:
  version "2.2.3"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.2.3.tgz"
  integrity sha1-+UN3iGBsPJrO4W/+jYsWKX8nu5A=
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^2.2.2:
  version "2.2.3"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.2.3.tgz"
  integrity sha1-+UN3iGBsPJrO4W/+jYsWKX8nu5A=
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^3.0.0:
  version "3.1.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz"
  integrity sha512-h7fJ/5uWuRVyOtkO45pnt1Ih40CEleeyCHzipqAZO2e5H20g25Y48uYnFUiShvY4rZWNJ/Bib/KVPmanaCtOhA==
  dependencies:
    dot-prop "^5.2.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.2.tgz"
  integrity sha512-36P2QR59jDTOAiIkqEprfJDsoNrvwFei3eCqKd1Y0tUsBimsq39BLp7RD+JWny3WgB1zGhJX8XVePwm9k4wdBg==
  dependencies:
    cssesc "^3.0.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-svgo@^2.1.1:
  version "2.1.6"
  resolved "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-2.1.6.tgz"
  integrity sha1-tt8YqmE7Zm4TPwittSGcJoSsEI0=
  dependencies:
    is-svg "^2.0.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"
    svgo "^0.7.0"

postcss-svgo@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-4.0.2.tgz"
  integrity sha512-C6wyjo3VwFm0QgBy+Fu7gCYOkCmgmClghO+pjcxvrcBKtiKt0uCF+hvbMO1fyv5BMImRK90SMb+dwUnfbGd+jw==
  dependencies:
    is-svg "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    svgo "^1.0.0"

postcss-unique-selectors@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-2.0.2.tgz"
  integrity sha1-mB1X0p3csz57Hf4f1DuGSfkzyh0=
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss-unique-selectors@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-4.0.1.tgz"
  integrity sha512-+JanVaryLo9QwZjKrmJgkI4Fn8SBgRO6WXQBJi7KiAVPlmxikB5Jzc4EvXMT2H0/m0RjrVVm9rGNhZddm/8Spg==
  dependencies:
    alphanum-sort "^1.0.0"
    postcss "^7.0.0"
    uniqs "^2.0.0"

postcss-value-parser@^3.0.0, postcss-value-parser@^3.0.1, postcss-value-parser@^3.0.2, postcss-value-parser@^3.1.1, postcss-value-parser@^3.1.2, postcss-value-parser@^3.2.3, postcss-value-parser@^3.3.0:
  version "3.3.1"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
  integrity sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ==

postcss-value-parser@^4.0.2:
  version "4.1.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.1.0.tgz"
  integrity sha512-97DXOFbQJhk71ne5/Mt6cOu6yxsSfM0QGQyl0L25Gca4yGWEGJaig7l7gbCX623VqTBNGLRLaVUCnNkcedlRSQ==

postcss-value-parser@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.1.0.tgz"
  integrity sha512-97DXOFbQJhk71ne5/Mt6cOu6yxsSfM0QGQyl0L25Gca4yGWEGJaig7l7gbCX623VqTBNGLRLaVUCnNkcedlRSQ==

postcss-zindex@^2.0.1:
  version "2.2.0"
  resolved "https://registry.npmjs.org/postcss-zindex/-/postcss-zindex-2.2.0.tgz"
  integrity sha1-0hCd3AVbka9n/EyzsCWUZjnSryI=
  dependencies:
    has "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss@^5.0.10:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.11:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.12:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.13:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.14:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.16:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.18:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.2:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.4:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.5:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.6:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.0.8:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^5.2.16:
  version "5.2.18"
  resolved "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz"
  integrity sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^6.0.1:
  version "6.0.23"
  resolved "https://registry.npmjs.org/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.23:
  version "6.0.23"
  resolved "https://registry.npmjs.org/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^6.0.8:
  version "6.0.23"
  resolved "https://registry.npmjs.org/postcss/-/postcss-6.0.23.tgz"
  integrity sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^7.0.0, postcss@^7.0.1, postcss@^7.0.14, postcss@^7.0.27, postcss@^7.0.32:
  version "7.0.32"
  resolved "https://registry.npmjs.org/postcss/-/postcss-7.0.32.tgz"
  integrity sha512-03eXong5NLnNCD05xscnGKGDZ98CyzoqPSMjOe6SuoQY7Z2hIj0Ld1g/O/UQRuOle2aRtiIRDg9tDcTGAkLfKw==
  dependencies:
    chalk "^2.4.2"
    source-map "^0.6.1"
    supports-color "^6.1.0"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmjs.org/prepend-http/-/prepend-http-1.0.4.tgz"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

prettier@^1.18.2, prettier@^1.7.0:
  version "1.19.1"
  resolved "https://registry.npmjs.org/prettier/-/prettier-1.19.1.tgz"
  integrity sha512-s7PoyDv/II1ObgQunCbB9PdLmUcBZcnWOcxDh7O0N/UwDEsHyqkW+Qh28jW+mVuCdx7gLB0BotYI1Y6uI9iyew==

pretty-error@^2.0.2, pretty-error@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/pretty-error/-/pretty-error-2.1.1.tgz"
  integrity sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM=
  dependencies:
    renderkid "^2.0.1"
    utila "~0.4"

private@^0.1.6, private@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npmjs.org/private/-/private-0.1.8.tgz"
  integrity sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg==

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

process@^0.11.10:
  version "0.11.10"
  resolved "https://registry.npmjs.org/process/-/process-0.11.10.tgz"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

process@~0.5.1:
  version "0.5.2"
  resolved "https://registry.npmjs.org/process/-/process-0.5.2.tgz"
  integrity sha512-oNpcutj+nYX2FjdEW7PGltWhXulAnFlM0My/k48L90hARCOJtvBbQXc/6itV2jDvU5xAAtonP+r6wmQgCcbAUA==

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz"
  integrity sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

promise@^7.1.1:
  version "7.3.1"
  resolved "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz"
  integrity sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==
  dependencies:
    asap "~2.0.3"

proxy-addr@~2.0.5:
  version "2.0.6"
  resolved "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.6.tgz"
  integrity sha512-dh/frvCBVmSsDYzw6n926jv974gddhkFPfiN8hPOi30Wax25QZyZEGveluCgliBnqmuM+UJmBErbAUFIoDbjOw==
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.8.0"
  resolved "https://registry.npmjs.org/psl/-/psl-1.8.0.tgz"
  integrity sha512-RIdOzyoavK+hA18OGGWDqUTsCLhtA7IcZ/6NCs4fFJaHBDab+pDDmDIByWFRQJq2Cd7r1OoQxBGKOaztq+hjIQ==

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npmjs.org/public-encrypt/-/public-encrypt-4.0.3.tgz"
  integrity sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz"
  integrity sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz"
  integrity sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz"
  integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "https://registry.npmjs.org/pumpify/-/pumpify-1.5.1.tgz"
  integrity sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@^1.2.4:
  version "1.4.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
  integrity sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

q@^1.1.2:
  version "1.5.1"
  resolved "https://registry.npmjs.org/q/-/q-1.5.1.tgz"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qs@~6.5.2:
  version "6.5.2"
  resolved "https://registry.npmjs.org/qs/-/qs-6.5.2.tgz"
  integrity sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA==

qs@6.7.0:
  version "6.7.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.7.0.tgz"
  integrity sha512-VCdBRNFTX1fyE7Nb6FYoURo/SPe62QCaAyzJvUjwRaIsc+NePBEniHlvxFmmX56+HZphIGtV0XeCirBtpDrTyQ==

query-string@^4.1.0:
  version "4.3.4"
  resolved "https://registry.npmjs.org/query-string/-/query-string-4.3.4.tgz"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npmjs.org/querystring-es3/-/querystring-es3-0.2.1.tgz"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

querystringify@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/querystringify/-/querystringify-2.1.1.tgz"
  integrity sha512-w7fLxIRCRT7U8Qu53jQnJyPkYZIaR4n5151KMfcJlO/A9397Wxb1amJvROTK6TOnp7PfoAmg/qXiNHI+08jRfA==

raf@^3.4.0:
  version "3.4.1"
  resolved "https://registry.npm.taobao.org/raf/download/raf-3.4.1.tgz"
  integrity sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=
  dependencies:
    performance-now "^2.1.0"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/randomfill/-/randomfill-1.0.4.tgz"
  integrity sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.0.3, range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.4.0.tgz"
  integrity sha512-4Oz8DUIwdvoa5qMJelxipzi/iJIi40O5cGV1wNYp5hvZP8ZN0T+jiNkL0QepXs+EsQ9XJ8ipEDoiH70ySUJP3Q==
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-1.0.1.tgz"
  integrity sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-2.0.0.tgz"
  integrity sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/read-pkg/-/read-pkg-1.1.0.tgz"
  integrity sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/read-pkg/-/read-pkg-2.0.0.tgz"
  integrity sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

read-pkg@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz"
  integrity sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.6, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@~2.3.6, "readable-stream@1 || 2":
  version "2.3.7"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz"
  integrity sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^3.1.1:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz"
  integrity sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz"
  integrity sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/readdirp/-/readdirp-2.2.1.tgz"
  integrity sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.4.0:
  version "3.4.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.4.0.tgz"
  integrity sha512-0xe001vZBnJEK+uKcj8qOhyAKPzIT+gStxWr3LCB0DwcXR5NZJ3IaC+yGnHCYzB/S7ov3m3EEbZI2zeNvX+hGQ==
  dependencies:
    picomatch "^2.2.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.7.0:
  version "0.7.1"
  resolved "https://registry.npmmirror.com/rechoir/-/rechoir-0.7.1.tgz"
  integrity sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==
  dependencies:
    resolve "^1.9.0"

redent@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz"
  integrity sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

reduce-css-calc@^1.2.6:
  version "1.3.0"
  resolved "https://registry.npmjs.org/reduce-css-calc/-/reduce-css-calc-1.3.0.tgz"
  integrity sha1-dHyRTgSWFKTJz7umKYca0dKSdxY=
  dependencies:
    balanced-match "^0.4.2"
    math-expression-evaluator "^1.2.14"
    reduce-function-call "^1.0.1"

reduce-function-call@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmjs.org/reduce-function-call/-/reduce-function-call-1.0.3.tgz"
  integrity sha512-Hl/tuV2VDgWgCSEeWMLwxLZqX7OK59eU1guxXsRKTAyeYimivsKdtcV4fu3r710tpG5GmDKDhQ0HSZLExnNmyQ==
  dependencies:
    balanced-match "^1.0.0"

regenerate-unicode-properties@^8.2.0:
  version "8.2.0"
  resolved "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-8.2.0.tgz"
  integrity sha512-F9DjY1vKLo/tPePDycuH3dn9H1OTPIkVD9Kz4LODu+F2C75mgjAJ7x/gwy6ZcSNRAAkhNlJSOHRe8k3p+K9WhA==
  dependencies:
    regenerate "^1.4.0"

regenerate@^1.2.1, regenerate@^1.4.0:
  version "1.4.1"
  resolved "https://registry.npmjs.org/regenerate/-/regenerate-1.4.1.tgz"
  integrity sha512-j2+C8+NtXQgEKWk49MMP5P/u2GhnahTtVkRIHr5R5lVRlbKvmQ+oS+A5aLKWp2ma5VkT8sh6v+v4hbH0YHR66A==

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  integrity sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==

regenerator-runtime@^0.13.4:
  version "0.13.5"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.5.tgz"
  integrity sha512-ZS5w8CpKFinUzOwW3c83oPeVXoNsrLsaCoLtJvAClH135j/R77RuymhiSErhm2lKcwSCIpmvIWSbDkIfAqKQlA==

regenerator-transform@^0.10.0:
  version "0.10.1"
  resolved "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.10.1.tgz"
  integrity sha512-PJepbvDbuK1xgIgnau7Y90cwaAmO/LCLMI2mPvaXq2heGMR3aWW5/BQvYrhJ8jgmQjXewXvBjzfqKcVOmhjZ6Q==
  dependencies:
    babel-runtime "^6.18.0"
    babel-types "^6.19.0"
    private "^0.1.6"

regenerator-transform@^0.14.2:
  version "0.14.5"
  resolved "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.14.5.tgz"
  integrity sha512-eOf6vka5IO151Jfsw2NO9WpGX58W6wWmefK3I1zEGr0lOD0u8rwPaNqQL1aRxUaxLeKO3ArNh3VYg1KbaD+FFw==
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz"
  integrity sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regex-parser@^2.2.9:
  version "2.2.10"
  resolved "https://registry.npmjs.org/regex-parser/-/regex-parser-2.2.10.tgz"
  integrity sha512-8t6074A68gHfU8Neftl0Le6KTDwfGAj7IyjPIMSfikI2wJUTHDMaIq42bUsfVnj8mhx0R+45rdUXHGpN164avA==

regexp.prototype.flags@^1.2.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.3.0.tgz"
  integrity sha512-2+Q0C5g951OlYlJz6yu5/M33IcsESLlLfsyIaLJaG4FA2r4yP8MvVMJUUP/fVBkSpbbbZlS5gynbEWLipiiXiQ==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

regexpp@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npmjs.org/regexpp/-/regexpp-1.1.0.tgz"
  integrity sha512-LOPw8FpgdQF9etWMaAfG/WRthIdXJGYp4mJ2Jgn/2lpkbod9jPn0t9UqN7AxBOKNfzRbYyVfgc7Vk4t/MpnXgw==

regexpp@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/regexpp/-/regexpp-2.0.1.tgz"
  integrity sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw==

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/regexpu-core/-/regexpu-core-2.0.0.tgz"
  integrity sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA=
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regexpu-core@^4.6.0:
  version "4.7.0"
  resolved "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.7.0.tgz"
  integrity sha512-TQ4KXRnIn6tz6tjnrXEkD/sshygKH/j5KzK86X8MkeHyZ8qst/LZ89j3X4/8HEIfHANTFIP/AbXakeRhWIl5YQ==
  dependencies:
    regenerate "^1.4.0"
    regenerate-unicode-properties "^8.2.0"
    regjsgen "^0.5.1"
    regjsparser "^0.6.4"
    unicode-match-property-ecmascript "^1.0.4"
    unicode-match-property-value-ecmascript "^1.2.0"

regexpu-core@^4.7.0:
  version "4.7.0"
  resolved "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.7.0.tgz"
  integrity sha512-TQ4KXRnIn6tz6tjnrXEkD/sshygKH/j5KzK86X8MkeHyZ8qst/LZ89j3X4/8HEIfHANTFIP/AbXakeRhWIl5YQ==
  dependencies:
    regenerate "^1.4.0"
    regenerate-unicode-properties "^8.2.0"
    regjsgen "^0.5.1"
    regjsparser "^0.6.4"
    unicode-match-property-ecmascript "^1.0.4"
    unicode-match-property-value-ecmascript "^1.2.0"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/regjsgen/-/regjsgen-0.2.0.tgz"
  integrity sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=

regjsgen@^0.5.1:
  version "0.5.2"
  resolved "https://registry.npmjs.org/regjsgen/-/regjsgen-0.5.2.tgz"
  integrity sha512-OFFT3MfrH90xIW8OOSyUrk6QHD5E9JOTeGodiJeBS3J6IwlgzJMNE/1bZklWz5oTg+9dCMyEetclvCVXOPoN3A==

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.5.tgz"
  integrity sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=
  dependencies:
    jsesc "~0.5.0"

regjsparser@^0.6.4:
  version "0.6.4"
  resolved "https://registry.npmjs.org/regjsparser/-/regjsparser-0.6.4.tgz"
  integrity sha512-64O87/dPDgfk8/RQqC4gkZoGyyWFIEUTTh80CU6CWuK5vkCGyekIx+oKcEIYtP/RAxSQltCZHCNu/mdd7fqlJw==
  dependencies:
    jsesc "~0.5.0"

regression@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/regression/-/regression-2.0.1.tgz"
  integrity sha1-jSnD6CJKEIUMNeM36FqLL6w7DIc=

relateurl@^0.2.7, relateurl@0.2.x:
  version "0.2.7"
  resolved "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^2.0.1:
  version "2.0.3"
  resolved "https://registry.npmjs.org/renderkid/-/renderkid-2.0.3.tgz"
  integrity sha512-z8CLQp7EZBPCwCnncgf9C4XAi3WR0dv+uWu/PjIyhhAb5d6IJ/QZqlHFprHeKT+59//V6BNUsLbvN8+2LarxGA==
  dependencies:
    css-select "^1.1.0"
    dom-converter "^0.2"
    htmlparser2 "^3.3.0"
    strip-ansi "^3.0.0"
    utila "^0.4.0"

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.3.tgz"
  integrity sha512-ahGq0ZnV5m5XtZLMb+vP76kcAM5nkLqk0lpqAuojSKGgQtn4eRi4ZZGm2olo2zKFH+sMsWaqOCW1dqAnOru72g==

repeat-string@^1.5.2, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz"
  integrity sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=
  dependencies:
    is-finite "^1.0.0"

request-promise-core@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/request-promise-core/-/request-promise-core-1.1.3.tgz"
  integrity sha512-QIs2+ArIGQVp5ZYbWD5ZLCY29D5CfWizP8eWnm8FoGD1TX61veauETVQbrV60662V0oFBkrDOuaBI8XgtuyYAQ==
  dependencies:
    lodash "^4.17.15"

request-promise-native@^1.0.7:
  version "1.0.8"
  resolved "https://registry.npmjs.org/request-promise-native/-/request-promise-native-1.0.8.tgz"
  integrity sha512-dapwLGqkHtwL5AEbfenuzjTYg35Jd6KPytsC2/TLkVMz8rm+tNt72MGUWT1RP/aYawMpN6HqbNGBQaRcBtjQMQ==
  dependencies:
    request-promise-core "1.1.3"
    stealthy-require "^1.1.1"
    tough-cookie "^2.3.3"

request@^2.34, request@^2.83.0, request@^2.87.0, request@^2.88.0:
  version "2.88.2"
  resolved "https://registry.npmjs.org/request/-/request-2.88.2.tgz"
  integrity sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^1.1.0:
  version "1.2.1"
  resolved "https://registry.npmjs.org/require-from-string/-/require-from-string-1.2.1.tgz"
  integrity sha1-UpyczvJzgK3+yaL5ZbZJu+5jZBg=

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/require-main-filename/-/require-main-filename-1.0.1.tgz"
  integrity sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/require-main-filename/-/require-main-filename-2.0.0.tgz"
  integrity sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==

require-uncached@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/require-uncached/-/require-uncached-1.0.3.tgz"
  integrity sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=
  dependencies:
    caller-path "^0.1.0"
    resolve-from "^1.0.0"

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

reselect@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/reselect/-/reselect-3.0.1.tgz"
  integrity sha1-79qpjqdFEyTQkrKyFjpqHXqaIUc=

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npm.taobao.org/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-2.0.0.tgz"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  integrity sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.1.tgz"
  integrity sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==

resolve-url-loader@^2.1.1:
  version "2.3.2"
  resolved "https://registry.npmjs.org/resolve-url-loader/-/resolve-url-loader-2.3.2.tgz"
  integrity sha512-sc/UVgiADdoTc+4cGPB7cUCnlEkzlxD1NXHw4oa9qA0fp30H8mAQ2ePJBP9MQ029DUuhEPouhNdvzT37pBCV0g==
  dependencies:
    adjust-sourcemap-loader "^1.1.0"
    camelcase "^4.1.0"
    convert-source-map "^1.5.1"
    loader-utils "^1.1.0"
    lodash.defaults "^4.0.0"
    rework "^1.0.1"
    rework-visit "^1.0.0"
    source-map "^0.5.7"
    urix "^0.1.0"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.10.0, resolve@^1.12.0, resolve@^1.13.1, resolve@^1.17.0, resolve@^1.3.2, resolve@^1.4.0, resolve@^1.8.1, resolve@^1.9.0, resolve@~1.17.0:
  version "1.17.0"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.17.0.tgz"
  integrity sha512-ic+7JYiV8Vi2yzQGFWOkiZD5Z9z7O2Zhm9XMaTxdJExKasieFCr+yXZ/WmXsckHiKl12ar0y6XiXDx3m4RHn1w==
  dependencies:
    path-parse "^1.0.6"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

resumer@~0.0.0:
  version "0.0.0"
  resolved "https://registry.npmjs.org/resumer/-/resumer-0.0.0.tgz"
  integrity sha1-8ej0YeQGS6Oegq883CqMiT0HZ1k=
  dependencies:
    through "~2.3.4"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz"
  integrity sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==

retry@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

rework-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/rework-visit/-/rework-visit-1.0.0.tgz"
  integrity sha1-mUWygD8hni96ygCtuLyfZA+ELJo=

rework@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/rework/-/rework-1.0.1.tgz"
  integrity sha1-MIBqhBNCtUUQqkEQhQzUhTQUSqc=
  dependencies:
    convert-source-map "^0.3.3"
    css "^2.0.0"

rgb-regex@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/rgb-regex/-/rgb-regex-1.0.1.tgz"
  integrity sha1-wODWiC3w4jviVKR16O3UGRX+rrE=

rgba-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/rgba-regex/-/rgba-regex-1.0.0.tgz"
  integrity sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=

right-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz"
  integrity sha1-YTObci/mo1FWiSENJOFMlhSGE+8=
  dependencies:
    align-text "^0.1.1"

rimraf@^2.2.8, rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.2, rimraf@^2.6.3, rimraf@^2.7.1, rimraf@2:
  version "2.7.1"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz"
  integrity sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==
  dependencies:
    glob "^7.1.3"

rimraf@~2.6.2:
  version "2.6.3"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-2.6.3.tgz"
  integrity sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==
  dependencies:
    glob "^7.1.3"

rimraf@2.6.3:
  version "2.6.3"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-2.6.3.tgz"
  integrity sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.2.tgz"
  integrity sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

rollup@^0.25.8:
  version "0.25.8"
  resolved "https://registry.npmjs.org/rollup/-/rollup-0.25.8.tgz"
  integrity sha1-v2zoO4dRDRY0Ru6qV37WpvxYNeA=
  dependencies:
    chalk "^1.1.1"
    minimist "^1.2.0"
    source-map-support "^0.3.2"

run-async@^2.2.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz"
  integrity sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/run-queue/-/run-queue-1.0.3.tgz"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rust-result@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/rust-result/-/rust-result-1.0.0.tgz"
  integrity sha512-6cJzSBU+J/RJCF063onnQf0cDUOHs9uZI1oroSGnHOph+CQTIJ5Pp2hK5kEQq1+7yE/EEWfulSNXAQ2jikPthA==
  dependencies:
    individual "^2.0.0"

rw@^1.3.2, rw@1:
  version "1.3.3"
  resolved "https://registry.npmjs.org/rw/-/rw-1.3.3.tgz"
  integrity sha1-P4Yt+pGrdmsUiF700BEkv9oHT7Q=

rx-lite-aggregates@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/rx-lite-aggregates/-/rx-lite-aggregates-4.0.8.tgz"
  integrity sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74=
  dependencies:
    rx-lite "*"

rx-lite@*, rx-lite@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/rx-lite/-/rx-lite-4.0.8.tgz"
  integrity sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ=

rxjs@^6.4.0:
  version "6.6.0"
  resolved "https://registry.npmjs.org/rxjs/-/rxjs-6.6.0.tgz"
  integrity sha512-3HMA8z/Oz61DUHe+SdOiQyzIf4tOx5oQHmMir7IZEu6TMqCLHT4LRcmNaUS0NwOz8VLvmmBduMsoaUvMaIiqzg==
  dependencies:
    tslib "^1.9.0"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@>=5.1.0, safe-buffer@~5.1.0, safe-buffer@~5.1.1, safe-buffer@5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-buffer@^5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-json-parse@4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/safe-json-parse/-/safe-json-parse-4.0.0.tgz"
  integrity sha512-RjZPPHugjK0TOzFrLZ8inw44s9bKox99/0AZW9o/BEQVrJfhI+fIHMErnPyRa89/yRXUUr93q+tiN6zhoVV4wQ==
  dependencies:
    rust-result "^1.0.0"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

safer-buffer@^2.0.2, safer-buffer@^2.1.0, "safer-buffer@>= 2.1.2 < 3", safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sass-graph@2.2.5:
  version "2.2.5"
  resolved "https://registry.npmmirror.com/sass-graph/-/sass-graph-2.2.5.tgz"
  integrity sha512-VFWDAHOe6mRuT4mZRd4eKE+d8Uedrk6Xnh7Sh9b4NGufQLQjOrvf/MQoOdx+0s92L89FeyUUNfU597j/3uNpag==
  dependencies:
    glob "^7.0.0"
    lodash "^4.0.0"
    scss-tokenizer "^0.2.3"
    yargs "^13.3.2"

sass-loader@^8.0.1:
  version "8.0.2"
  resolved "https://registry.npmmirror.com/sass-loader/-/sass-loader-8.0.2.tgz"
  integrity sha512-7o4dbSK8/Ol2KflEmSco4jTjQoV988bM82P9CZdmo9hR3RLnvNc0ufMNdMrB0caq38JQ/FgF4/7RcbcfKzxoFQ==
  dependencies:
    clone-deep "^4.0.1"
    loader-utils "^1.2.3"
    neo-async "^2.6.1"
    schema-utils "^2.6.1"
    semver "^6.3.0"

sax@~1.2.1, sax@~1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
  integrity sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==

schema-utils@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-0.3.0.tgz"
  integrity sha1-9YdyIs4+kx7a4DnxfrNxbnE3+M8=
  dependencies:
    ajv "^5.0.0"

schema-utils@^0.4.5:
  version "0.4.7"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.7.tgz"
  integrity sha512-v/iwU6wvwGK8HbU9yi3/nhGzP0yGSuhQMzL6ySiec1FSrZZDkhm4noOSWzrNFo/jEc+SJY6jRTwuwbSXJPDUnQ==
  dependencies:
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz"
  integrity sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

schema-utils@^2.6.1:
  version "2.7.0"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.0.tgz"
  integrity sha512-0ilKFI6QQF5nxDZLFn2dMjvc4hjg/Wkg7rHd3jK6/A4a1Hl9VFdQWvgB1UMGoU94pad1P/8N7fMcEnLnSiju8A==
  dependencies:
    "@types/json-schema" "^7.0.4"
    ajv "^6.12.2"
    ajv-keywords "^3.4.1"

schema-utils@^2.6.5:
  version "2.7.0"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.0.tgz"
  integrity sha512-0ilKFI6QQF5nxDZLFn2dMjvc4hjg/Wkg7rHd3jK6/A4a1Hl9VFdQWvgB1UMGoU94pad1P/8N7fMcEnLnSiju8A==
  dependencies:
    "@types/json-schema" "^7.0.4"
    ajv "^6.12.2"
    ajv-keywords "^3.4.1"

scss-tokenizer@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npmmirror.com/scss-tokenizer/-/scss-tokenizer-0.2.3.tgz"
  integrity sha512-dYE8LhncfBUar6POCxMTm0Ln+erjeczqEvCJib5/7XNkdw1FkUGgwMPY360FY0FgPWQxHWCx29Jl3oejyGLM9Q==
  dependencies:
    js-base64 "^2.1.8"
    source-map "^0.4.2"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

select@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/select/-/select-1.1.2.tgz"
  integrity sha1-DnNQrN7ICxEIUoeG7B1EGNEbOW0=

selfsigned@^1.10.7, selfsigned@^1.9.1:
  version "1.10.7"
  resolved "https://registry.npmjs.org/selfsigned/-/selfsigned-1.10.7.tgz"
  integrity sha512-8M3wBCzeWIJnQfl43IKwOmC4H/RAp50S8DF60znzjW5GVqTcSe2vWclt7hmYVPkKPlHWOu5EaWOMZ2Y6W8ZXTA==
  dependencies:
    node-forge "0.9.0"

semver@^5.3.0, semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0, "semver@2 || 3 || 4 || 5":
  version "5.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==

semver@^6.0.0, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmmirror.com/semver/-/semver-6.3.0.tgz"
  integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==

semver@~5.3.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/semver/-/semver-5.3.0.tgz"
  integrity sha512-mfmm3/H9+67MCVix1h+IXTpDwL6710LyHuk7+cWC9T1mE0qz4iHhh6r4hU2wrIT9iTsAAC2XQRvfblL028cpLw==

send@0.17.1:
  version "0.17.1"
  resolved "https://registry.npmjs.org/send/-/send-0.17.1.tgz"
  integrity sha512-BsVKsiGcQMFwT8UxypobUKyv7irCNRHk1T0G680vk88yf6LBByGcZJOTJCrTP2xVN6yI+XjPJcNuE3V4fT9sAg==
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^1.4.0:
  version "1.9.1"
  resolved "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-1.9.1.tgz"
  integrity sha512-0Vb/54WJ6k5v8sSWN09S0ora+Hnr+cX40r9F170nT+mSkaxltoE/7R3OrIdBSUv1OoiobH1QoWQbCnAO+e8J1A==

serialize-javascript@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-2.1.2.tgz"
  integrity sha512-rs9OggEUF0V4jUSecXazOYsLfu7OGK2qIn3c7IPBiffz32XniEp/TX9Xmc9LQfK2nQ2QKHvZ2oygKUGU0lG4jQ==

serialize-javascript@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-3.1.0.tgz"
  integrity sha512-JIJT1DGiWmIKhzRsG91aS6Ze4sFUrYbltlkg2onR5OrnNM02Kl/hnY/T4FN2omvyeBbQmMJv+K4cPOpGzOTFBg==
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.14.1:
  version "1.14.1"
  resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.14.1.tgz"
  integrity sha512-JMrvUwE54emCYWlTI+hGrGv5I8dEwmco/00EvkzIIsR7MqrHonbD9pO2MOfFnpFntl7ecpZs+3mW+XbQZu9QCg==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/set-value/-/set-value-2.0.1.tgz"
  integrity sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz"
  integrity sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.1.tgz"
  integrity sha512-JvdAWfbXeIGaZ9cILp38HntZSFSo3mWg6xGcJJsd+d4aRMOqauag1C63dJfDw7OaMYwEbHMOxEZ1lqVRYP2OAw==

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz"
  integrity sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/shallow-clone/-/shallow-clone-3.0.1.tgz"
  integrity sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==
  dependencies:
    kind-of "^6.0.2"

shallow-equal@^1.0.0:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/shallow-equal/download/shallow-equal-1.2.1.tgz"
  integrity sha1-TBar+lYEOqINBQMk76aJQLDaedo=

shallowequal@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/shallowequal/download/shallowequal-1.1.0.tgz"
  integrity sha1-GI1SHelbkIdAT9TctosT3wrk5/g=

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

shell-quote@^1.6.1:
  version "1.7.2"
  resolved "https://registry.npmjs.org/shell-quote/-/shell-quote-1.7.2.tgz"
  integrity sha512-mRz/m/JVscCrkMyPqHc/bczi3OQHkLTqXHEFu0zDhK/qfv3UcOA4SVmRCLmos4bhjr9ekVQubj/R7waKapmiQg==

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.3.tgz"
  integrity sha512-VUJ49FC8U1OxwZLxIbTTrDvLnf/6TDgxZcK8wxR8zs13xpx7xbG60ndBlhNrFi2EMuFRoeDoJO7wthSLq42EjA==

simple-concat@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/simple-concat/-/simple-concat-1.0.1.tgz"
  integrity sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==

simple-get@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/simple-get/-/simple-get-4.0.1.tgz"
  integrity sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA==
  dependencies:
    decompress-response "^6.0.0"
    once "^1.3.1"
    simple-concat "^1.0.0"

simple-statistics@^6.1.0:
  version "6.1.1"
  resolved "https://registry.npmjs.org/simple-statistics/-/simple-statistics-6.1.1.tgz"
  integrity sha512-zGwn0DDRa9Zel4H4n2pjTFIyGoAGpnpjrGIctreCxj5XWrcx9v7Xy7270FkC967WMmcvuc8ZU7m0ZG+hGN7gAA==

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slash@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-2.0.0.tgz"
  integrity sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A==

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/slice-ansi/-/slice-ansi-2.1.0.tgz"
  integrity sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ==
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

slice-ansi@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/slice-ansi/-/slice-ansi-1.0.0.tgz"
  integrity sha512-POqxBK6Lb3q6s047D/XsDVNPnF9Dl8JSaqe9h9lURl0OdNqy/ujDrOiIHtsqXMGbWWTIomRzAMaTyawAU//Reg==
  dependencies:
    is-fullwidth-code-point "^2.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  integrity sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  integrity sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz"
  integrity sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@1.1.5:
  version "1.1.5"
  resolved "https://registry.npmjs.org/sockjs-client/-/sockjs-client-1.1.5.tgz"
  integrity sha1-G7fA9yIsQPQq3xT0RCy9Eml3GoM=
  dependencies:
    debug "^2.6.6"
    eventsource "0.1.6"
    faye-websocket "~0.11.0"
    inherits "^2.0.1"
    json3 "^3.3.2"
    url-parse "^1.1.8"

sockjs-client@1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/sockjs-client/-/sockjs-client-1.4.0.tgz"
  integrity sha512-5zaLyO8/nri5cua0VtOrFXBPK1jbL4+1cebT/mmKA1E1ZXOvJrII75bPu0l0k843G/+iAbhEqzyKr0w/eCCj7g==
  dependencies:
    debug "^3.2.5"
    eventsource "^1.0.7"
    faye-websocket "~0.11.1"
    inherits "^2.0.3"
    json3 "^3.3.2"
    url-parse "^1.4.3"

sockjs@0.3.19:
  version "0.3.19"
  resolved "https://registry.npmjs.org/sockjs/-/sockjs-0.3.19.tgz"
  integrity sha512-V48klKZl8T6MzatbLlzzRNhMepEys9Y4oGFpypBFFn1gLI/QQ9HtLLyWJNbPlwGLelOVOEijUbTTJeLLI59jLw==
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^3.0.1"

sockjs@0.3.20:
  version "0.3.20"
  resolved "https://registry.npmjs.org/sockjs/-/sockjs-0.3.20.tgz"
  integrity sha512-SpmVOVpdq0DJc0qArhF3E5xsxvaiqGNb73XfgBpK1y3UD5gs8DSo8aCTsuT5pX8rssdc2NDIzANwP9eCAiSdTA==
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^3.4.0"
    websocket-driver "0.6.5"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/sort-keys/-/sort-keys-1.1.2.tgz"
  integrity sha1-RBttTTRnmPG05J6JIK37oOVD+a0=
  dependencies:
    is-plain-obj "^1.0.0"

sortablejs@^1.10.1:
  version "1.10.2"
  resolved "https://registry.npmjs.org/sortablejs/-/sortablejs-1.10.2.tgz"
  integrity sha512-YkPGufevysvfwn5rfdlGyrGjt7/CRHwvRPogD/lC+TnvcN29jDpCifKP+rBqf+LRldfXSTh+0CGLcSg0VIxq3A==

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz"
  integrity sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==

source-map-resolve@^0.5.0, source-map-resolve@^0.5.2:
  version "0.5.3"
  resolved "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.3.tgz"
  integrity sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.3.2:
  version "0.3.3"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.3.3.tgz"
  integrity sha1-NJAJd9W6PwfHdX7nLnO7GptTdU8=
  dependencies:
    source-map "0.1.32"

source-map-support@^0.4.15:
  version "0.4.18"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.4.18.tgz"
  integrity sha512-try0/JqxPLF9nOjvSta7tVondkP5dwgyLDjVoyMDlmjugT2lRZ1OfsrYTkCd2hkDnJTKRbO/Rl3orm8vlsUzbA==
  dependencies:
    source-map "^0.5.6"

source-map-support@~0.5.12:
  version "0.5.19"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.19.tgz"
  integrity sha512-Wonm7zOCIJzBGQdB+thsPar0kYuCIzYvxZwlBa87yi/Mdjv7Tip2cyVbLj5o0cFPN4EVkuTwb3GDDyUx2DGnGw==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.0.tgz"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.4.2:
  version "0.4.4"
  resolved "https://registry.npmmirror.com/source-map/-/source-map-0.4.4.tgz"
  integrity sha512-Y8nIfcb1s/7DcobUz1yOO1GSp7gyL+D9zLHDehT7iRESqGSxjJ448Sg7rvfgsRJCnKLdSl11uGf0s9X80cH0/A==
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.5.3:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.5.6:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@^0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@^0.7.3:
  version "0.7.3"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.7.3.tgz"
  integrity sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ==

source-map@~0.5.1:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@~0.6.0:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@0.1.32:
  version "0.1.32"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.1.32.tgz"
  integrity sha1-yLbBZ3l7pHQKjqMyUhYv8IWRsmY=
  dependencies:
    amdefine ">=0.0.4"

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.1.1.tgz"
  integrity sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  integrity sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.5"
  resolved "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.5.tgz"
  integrity sha512-J+FWzZoynJEXGphVIS+XEh3kFSjZX/1i9gFBaWQcB+/tmpe2qUsSBABpcxqxnAxFdiUFEgAX1bjYGQvIZmoz9Q==

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz"
  integrity sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.0, spdy@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/spdy/-/spdy-4.0.2.tgz"
  integrity sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz"
  integrity sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://registry.npmjs.org/sshpk/-/sshpk-1.16.1.tgz"
  integrity sha512-HXXqVUq7+pcKeLqqZj6mHFUMvXtOJt1uoUx09pFW6011inTMxqI8BA8PM95myrIyyKwdnzjdFjLiE6KBPVtJIg==
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^5.2.4:
  version "5.3.0"
  resolved "https://registry.npmjs.org/ssri/-/ssri-5.3.0.tgz"
  integrity sha512-XRSIPqLij52MtgoQavH/x/dU1qVKtWUAAZeOHsR9c2Ddi4XerFy3mc1alf+dLJKl9EUIm/Ht+EowFkTUOA6GAQ==
  dependencies:
    safe-buffer "^5.1.1"

ssri@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/ssri/-/ssri-6.0.1.tgz"
  integrity sha512-3Wge10hNcT1Kur4PDFwEieXSCMCJs/7WvSACcrMYrNp+b8kDL1/0wJch5Ni2WrtwEa2IO8OsVfeKIciKCDx/QA==
  dependencies:
    figgy-pudding "^3.5.1"

ssri@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/ssri/-/ssri-7.1.0.tgz"
  integrity sha512-77/WrDZUWocK0mvA5NTRQyveUf+wsrIc6vyrxpS8tVvYBcX215QbafrJR3KtkpskIzoFLqqNuuYQvxaMjXJ/0g==
  dependencies:
    figgy-pudding "^3.5.1"
    minipass "^3.1.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz"
  integrity sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==

stackframe@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/stackframe/-/stackframe-1.2.0.tgz"
  integrity sha512-GrdeshiRmS1YLMYgzF16olf2jJ/IzxXY9lhKOskuVziubpTYcYqyOwYeJKzQkwy7uN0fYSsbsC4RQaXf9LCrYA==

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stdout-stream@^1.4.0:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/stdout-stream/-/stdout-stream-1.4.1.tgz"
  integrity sha512-j4emi03KXqJWcIeF8eIXkjMFN1Cmb8gUlDYGeBALLPo5qdyTfA9bOtl8m33lRoC+vFMkP3gl0WsDr6+gzxbbTA==
  dependencies:
    readable-stream "^2.0.1"

stealthy-require@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/stealthy-require/-/stealthy-require-1.1.1.tgz"
  integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/stream-browserify/-/stream-browserify-2.0.2.tgz"
  integrity sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg==
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "https://registry.npmjs.org/stream-each/-/stream-each-1.2.3.tgz"
  integrity sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "https://registry.npmjs.org/stream-http/-/stream-http-2.8.3.tgz"
  integrity sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.1.tgz"
  integrity sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ==

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

string_decoder@^1.0.0, string_decoder@^1.1.1, string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

string-convert@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/string-convert/download/string-convert-0.2.1.tgz"
  integrity sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=

string-split-by@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/string-split-by/-/string-split-by-1.0.0.tgz"
  integrity sha512-KaJKY+hfpzNyet/emP81PJA9hTVSfxNLS9SFTWxdCnnW1/zOOwiV248+EfoX7IQFcBaOp4G5YE6xTJMF+pLg6A==
  dependencies:
    parenthesis "^3.1.5"

string-width@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/string-width/-/string-width-1.0.2.tgz"
  integrity sha512-0XsVpQLnVCXHJfyEs8tC0zpTVIr5PKKsQtkT29IwupnPTjtPmQ3xT/4yCREF9hYkV/3M3kzcUTSAZT6a6h81tw==
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^2.0.0, string-width@^2.1.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz"
  integrity sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/string-width/-/string-width-3.1.0.tgz"
  integrity sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.0.tgz"
  integrity sha512-zUz5JD+tgqtuDjMhwIg5uFVV3dtqZ9yQJlZVfq4I01/K5Paj5UHj7VyrQOJvzawSVlKpObApbfD0Ed6yJc+1eg==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string.prototype.padend@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/string.prototype.padend/-/string.prototype.padend-3.1.0.tgz"
  integrity sha512-3aIv8Ffdp8EZj8iLwREGpQaUZiPyrWrpzMBHvkiSW/bK/EGve9np07Vwy7IJ5waydpGXzQZu/F8Oze2/IWkBaA==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

string.prototype.padstart@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/string.prototype.padstart/-/string.prototype.padstart-3.1.0.tgz"
  integrity sha512-envqZvUp2JItI+OeQ5UAh1ihbAV5G/2bixTojvlIa090GGqF+NQRxbWb2nv9fTGrZABv6+pE6jXoAZhhS2k4Hw==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

string.prototype.trim@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.1.tgz"
  integrity sha512-MjGFEeqixw47dAMFMtgUro/I0+wNqZB5GKXGt1fFr24u3TzDXCPu7J9Buppzoe3r/LqkSDLDDJzE15RGWDGAVw==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"
    function-bind "^1.1.1"

string.prototype.trimend@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.1.tgz"
  integrity sha512-LRPxFUaTtpqYsTeNKaFOw3R4bxIzWOnbQ837QfBylo8jIxtcbK/A/sMV7Q+OAV/vWo+7s25pOE10KYSjaSO06g==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trimstart@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.1.tgz"
  integrity sha512-XxZn+QpvrBI1FOcg6dIpxUPgWCPuNXvMD72aaRaUQv1eD4e/Qy8i/hFTe0BUmD60p/QA6bh1avmuPTfNjqVWRw==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz"
  integrity sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.0.tgz"
  integrity sha512-AuvKTrTfQNYNIctbR1K/YGTR1756GycPsg7b9bdV9Duqur4gv6aKqHXah67Z8ImS7WEz5QVcOtlfW2rZEugt6w==
  dependencies:
    ansi-regex "^5.0.0"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz"
  integrity sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=
  dependencies:
    get-stdin "^4.0.1"

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/strip-indent/-/strip-indent-2.0.0.tgz"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-json-comments@^2.0.1, strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

style-loader@^0.19.0:
  version "0.19.1"
  resolved "https://registry.npmjs.org/style-loader/-/style-loader-0.19.1.tgz"
  integrity sha512-IRE+ijgojrygQi3rsqT0U4dd+UcPCqcVvauZpCnQrGAlEe+FUIyrK93bUDScamesjP08JlQNsFJU+KmPedP5Og==
  dependencies:
    loader-utils "^1.0.2"
    schema-utils "^0.3.0"

stylehacks@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npmjs.org/stylehacks/-/stylehacks-4.0.3.tgz"
  integrity sha512-7GlLk9JwlElY4Y6a/rmbH2MhVlTyVmiJd1PfTCqFaIBEGMYNsrO/v3SeGTdhBThLg4Z+NbOk/qFMwCa+J+3p/g==
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz"
  integrity sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=
  dependencies:
    has-flag "^1.0.0"

supports-color@^4.2.1:
  version "4.5.0"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-4.5.0.tgz"
  integrity sha512-ycQR/UbvI9xIlEdQT1TQqwoXtEldExbCEAJgRo5YXlmSKjv6ThHnP9/vwGa1gr19Gfw+LkFd7KqYMhzrRC5JYw==
  dependencies:
    has-flag "^2.0.0"

supports-color@^5.1.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^5.3.0, supports-color@^5.4.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-6.1.0.tgz"
  integrity sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.1.0.tgz"
  integrity sha512-oRSIpR8pxT1Wr2FquTNnGet79b3BWljqOuoW/h4oBhxJ/HUbX5nX6JSruTkvXDCFMwDPvsaTTbvMLKZWSy0R5g==
  dependencies:
    has-flag "^4.0.0"

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/svg-tags/-/svg-tags-1.0.0.tgz"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

svgo@^0.7.0:
  version "0.7.2"
  resolved "https://registry.npmjs.org/svgo/-/svgo-0.7.2.tgz"
  integrity sha1-n1dyQTlSE1xv779Ar+ak+qiLS7U=
  dependencies:
    coa "~1.0.1"
    colors "~1.1.2"
    csso "~2.3.1"
    js-yaml "~3.7.0"
    mkdirp "~0.5.1"
    sax "~1.2.1"
    whet.extend "~0.9.9"

svgo@^1.0.0:
  version "1.3.2"
  resolved "https://registry.npmjs.org/svgo/-/svgo-1.3.2.tgz"
  integrity sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw==
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

table@^5.2.3:
  version "5.4.6"
  resolved "https://registry.npmjs.org/table/-/table-5.4.6.tgz"
  integrity sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

table@4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/table/-/table-4.0.2.tgz"
  integrity sha512-UUkEAPdSGxtRpiV9ozJ5cMTtYiqz7Ni1OGqLXRCynrvzdtR1p+cfOWe2RJLwvUG8hNanaSRjecIqwOjqeatDsA==
  dependencies:
    ajv "^5.2.3"
    ajv-keywords "^2.1.0"
    chalk "^2.1.0"
    lodash "^4.17.4"
    slice-ansi "1.0.0"
    string-width "^2.1.1"

tapable@^0.2.7:
  version "0.2.9"
  resolved "https://registry.npmmirror.com/tapable/-/tapable-0.2.9.tgz"
  integrity sha512-2wsvQ+4GwBvLPLWsNfLCDYGsW6xb7aeC6utq2Qh0PFwgEy7K7dsma9Jsmb2zSQj7GvYAyUGSntLtsv++GmgL1A==

tapable@^1.0.0, tapable@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz"
  integrity sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==

tape@^4.5.1:
  version "4.13.3"
  resolved "https://registry.npmjs.org/tape/-/tape-4.13.3.tgz"
  integrity sha512-0/Y20PwRIUkQcTCSi4AASs+OANZZwqPKaipGCEwp10dQMipVvSZwUUCi01Y/OklIGyHKFhIcjock+DKnBfLAFw==
  dependencies:
    deep-equal "~1.1.1"
    defined "~1.0.0"
    dotignore "~0.1.2"
    for-each "~0.3.3"
    function-bind "~1.1.1"
    glob "~7.1.6"
    has "~1.0.3"
    inherits "~2.0.4"
    is-regex "~1.0.5"
    minimist "~1.2.5"
    object-inspect "~1.7.0"
    resolve "~1.17.0"
    resumer "~0.0.0"
    string.prototype.trim "~1.2.1"
    through "~2.3.8"

tar@^2.0.0:
  version "2.2.2"
  resolved "https://registry.npmmirror.com/tar/-/tar-2.2.2.tgz"
  integrity sha512-FCEhQ/4rE1zYv9rYXJw/msRqsnmlje5jHP6huWeBZ704jUTy02c5AZyWujpMR1ax6mVw9NyJMfuK2CMDWVIfgA==
  dependencies:
    block-stream "*"
    fstream "^1.0.12"
    inherits "2"

terser-webpack-plugin@^1.2.3, terser-webpack-plugin@^1.4.3:
  version "1.4.4"
  resolved "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.4.4.tgz"
  integrity sha512-U4mACBHIegmfoEe5fdongHESNJWqsGU+W0S/9+BmYGVQDw1+c2Ow05TpMhxjPK1sRb7cuYq1BPl1e5YHJMTCqA==
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^3.1.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser@^4.1.2, terser@^4.6.3:
  version "4.8.0"
  resolved "https://registry.npmjs.org/terser/-/terser-4.8.0.tgz"
  integrity sha512-EAPipTNeWsb/3wLPeup1tVPaXfIaU68xMnVdPafIL1TV05OhASArYyIfFvnvJCNrR2NIOvDVNNTFRa+Re2MWyw==
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

text-segmentation@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/text-segmentation/-/text-segmentation-1.0.3.tgz"
  integrity sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==
  dependencies:
    utrie "^1.0.2"

text-table@^0.2.0, text-table@~0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

thread-loader@^2.1.2:
  version "2.1.3"
  resolved "https://registry.npmjs.org/thread-loader/-/thread-loader-2.1.3.tgz"
  integrity sha512-wNrVKH2Lcf8ZrWxDF/khdlLlsTMczdcwPA9VEK4c2exlEPynYWxi9op3nPTo5lAnDIkE0rQEB3VBP+4Zncc9Hg==
  dependencies:
    loader-runner "^2.3.1"
    loader-utils "^1.1.0"
    neo-async "^2.6.0"

through@^2.3.6, through@~2.3.4, through@~2.3.8:
  version "2.3.8"
  resolved "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

through2@^2.0.0:
  version "2.0.5"
  resolved "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz"
  integrity sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz"
  integrity sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==

time-stamp@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/time-stamp/-/time-stamp-2.2.0.tgz"
  integrity sha512-zxke8goJQpBeEgD82CXABeMh0LSJcj7CXEd0OHOg45HgcofF7pxNwZm9+RknpxpDhwN4gFpySkApKfFYfRQnUA==

timers-browserify@^2.0.4:
  version "2.0.11"
  resolved "https://registry.npmjs.org/timers-browserify/-/timers-browserify-2.0.11.tgz"
  integrity sha512-60aV6sgJ5YEbzUdn9c8kYGIqOubPoUdqQCul3SBAsRCZ40s6Y5cMcrW4dt3/k/EsbLVJNl9n6Vz3fTc+k2GeKQ==
  dependencies:
    setimmediate "^1.0.4"

timsort@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/timsort/-/timsort-0.3.0.tgz"
  integrity sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=

tiny-emitter@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-2.1.0.tgz"
  integrity sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q==

tinycolor2@^1.4.1:
  version "1.4.2"
  resolved "https://registry.npm.taobao.org/tinycolor2/download/tinycolor2-1.4.2.tgz?cache=0&sync_timestamp=1601056395015&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftinycolor2%2Fdownload%2Ftinycolor2-1.4.2.tgz"
  integrity sha1-P2pNEHGtB2dtf6Ry4frECnGdiAM=

tinymce@^5.0.2:
  version "5.4.1"
  resolved "https://registry.npmjs.org/tinymce/-/tinymce-5.4.1.tgz"
  integrity sha512-eUjwDVCTSHSnFfpzX5TjTHXsGj5gvPQwzo3RFDF374gfK5voHqcRGojvQnu4NtNW1BAb012/KW1tkXBbUrEveg==

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz"
  integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
  dependencies:
    os-tmpdir "~1.0.2"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.3.tgz"
  integrity sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz"
  integrity sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.0.tgz"
  integrity sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw==

topojson-client@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/topojson-client/-/topojson-client-3.1.0.tgz"
  integrity sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==
  dependencies:
    commander "2"

toposort@^1.0.0:
  version "1.0.7"
  resolved "https://registry.npmjs.org/toposort/-/toposort-1.0.7.tgz"
  integrity sha1-LmhELZ9k7HILjMieZEOsbKqVACk=

tough-cookie@^2.3.3, tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz"
  integrity sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz"
  integrity sha1-WIeWa7WCpFA6QetST301ARgVphM=

trim-right@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/trim-right/-/trim-right-1.0.1.tgz"
  integrity sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=

"true-case-path@^1.0.2":
  version "1.0.3"
  resolved "https://registry.npmmirror.com/true-case-path/-/true-case-path-1.0.3.tgz"
  integrity sha512-m6s2OdQe5wgpFMC+pAJ+q9djG82O2jcHPOI6RNg1yy9rCYR+WD6Nbpl32fDpfC56nirdRy+opFa/Vk7HYhqaew==
  dependencies:
    glob "^7.1.2"

tryer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/tryer/-/tryer-1.0.1.tgz"
  integrity sha512-c3zayb8/kWWpycWYg87P71E1S1ZL6b6IJxfb5fvsUgsf0S2MVGaDhDXXjDMpdCpfWXqptc+4mXwmiy1ypXqRAA==

tsconfig-paths@^3.9.0:
  version "3.9.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.9.0.tgz"
  integrity sha512-dRcuzokWhajtZWkQsDVKbWyY+jgcLC5sqJhg2PSgf4ZkH2aHPvaOY8YWGhmjb68b5qqTfasSsDO9k7RUiEmZAw==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tslib@^1.10.0, tslib@^1.9.0:
  version "1.13.0"
  resolved "https://registry.npmjs.org/tslib/-/tslib-1.13.0.tgz"
  integrity sha512-i/6DQjL8Xf3be4K/E6Wgpekn5Qasl1usyw++dAA35Ue5orEn65VIxOA+YvNNl9HV3qv70T7CNwjODHZrLwvd1Q==

tslib@2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz"
  integrity sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==

tsml@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/tsml/-/tsml-1.0.1.tgz"
  integrity sha512-3KmepnH9SUsoOVtg013CRrL7c+AK7ECaquAsJdvu4288EDJuraqBlP4PDXT/rLEJ9YDn4jqLAzRJsnFPx+V6lg==

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.npmjs.org/tty-browserify/-/tty-browserify-0.0.0.tgz"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz"
  integrity sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==

type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type@^1.0.1:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/type/-/type-1.2.0.tgz"
  integrity sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg==

type@^2.7.2:
  version "2.7.2"
  resolved "https://registry.npmmirror.com/type/-/type-2.7.2.tgz"
  integrity sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw==

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

uglify-js@^2.6.2, uglify-js@^2.8.29:
  version "2.8.29"
  resolved "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz"
  integrity sha1-KcVzMUgFe7Th913zW3qcty5qWd0=
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-js@3.4.x:
  version "3.4.10"
  resolved "https://registry.npmjs.org/uglify-js/-/uglify-js-3.4.10.tgz"
  integrity sha512-Y2VsbPVs0FIshJztycsO2SfPk7/KAF/T72qzv9u5EpQ4kB2hQoHlhNQTsNyy6ul7lQtqJN/AoWeS23OzEiEFxw==
  dependencies:
    commander "~2.19.0"
    source-map "~0.6.1"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz"
  integrity sha1-bgkk1r2mta/jSeOabWMoUKD4grc=

uglifyjs-webpack-plugin@^0.4.6:
  version "0.4.6"
  resolved "https://registry.npmmirror.com/uglifyjs-webpack-plugin/-/uglifyjs-webpack-plugin-0.4.6.tgz"
  integrity sha512-TNM20HMW67kxHRNCZdvLyiwE1ST6WyY5Ae+TG55V81NpvNwJ9+V4/po4LHA1R9afV/WrqzfedG2UJCk2+swirw==
  dependencies:
    source-map "^0.5.6"
    uglify-js "^2.8.29"
    webpack-sources "^1.0.1"

unicode-canonical-property-names-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz"
  integrity sha512-jDrNnXWHd4oHiTZnx/ZG7gtUTVp+gCcTTKr8L0HjlwphROEW3+Him+IpvC+xcJEFegapiMZyZe02CyuOnRmbnQ==

unicode-match-property-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz"
  integrity sha512-L4Qoh15vTfntsn4P1zqnHulG0LdXgjSO035fEpdtp6YxXhMT51Q6vgM5lYdG/5X3MjS+k/Y9Xw4SFCY9IkR0rg==
  dependencies:
    unicode-canonical-property-names-ecmascript "^1.0.4"
    unicode-property-aliases-ecmascript "^1.0.4"

unicode-match-property-value-ecmascript@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.2.0.tgz"
  integrity sha512-wjuQHGQVofmSJv1uVISKLE5zO2rNGzM/KCYZch/QQvez7C1hUhBIuZ701fYXExuufJFMPhv2SyL8CyoIfMLbIQ==

unicode-property-aliases-ecmascript@^1.0.4:
  version "1.1.0"
  resolved "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.1.0.tgz"
  integrity sha512-PqSoPh/pWetQ2phoj5RLiaqIk4kCNwoV3CI+LfGmWLKI3rE3kl1h59XpX2BjgDrmbxD9ARtQobPGU1SguCYuQg==

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/union-value/-/union-value-1.0.1.tgz"
  integrity sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

uniq@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/uniq/-/uniq-1.0.1.tgz"
  integrity sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=

uniqs@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/uniqs/-/uniqs-2.0.0.tgz"
  integrity sha1-/+3ks2slKQaW5uFl1KWe25mOawI=

unique-filename@^1.1.0, unique-filename@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/unique-filename/-/unique-filename-1.1.1.tgz"
  integrity sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/unique-slug/-/unique-slug-2.0.2.tgz"
  integrity sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==
  dependencies:
    imurmurhash "^0.1.4"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
  integrity sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/unquote/-/unquote-1.1.1.tgz"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/upath/-/upath-1.2.0.tgz"
  integrity sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg==

upper-case@^1.1.1:
  version "1.1.3"
  resolved "https://registry.npmjs.org/upper-case/-/upper-case-1.1.3.tgz"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uri-js@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.2.2.tgz"
  integrity sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-loader@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/url-loader/-/url-loader-1.1.2.tgz"
  integrity sha512-dXHkKmw8FhPqu8asTc1puBfe3TehOCo2+RmOOev5suNCIYBcT626kxiWg1NBVkwc4rO8BGa7gP70W7VXuqHrjg==
  dependencies:
    loader-utils "^1.1.0"
    mime "^2.0.3"
    schema-utils "^1.0.0"

url-parse@^1.1.8, url-parse@^1.4.3:
  version "1.4.7"
  resolved "https://registry.npmjs.org/url-parse/-/url-parse-1.4.7.tgz"
  integrity sha512-d3uaVyzDB9tQoSXFvuSUNFibTd9zxd2bkVrDRvF5TmvWWQwqE4lgYJ5m+x1DbecWkw+LK4RNl2CU1hHuOKPVlg==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url-toolkit@^2.1.3:
  version "2.2.5"
  resolved "https://registry.npmjs.org/url-toolkit/-/url-toolkit-2.2.5.tgz"
  integrity sha512-mtN6xk+Nac+oyJ/PrI7tzfmomRVNFIWKUbG8jdYFt52hxbiReFAXIjYskvu64/dvuW71IcB7lV8l0HvZMac6Jg==

url@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npmjs.org/url/-/url-0.11.0.tgz"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/use/-/use-3.1.1.tgz"
  integrity sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.1.tgz"
  integrity sha512-g9JpC/3He3bm38zsLupWryXHoEcS22YHthuPQSJdMy6KNrzIRzWqcsHzD/WUnqe45whVou4VIsPew37DoXWNrA==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

util.promisify@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.0.tgz"
  integrity sha512-i+6qA2MPhvoKLuxnJNpXAGhg7HphQOSUq2LKMZD0m15EiskXUkMvKdF4Uui0WYeCUGea+o2cw/ZuwehtfsrNkA==
  dependencies:
    define-properties "^1.1.2"
    object.getownpropertydescriptors "^2.0.3"

util@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npmjs.org/util/-/util-0.11.1.tgz"
  integrity sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ==
  dependencies:
    inherits "2.0.3"

util@0.10.3:
  version "0.10.3"
  resolved "https://registry.npmjs.org/util/-/util-0.10.3.tgz"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

utila@^0.4.0, utila@~0.4:
  version "0.4.0"
  resolved "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

utrie@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/utrie/-/utrie-1.0.2.tgz"
  integrity sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==
  dependencies:
    base64-arraybuffer "^1.0.2"

uuid@^3.0.1, uuid@^3.3.2, uuid@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz"
  integrity sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vendors@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmjs.org/vendors/-/vendors-1.0.4.tgz"
  integrity sha512-/juG65kTL4Cy2su4P8HjtkTxk6VmJDiOPBufWniqQ6wknac6jNiXS9vU+hO3wgusiyqWlzTbVHi0dyJqRONg3w==

venn.js@~0.2.20:
  version "0.2.20"
  resolved "https://registry.npmjs.org/venn.js/-/venn.js-0.2.20.tgz"
  integrity sha512-bb5SYq/wamY9fvcuErb9a0FJkgIFHJjkLZWonQ+DoKKuDX3WPH2B4ouI1ce4K2iejBklQy6r1ly8nOGIyOCO6w==
  dependencies:
    d3-selection "^1.0.2"
    d3-transition "^1.0.1"
    fmin "0.0.2"

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

"video.js@^5.17.0 || ^6.2.0", "video.js@^5.19.1 || ^6.2.0", "video.js@^6 || ^7", video.js@^6.6.0:
  version "6.13.0"
  resolved "https://registry.npmjs.org/video.js/-/video.js-6.13.0.tgz"
  integrity sha512-36/JR/GhPQSZj0o+GNbhcEYv/b0SkV9SQsjlodAnzMQYN0TA7VhmqrKPYMCi1NGRYu7S9W3OaFCFoUxkYfSVlg==
  dependencies:
    babel-runtime "^6.9.2"
    global "4.3.2"
    safe-json-parse "4.0.0"
    tsml "1.0.1"
    videojs-font "2.1.0"
    videojs-ie8 "1.1.2"
    videojs-vtt.js "0.12.6"
    xhr "2.4.0"

videojs-contrib-hls@^5.12.2:
  version "5.15.0"
  resolved "https://registry.npmjs.org/videojs-contrib-hls/-/videojs-contrib-hls-5.15.0.tgz"
  integrity sha512-18zbMYZ0XRBKTPEayA9bFTWWrqhT9b4G8+zf0czJLD7Epe5PcK1I/3dflTHQeQ5rwlWir+/XnFU3sMg/B2MMcw==
  dependencies:
    aes-decrypter "1.0.3"
    global "^4.3.0"
    m3u8-parser "2.1.0"
    mux.js "4.3.2"
    url-toolkit "^2.1.3"
    video.js "^5.19.1 || ^6.2.0"
    videojs-contrib-media-sources "4.7.2"
    webwackify "0.1.6"

videojs-contrib-media-sources@4.7.2:
  version "4.7.2"
  resolved "https://registry.npmjs.org/videojs-contrib-media-sources/-/videojs-contrib-media-sources-4.7.2.tgz"
  integrity sha512-e6iCHWBFuV05EGo7v+pS9iepObXnJ9joms467gzi8ZjpKVb3ifha9M0Ja24Rd8JfvYpzjltsgDVtGFDvIg4hQQ==
  dependencies:
    global "^4.3.0"
    mux.js "4.3.2"
    video.js "^5.17.0 || ^6.2.0"
    webwackify "0.1.6"

videojs-flash@^2.1.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/videojs-flash/-/videojs-flash-2.2.1.tgz"
  integrity sha512-mHu6TD12EKkxMvr8tg4AcfV/DuVLff427nneoZom3N9Dd2bv0sJOWwdLPQH1v5BCuAuXAVuAOba56ovTl+G3tQ==
  dependencies:
    global "^4.4.0"
    video.js "^6 || ^7"
    videojs-swf "5.4.2"

videojs-font@2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/videojs-font/-/videojs-font-2.1.0.tgz"
  integrity sha512-zFqWpLrXf1q8NtYx5qtZhMC6SLUFScDmR6j+UGPogobxR21lvXShhnzcNNMdOxJUuFLiToJ/BPpFUQwX4xhpvA==

videojs-hotkeys@^0.2.20:
  version "0.2.28"
  resolved "https://registry.npmjs.org/videojs-hotkeys/-/videojs-hotkeys-0.2.28.tgz"
  integrity sha512-M8rlD5OSB3EDRdbS4MRNlGKFpA2sSIStmUPvy5zfl/NigzWaN6r4wnb32rEN0v97GiQwmUfXSmqrPNrXhiFQmQ==

videojs-ie8@1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/videojs-ie8/-/videojs-ie8-1.1.2.tgz"
  integrity sha512-0Zb2T4MLkpfZbeGMK/Z93b8Lrepr+rLFoHgQV1CoDeFqXvH7b+Vsd/VHoILGxQrgCSHFQ7mAODR6oyMjuiD4/g==
  dependencies:
    es5-shim "^4.5.1"

videojs-swf@5.4.2:
  version "5.4.2"
  resolved "https://registry.npmjs.org/videojs-swf/-/videojs-swf-5.4.2.tgz"
  integrity sha512-FGg+Csioa8/A/EacvFefBdb9Z0rSiMlheHDunZnN3xXfUF43jvjawcWFQnZvrv1Cs1nE1LBrHyUZjF7j2mKOLw==

videojs-vtt.js@0.12.6:
  version "0.12.6"
  resolved "https://registry.npmjs.org/videojs-vtt.js/-/videojs-vtt.js-0.12.6.tgz"
  integrity sha512-XFXeGBQiljnElMhwCcZst0RDbZn2n8LU7ZScXryd3a00OaZsHAjdZu/7/RdSr7Z1jHphd45FnOvOQkGK4YrWCQ==
  dependencies:
    global "^4.3.1"

viser-vue@^2.4.8:
  version "2.4.8"
  resolved "https://registry.npmjs.org/viser-vue/-/viser-vue-2.4.8.tgz"
  integrity sha512-ERAREN+6k/ywrwT+swcMo4CDIAq6dBjnB0+lhmsSfaip06BGHSBfNKg6yl7/4GJ9Nk2kioUw3llNhEboJuIKmQ==
  dependencies:
    "@types/node" "*"
    viser "^2.0.0"
    vue "^2.5.3"

viser@^2.0.0:
  version "2.4.9"
  resolved "https://registry.npmjs.org/viser/-/viser-2.4.9.tgz"
  integrity sha512-DKsqtMa3TZYQHEZ7jp4kpNp1Iqomda7d+3IkkIjIdKQvfL8OeksXfy/ECZUY1hTrGoOe7cq85+6PMS+MPn4mgQ==
  dependencies:
    "@antv/g2" "~3.5.3"
    "@antv/g2-brush" "^0.0.2"
    "@antv/g2-plugin-slider" "^2.1.0"
    "@types/d3-format" "*"
    "@types/lodash" "*"
    "@types/node" "^8.0.53"
    d3-format "^1.3.0"
    lodash "^4.17.4"

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "https://registry.npmjs.org/vm-browserify/-/vm-browserify-1.1.2.tgz"
  integrity sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ==

vue-area-linkage@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/vue-area-linkage/-/vue-area-linkage-5.1.0.tgz"
  integrity sha512-86WD4O6PD9e2koWZ81Zlfcyey17U1+3B3fpWoyIZlBIe2V6qfvrg9BTozWdTpdF4CmKBiz138LBRTKhawl/UWw==
  dependencies:
    lodash.find "^4.6.0"

vue-cropper@^0.5.4:
  version "0.5.4"
  resolved "https://registry.npmjs.org/vue-cropper/-/vue-cropper-0.5.4.tgz"
  integrity sha512-9wwHOk1eHdl+nyq2fnIFDeDMjaXPh1dqfY+FdeEGU7Z/cKVOyH0pLrW0/5NH3xnRzqrU3DAQ8LjC/9/eHdvqKA==

vue-eslint-parser@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/vue-eslint-parser/-/vue-eslint-parser-2.0.3.tgz"
  integrity sha512-ZezcU71Owm84xVF6gfurBQUGg8WQ+WZGxgDEQu1IHFBZNx7BFZg3L1yHxrCBNNwbwFtE1GuvfJKMtb6Xuwc/Bw==
  dependencies:
    debug "^3.1.0"
    eslint-scope "^3.7.1"
    eslint-visitor-keys "^1.0.0"
    espree "^3.5.2"
    esquery "^1.0.0"
    lodash "^4.17.4"

vue-eslint-parser@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/vue-eslint-parser/-/vue-eslint-parser-5.0.0.tgz"
  integrity sha512-JlHVZwBBTNVvzmifwjpZYn0oPWH2SgWv5dojlZBsrhablDu95VFD+hriB1rQGwbD+bms6g+rAFhQHk6+NyiS6g==
  dependencies:
    debug "^4.1.0"
    eslint-scope "^4.0.0"
    eslint-visitor-keys "^1.0.0"
    espree "^4.1.0"
    esquery "^1.0.1"
    lodash "^4.17.11"

vue-hot-reload-api@^2.2.0, vue-hot-reload-api@^2.3.0:
  version "2.3.4"
  resolved "https://registry.npmjs.org/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz"
  integrity sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog==

vue-i18n@^8.7.0:
  version "8.18.2"
  resolved "https://registry.npmjs.org/vue-i18n/-/vue-i18n-8.18.2.tgz"
  integrity sha512-0X5nBTCZAVjlwcrPaYJwNs3iipBBTv0AUHwQUOa8yP3XbQGWKbRHqBb3OhCYtum/IHDD21d/df5Xd2VgyxbxfA==

vue-loader@^13.0.5:
  version "13.7.3"
  resolved "https://registry.npmjs.org/vue-loader/-/vue-loader-13.7.3.tgz"
  integrity sha512-ACCwbfeC6HjY2pnDii+Zer+MZ6sdOtwvLmDXRK/BoD3WNR551V22R6KEagwHoTRJ0ZlIhpCBkptpCU6+Ri/05w==
  dependencies:
    consolidate "^0.14.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    lru-cache "^4.1.1"
    postcss "^6.0.8"
    postcss-load-config "^1.1.0"
    postcss-selector-parser "^2.0.0"
    prettier "^1.7.0"
    resolve "^1.4.0"
    source-map "^0.6.1"
    vue-hot-reload-api "^2.2.0"
    vue-style-loader "^3.0.0"
    vue-template-es2015-compiler "^1.6.0"

vue-loader@^15.7.0:
  version "15.9.3"
  resolved "https://registry.npmjs.org/vue-loader/-/vue-loader-15.9.3.tgz"
  integrity sha512-Y67VnGGgVLH5Voostx8JBZgPQTlDQeOVBLOEsjc2cXbCYBKexSKEpOA56x0YZofoDOTszrLnIShyOX1p9uCEHA==
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    vue-hot-reload-api "^2.3.0"
    vue-style-loader "^4.1.0"

vue-ls@^3.2.0:
  version "3.2.1"
  resolved "https://registry.npmjs.org/vue-ls/-/vue-ls-3.2.1.tgz"
  integrity sha512-JoZDpSu0qV5AM50vspEzOut2lcm8R0tr5WG99jMHh14qFXkUes3ZdmDMG/p8iSQVQpg0pd/svGwM9Nd7OI2oaw==

vue-photo-preview@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/vue-photo-preview/-/vue-photo-preview-1.1.3.tgz"
  integrity sha512-L9JTQh62rYqLHNCdpy7zQdqVisks9dvkmUpM8+7kIKG7l+KkbRLzKWK7lCjCKc6OQT42o6/ngK6B13niXunS9Q==
  dependencies:
    autoprefixer-loader "^3.2.0"
    babel-core "^6.26.0"
    babel-loader "^7.1.2"
    babel-preset-env "^1.6.0"
    cross-env "^5.0.5"
    css-loader "^0.28.7"
    extract-text-webpack-plugin "^3.0.1"
    file-loader "^1.1.4"
    photoswipe "^4.1.2"
    resolve-url-loader "^2.1.1"
    style-loader "^0.19.0"
    vue "^2.4.4"
    vue-loader "^13.0.5"
    vue-template-compiler "^2.4.4"
    webpack "^3.6.0"
    webpack-dev-server "^2.9.1"

vue-print-nb-jeecg@^1.0.9:
  version "1.0.9"
  resolved "https://registry.npmjs.org/vue-print-nb-jeecg/-/vue-print-nb-jeecg-1.0.9.tgz"
  integrity sha512-dWLbThjeq/hBo50wcKoHrJAxLEZcqiwoIFEjZQEvntVQs76PdDrWvpkC8F8tGZ5G9GXs9ihrddkXpaIltHJ1dQ==
  dependencies:
    babel-plugin-transform-runtime "^6.23.0"

vue-qr@^4.0.9:
  version "4.0.9"
  resolved "https://registry.npmmirror.com/vue-qr/-/vue-qr-4.0.9.tgz"
  integrity sha512-pAISV94T0MNEYA3NGjykUpsXRE2QfaNxlu9ZhEL6CERgqNc21hJYuP3hRVzAWfBQlgO18DPmZTbrFerJC3+Ikw==
  dependencies:
    glob "^8.0.1"
    js-binary-schema-parser "^2.0.2"
    simple-get "^4.0.1"
    string-split-by "^1.0.0"

vue-ref@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/vue-ref/download/vue-ref-2.0.0.tgz"
  integrity sha1-SDCE1zKr7RHaeWd4qCZqOvDqGpw=

vue-router@^3.0.1:
  version "3.3.4"
  resolved "https://registry.npmjs.org/vue-router/-/vue-router-3.3.4.tgz"
  integrity sha512-SdKRBeoXUjaZ9R/8AyxsdTqkOfMcI5tWxPZOUX5Ie1BTL5rPSZ0O++pbiZCeYeythiZIdLEfkDiQPKIaWk5hDg==

vue-splitpane@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/vue-splitpane/-/vue-splitpane-1.0.6.tgz"
  integrity sha512-6sneVSEeF1VsCD025HP93nRxwzYhv3rotCeYi2Ah1FmGxwtfqbCZEIzaHGberdGNCpMlEzGD/1OIE1kK5QukLA==

vue-style-loader@^3.0.0:
  version "3.1.2"
  resolved "https://registry.npmjs.org/vue-style-loader/-/vue-style-loader-3.1.2.tgz"
  integrity sha512-ICtVdK/p+qXWpdSs2alWtsXt9YnDoYjQe0w5616j9+/EhjoxZkbun34uWgsMFnC1MhrMMwaWiImz3K2jK1Yp2Q==
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-style-loader@^4.1.0:
  version "4.1.2"
  resolved "https://registry.npmjs.org/vue-style-loader/-/vue-style-loader-4.1.2.tgz"
  integrity sha512-0ip8ge6Gzz/Bk0iHovU9XAUQaFt/G2B61bnWa2tCcqqdgfHs1lF9xXorFbE55Gmy92okFT+8bfmySuUOu13vxQ==
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-compiler@^2.0.0, vue-template-compiler@^2.4.4, vue-template-compiler@^2.6.0, vue-template-compiler@^2.6.10, vue-template-compiler@>=2.5.0:
  version "2.6.11"
  resolved "https://registry.npmjs.org/vue-template-compiler/-/vue-template-compiler-2.6.11.tgz"
  integrity sha512-KIq15bvQDrcCjpGjrAhx4mUlyyHfdmTaoNfeoATHLAiWB+MU3cx4lOzMwrnUh9cCxy0Lt1T11hAFY6TQgroUAA==
  dependencies:
    de-indent "^1.0.2"
    he "^1.1.0"

vue-template-es2015-compiler@^1.6.0, vue-template-es2015-compiler@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npmjs.org/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.9.1.tgz"
  integrity sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw==

vue-video-player@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/vue-video-player/-/vue-video-player-5.0.2.tgz"
  integrity sha512-IZXeRGGSX4YIp54G0Q5cB7iqh6Ok6Dpa2jRkjdyvMWw7MShJuh54/d5QNb1CZ+CvZUzX/TH7osnpir7mBNcFvQ==
  dependencies:
    object-assign "^4.1.1"
    video.js "^6.6.0"
    videojs-contrib-hls "^5.12.2"
    videojs-flash "^2.1.0"
    videojs-hotkeys "^0.2.20"

vue@^2.0.0, vue@^2.4.4, vue@^2.5.17, vue@^2.5.3, vue@^2.6.0, vue@^2.6.10, "vue@>= 2.6.0 < 3", vue@>=2.2.0, vue@>=2.5.0:
  version "2.6.11"
  resolved "https://registry.npmjs.org/vue/-/vue-2.6.11.tgz"
  integrity sha512-VfPwgcGABbGAue9+sfrD4PuwFar7gPb1yl1UK1MwXoQPAw0BKSqWfoYCT/ThFrdEVWoI51dBuyCoiNU9bZDZxQ==

vuedraggable@^2.20.0:
  version "2.24.0"
  resolved "https://registry.npmjs.org/vuedraggable/-/vuedraggable-2.24.0.tgz"
  integrity sha512-IlslPpc+iZ2zPNSJbydFZIDrE+don5u+Nc/bjT2YaF+Azidc+wxxJKfKT0NwE68AKk0syb0YbZneAcnynqREZQ==
  dependencies:
    sortablejs "^1.10.1"

vuex@^3.1.0:
  version "3.5.1"
  resolved "https://registry.npmjs.org/vuex/-/vuex-3.5.1.tgz"
  integrity sha512-w7oJzmHQs0FM9LXodfskhw9wgKBiaB+totOdb8sNzbTB2KDCEEwEs29NzBZFh/lmEK1t5tDmM1vtsO7ubG1DFw==

vxe-table-plugin-antd@1.8.10:
  version "1.8.10"
  resolved "https://registry.npm.taobao.org/vxe-table-plugin-antd/download/vxe-table-plugin-antd-1.8.10.tgz"
  integrity sha1-psAr/HzyJUYxETG4uFTAqtqwIOo=

vxe-table@>=1.15.27, vxe-table@2.9.13:
  version "2.9.13"
  resolved "https://registry.npm.taobao.org/vxe-table/download/vxe-table-2.9.13.tgz"
  integrity sha1-YZzVhRYN3ptSa5DOVSo1jss6YGA=

warning@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/warning/download/warning-4.0.3.tgz"
  integrity sha1-Fungd+uKhtavfWSqHgX9hbRnjKM=
  dependencies:
    loose-envify "^1.0.0"

watchpack-chokidar2@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/watchpack-chokidar2/-/watchpack-chokidar2-2.0.0.tgz"
  integrity sha512-9TyfOyN/zLUbA288wZ8IsMZ+6cbzvsNyEzSBp6e/zkifi6xxbl8SmQ/CxQq32k8NNqrdVEVUVSEf56L4rQ/ZxA==
  dependencies:
    chokidar "^2.1.8"

watchpack-chokidar2@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz"
  integrity sha512-nCFfBIPKr5Sh61s4LPpy1Wtfi0HE8isJ3d2Yb5/Ppw2P2B/3eVSEBjKfN0fmHJSK14+31KwMKmcrzs2GM4P0Ww==
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.4.0, watchpack@^1.6.1:
  version "1.7.2"
  resolved "https://registry.npmjs.org/watchpack/-/watchpack-1.7.2.tgz"
  integrity sha512-ymVbbQP40MFTp+cNMvpyBpBtygHnPzPkHqoIwRRj/0B8KhqQwV8LaKjtbaxF2lK4vl8zN9wCxS46IFCU5K4W0g==
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.0"
    watchpack-chokidar2 "^2.0.0"

watchpack@^1.7.4:
  version "1.7.5"
  resolved "https://registry.npmmirror.com/watchpack/-/watchpack-1.7.5.tgz"
  integrity sha512-9P3MWk6SrKjHsGkLT2KHXdQ/9SNkyoJbabxnKOoJepsvJjJG8uYTR3yTPxPQvNDI3w4Nz1xnE0TLHK4RIVe/MQ==
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.1"
    watchpack-chokidar2 "^2.0.1"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz"
  integrity sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webpack-bundle-analyzer@^3.3.0:
  version "3.8.0"
  resolved "https://registry.npmjs.org/webpack-bundle-analyzer/-/webpack-bundle-analyzer-3.8.0.tgz"
  integrity sha512-PODQhAYVEourCcOuU+NiYI7WdR8QyELZGgPvB1y2tjbUpbmcQOt5Q7jEK+ttd5se0KSBKD9SXHCEozS++Wllmw==
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"
    bfj "^6.1.1"
    chalk "^2.4.1"
    commander "^2.18.0"
    ejs "^2.6.1"
    express "^4.16.3"
    filesize "^3.6.1"
    gzip-size "^5.0.0"
    lodash "^4.17.15"
    mkdirp "^0.5.1"
    opener "^1.5.1"
    ws "^6.0.0"

webpack-chain@^4.11.0:
  version "4.12.1"
  resolved "https://registry.npmjs.org/webpack-chain/-/webpack-chain-4.12.1.tgz"
  integrity sha512-BCfKo2YkDe2ByqkEWe1Rw+zko4LsyS75LVr29C6xIrxAg9JHJ4pl8kaIZ396SUSNp6b4815dRZPSTAS8LlURRQ==
  dependencies:
    deepmerge "^1.5.2"
    javascript-stringify "^1.6.0"

webpack-cli@^4.10.0, webpack-cli@4.x.x:
  version "4.10.0"
  resolved "https://registry.npmmirror.com/webpack-cli/-/webpack-cli-4.10.0.tgz"
  integrity sha512-NLhDfH/h4O6UOy+0LSso42xvYypClINuMNBVVzX4vX98TmTaTUxwRbXdhucbFMd2qLaCTcLq/PdYrvi8onw90w==
  dependencies:
    "@discoveryjs/json-ext" "^0.5.0"
    "@webpack-cli/configtest" "^1.2.0"
    "@webpack-cli/info" "^1.5.0"
    "@webpack-cli/serve" "^1.7.0"
    colorette "^2.0.14"
    commander "^7.0.0"
    cross-spawn "^7.0.3"
    fastest-levenshtein "^1.0.12"
    import-local "^3.0.2"
    interpret "^2.2.0"
    rechoir "^0.7.0"
    webpack-merge "^5.7.3"

webpack-dev-middleware@^3.7.2:
  version "3.7.2"
  resolved "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-3.7.2.tgz"
  integrity sha512-1xC42LxbYoqLNAhV6YzTYacicgMZQTqRd27Sim9wn5hJrX3I5nxYy1SxSd4+gjUFsz1dQFj+yEe6zEVmSkeJjw==
  dependencies:
    memory-fs "^0.4.1"
    mime "^2.4.4"
    mkdirp "^0.5.1"
    range-parser "^1.2.1"
    webpack-log "^2.0.0"

webpack-dev-middleware@1.12.2:
  version "1.12.2"
  resolved "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-1.12.2.tgz"
  integrity sha512-FCrqPy1yy/sN6U/SaEZcHKRXGlqU0DUaEBL45jkUYoB8foVb6wCnbIJ1HKIx+qUFTW+3JpVcCJCxZ8VATL4e+A==
  dependencies:
    memory-fs "~0.4.1"
    mime "^1.5.0"
    path-is-absolute "^1.0.0"
    range-parser "^1.0.3"
    time-stamp "^2.0.0"

webpack-dev-server@^2.9.1:
  version "2.11.5"
  resolved "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-2.11.5.tgz"
  integrity sha512-7TdOKKt7G3sWEhPKV0zP+nD0c4V9YKUJ3wDdBwQsZNo58oZIRoVIu66pg7PYkBW8A74msP9C2kLwmxGHndz/pw==
  dependencies:
    ansi-html "0.0.7"
    array-includes "^3.0.3"
    bonjour "^3.5.0"
    chokidar "^2.1.2"
    compression "^1.7.3"
    connect-history-api-fallback "^1.3.0"
    debug "^3.1.0"
    del "^3.0.0"
    express "^4.16.2"
    html-entities "^1.2.0"
    http-proxy-middleware "^0.19.1"
    import-local "^1.0.0"
    internal-ip "1.2.0"
    ip "^1.1.5"
    killable "^1.0.0"
    loglevel "^1.4.1"
    opn "^5.1.0"
    portfinder "^1.0.9"
    selfsigned "^1.9.1"
    serve-index "^1.9.1"
    sockjs "0.3.19"
    sockjs-client "1.1.5"
    spdy "^4.0.0"
    strip-ansi "^3.0.0"
    supports-color "^5.1.0"
    webpack-dev-middleware "1.12.2"
    yargs "6.6.0"

webpack-dev-server@^3.4.1:
  version "3.11.0"
  resolved "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-3.11.0.tgz"
  integrity sha512-PUxZ+oSTxogFQgkTtFndEtJIPNmml7ExwufBZ9L2/Xyyd5PnOL5UreWe5ZT7IU25DSdykL9p1MLQzmLh2ljSeg==
  dependencies:
    ansi-html "0.0.7"
    bonjour "^3.5.0"
    chokidar "^2.1.8"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    debug "^4.1.1"
    del "^4.1.1"
    express "^4.17.1"
    html-entities "^1.3.1"
    http-proxy-middleware "0.19.1"
    import-local "^2.0.0"
    internal-ip "^4.3.0"
    ip "^1.1.5"
    is-absolute-url "^3.0.3"
    killable "^1.0.1"
    loglevel "^1.6.8"
    opn "^5.5.0"
    p-retry "^3.0.1"
    portfinder "^1.0.26"
    schema-utils "^1.0.0"
    selfsigned "^1.10.7"
    semver "^6.3.0"
    serve-index "^1.9.1"
    sockjs "0.3.20"
    sockjs-client "1.4.0"
    spdy "^4.0.2"
    strip-ansi "^3.0.1"
    supports-color "^6.1.0"
    url "^0.11.0"
    webpack-dev-middleware "^3.7.2"
    webpack-log "^2.0.0"
    ws "^6.2.1"
    yargs "^13.3.2"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/webpack-log/-/webpack-log-2.0.0.tgz"
  integrity sha512-cX8G2vR/85UYG59FgkoMamwHUIkSSlV3bBMRsbxVXVUk2j6NleCKjQ/WE9eYg9WY4w25O9w8wKP4rzNZFmUcUg==
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-merge@^4.2.1:
  version "4.2.2"
  resolved "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.2.2.tgz"
  integrity sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g==
  dependencies:
    lodash "^4.17.15"

webpack-merge@^5.7.3:
  version "5.8.0"
  resolved "https://registry.npmmirror.com/webpack-merge/-/webpack-merge-5.8.0.tgz"
  integrity sha512-/SaI7xY0831XwP6kzuwhKWVKDP9t1QY1h65lAFLbZqMPIuYcD9QAW4u9STIbU9kaJbPBB/geU/gLr1wDjOhQ+Q==
  dependencies:
    clone-deep "^4.0.1"
    wildcard "^2.0.0"

webpack-sources@^1.0.1, webpack-sources@^1.1.0, webpack-sources@^1.4.0, webpack-sources@^1.4.1:
  version "1.4.3"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz"
  integrity sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

"webpack@^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^4.0.0 || ^5.0.0":
  version "4.43.0"
  resolved "https://registry.npmjs.org/webpack/-/webpack-4.43.0.tgz"
  integrity sha512-GW1LjnPipFW2Y78OOab8NJlCflB7EFskMih2AHdvjbpKMeDJqEgSx24cXXXiPS65+WSwVyxtDsJH6jGX2czy+g==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.1.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.6.1"
    webpack-sources "^1.4.1"

"webpack@^1.0.0 || ^2.0.0 || ^3.0.0", "webpack@^1.9 || ^2 || ^2.1.0-beta || ^2.2.0-rc || ^3.0.0", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^2.2.0 || ^3.0.0", "webpack@^3.0.0 || ^4.0.0", "webpack@^3.0.0 || ^4.1.0 || ^5.0.0-0", webpack@^3.1.0, webpack@^4.0.0, "webpack@^4.3.0 || ^5.0.0", "webpack@^4.36.0 || ^5.0.0", webpack@^4.4.0, webpack@^4.46.0, "webpack@>=2.0.0 <5.0.0", webpack@>=4.0.0, "webpack@>=4.0.0 < 6.0.0", "webpack@2 || 3 || 4", "webpack@4.x.x || 5.x.x":
  version "4.46.0"
  resolved "https://registry.npmmirror.com/webpack/-/webpack-4.46.0.tgz"
  integrity sha512-6jJuJjg8znb/xRItk7bkT0+Q7AHCYjjFnvKIWQPkNIOyRqoCGvkOs0ipeQzrqz4l5FtN5ZI/ukEHroeX/o1/5Q==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.5.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.7.4"
    webpack-sources "^1.4.1"

webpack@^3.6.0:
  version "3.12.0"
  resolved "https://registry.npmmirror.com/webpack/-/webpack-3.12.0.tgz"
  integrity sha512-Sw7MdIIOv/nkzPzee4o0EdvCuPmxT98+vVpIvwtcwcF1Q4SDSNp92vwcKc4REe7NItH9f1S4ra9FuQ7yuYZ8bQ==
  dependencies:
    acorn "^5.0.0"
    acorn-dynamic-import "^2.0.0"
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"
    async "^2.1.2"
    enhanced-resolve "^3.4.0"
    escope "^3.6.0"
    interpret "^1.0.0"
    json-loader "^0.5.4"
    json5 "^0.5.1"
    loader-runner "^2.3.0"
    loader-utils "^1.1.0"
    memory-fs "~0.4.1"
    mkdirp "~0.5.0"
    node-libs-browser "^2.0.0"
    source-map "^0.5.3"
    supports-color "^4.2.1"
    tapable "^0.2.7"
    uglifyjs-webpack-plugin "^0.4.6"
    watchpack "^1.4.0"
    webpack-sources "^1.0.1"
    yargs "^8.0.2"

webpack@>=2:
  version "4.43.0"
  resolved "https://registry.npmjs.org/webpack/-/webpack-4.43.0.tgz"
  integrity sha512-GW1LjnPipFW2Y78OOab8NJlCflB7EFskMih2AHdvjbpKMeDJqEgSx24cXXXiPS65+WSwVyxtDsJH6jGX2czy+g==
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.1.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.6.1"
    webpack-sources "^1.4.1"

websocket-driver@>=0.5.1:
  version "0.7.4"
  resolved "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz"
  integrity sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-driver@0.6.5:
  version "0.6.5"
  resolved "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.6.5.tgz"
  integrity sha1-XLJVbOuF9Dc8bYI4qmkchFThOjY=
  dependencies:
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  integrity sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==

webwackify@0.1.6:
  version "0.1.6"
  resolved "https://registry.npmjs.org/webwackify/-/webwackify-0.1.6.tgz"
  integrity sha512-pGcw1T3HpNnM/UTRQqqRkkkzythSLts05mB+7Gr00B+0VbL0m39dFL5g20rSIEUt9Wrpw+/8k+snxRlUFHhcqA==

whet.extend@~0.9.9:
  version "0.9.9"
  resolved "https://registry.npmjs.org/whet.extend/-/whet.extend-0.9.9.tgz"
  integrity sha1-+HfVv2SMl+WqVC+twW1qJZucEaE=

which-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/which-module/-/which-module-1.0.0.tgz"
  integrity sha1-u6Y8qGGUiZT/MHc2CJ47lgJsKk8=

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/which-module/-/which-module-2.0.0.tgz"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@^1.2.9, which@1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.5"
  resolved "https://registry.npmmirror.com/wide-align/-/wide-align-1.1.5.tgz"
  integrity sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

wildcard@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/wildcard/-/wildcard-2.0.0.tgz"
  integrity sha512-JcKqAHLPxcdb9KM49dufGXn2x3ssnfjbcaQdLlfZsL9rH9wgDQjUtDxbo8NE0F6SFvydeu1VhZe7hZuHsB2/pw==

window-size@0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz"
  integrity sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=

wolfy87-eventemitter@^5.1.0:
  version "5.2.9"
  resolved "https://registry.npmjs.org/wolfy87-eventemitter/-/wolfy87-eventemitter-5.2.9.tgz"
  integrity sha512-P+6vtWyuDw+MB01X7UeF8TaHBvbCovf4HPEMF/SV7BdDc1SMTiBy13SRD71lQh4ExFTG1d/WNzDGDCyOKSMblw==

wolfy87-eventemitter@~5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/wolfy87-eventemitter/-/wolfy87-eventemitter-5.1.0.tgz"
  integrity sha1-NcGsDdGsDBXjXZgVCPwiCEoToBE=

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
  integrity sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==

wordwrap@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz"
  integrity sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/worker-farm/-/worker-farm-1.7.0.tgz"
  integrity sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw==
  dependencies:
    errno "~0.1.7"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz"
  integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-5.1.0.tgz"
  integrity sha512-QC1/iN/2/RPVJ5jYK8BGttj5z83LmSKmvbvrXPNCLZSEb32KKVDJDl/MOt2N01qU2H/FkzEa9PKto1BqDjtd7Q==
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/write/-/write-0.2.1.tgz"
  integrity sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=
  dependencies:
    mkdirp "^0.5.1"

write@1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/write/-/write-1.0.3.tgz"
  integrity sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig==
  dependencies:
    mkdirp "^0.5.1"

ws@^6.0.0, ws@^6.2.1:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ws/-/ws-6.2.1.tgz"
  integrity sha512-GIyAXC2cB7LjvpgMt9EKS2ldqr0MTrORaleiOno6TweZ6r3TKtoFQWay/2PceJ3RuBasOHzXNn5Lrw1X0bEjqA==
  dependencies:
    async-limiter "~1.0.0"

"xe-utils@>= 2.4.0 < 3", xe-utils@2.4.8:
  version "2.4.8"
  resolved "https://registry.npm.taobao.org/xe-utils/download/xe-utils-2.4.8.tgz"
  integrity sha1-Dv2jyoH2tV9oqKMeJ2+xfaWcmLM=

xhr@2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/xhr/-/xhr-2.4.0.tgz"
  integrity sha512-TUbBsdAuJbX8olk9hsDwGK8P1ri1XlV+PdEWkYw+HQQbpkiBR8PLgD1F3kQDPBs9l4Px34hP9rCYAZOCCAENbw==
  dependencies:
    global "~4.3.0"
    is-function "^1.0.1"
    parse-headers "^2.0.0"
    xtend "^4.0.0"

xregexp@^4.2.4:
  version "4.3.0"
  resolved "https://registry.npmjs.org/xregexp/-/xregexp-4.3.0.tgz"
  integrity sha512-7jXDIFXh5yJ/orPn4SXjuVrWWoi4Cr8jfV1eHv9CixKSbU+jY4mxfrBwAuDvupPNKpMUY+FeIqsVw/JLT9+B8g==
  dependencies:
    "@babel/runtime-corejs3" "^7.8.3"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==

y18n@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/y18n/-/y18n-3.2.1.tgz"
  integrity sha1-bRX7qITAhnnA136I53WegR4H+kE=

y18n@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/y18n/-/y18n-4.0.0.tgz"
  integrity sha512-r9S/ZyXu/Xu9q1tYlpsLIsa3EeLXXk0VwlxqTcFRfg9EhMW+17kbt9G0NrgCmhGb5vT2hyhJZLfDGx+7+5Uj/w==

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-13.1.2.tgz"
  integrity sha512-3lbsNRf/j+A4QuSZfDRA7HRSfWrzO0YjqTJd5kjAq37Zep1CEgaYmrH9Q3GwPiB9cHyd1Y1UwggGhJGoxipbzg==
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz"
  integrity sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^4.2.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-4.2.1.tgz"
  integrity sha1-KczqwNxPA8bIe0qfIX3RjJ90hxw=
  dependencies:
    camelcase "^3.0.0"

yargs-parser@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-7.0.0.tgz"
  integrity sha512-WhzC+xgstid9MbVUktco/bf+KJG+Uu6vMX0LN1sLJvwmbCQVxb4D8LzogobonKycNasCZLdOzTAk1SK7+K7swg==
  dependencies:
    camelcase "^4.1.0"

yargs@^13.3.2:
  version "13.3.2"
  resolved "https://registry.npmmirror.com/yargs/-/yargs-13.3.2.tgz"
  integrity sha512-AX3Zw5iPruN5ie6xGRIDgqkT+ZhnRlZMLMHAs8tg7nRruy2Nb+i5o9bwghAogtM08q1dpr2LVoS8KSTMYpWXUw==
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^15.0.0:
  version "15.4.0"
  resolved "https://registry.npmjs.org/yargs/-/yargs-15.4.0.tgz"
  integrity sha512-D3fRFnZwLWp8jVAAhPZBsmeIHY8tTsb8ItV9KaAaopmC6wde2u6Yw29JBIZHXw14kgkRnYmDgmQU4FVMDlIsWw==
  dependencies:
    cliui "^6.0.0"
    decamelize "^3.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^8.0.2:
  version "8.0.2"
  resolved "https://registry.npmmirror.com/yargs/-/yargs-8.0.2.tgz"
  integrity sha512-3RiZrpLpjrzIAKgGdPktBcMP/eG5bDFlkI+PHle1qwzyVXyDQL+pD/eZaMoOOO0Y7LLBfjpucObuUm/icvbpKQ==
  dependencies:
    camelcase "^4.1.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^2.0.0"
    read-pkg-up "^2.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1"
    yargs-parser "^7.0.0"

yargs@~3.10.0:
  version "3.10.0"
  resolved "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz"
  integrity sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

yargs@6.6.0:
  version "6.6.0"
  resolved "https://registry.npmjs.org/yargs/-/yargs-6.6.0.tgz"
  integrity sha1-eC7CHvQDNF+DCoCMo9UTr1YGUgg=
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "^4.2.0"

yorkie@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/yorkie/-/yorkie-2.0.0.tgz"
  integrity sha512-jcKpkthap6x63MB4TxwCyuIGkV0oYP/YRyuQU5UO0Yz/E/ZAu+653/uov+phdmO54n6BcvFRyyt0RRrWdN2mpw==
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"

zrender@5.4.3:
  version "5.4.3"
  resolved "https://registry.npmmirror.com/zrender/-/zrender-5.4.3.tgz"
  integrity sha512-DRUM4ZLnoaT0PBVvGBDO9oWIDBKFdAVieNWxWwK0niYzJCMwGchRk21/hsE+RKkIveH3XHCyvXcJDkgLVvfizQ==
  dependencies:
    tslib "2.3.0"
